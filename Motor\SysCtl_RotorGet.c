
//===========================================================================
//
// 文件名:  SysCtl_RotorGet.c
//
// 用途:
//
//===========================================================================
#include "SysCtl_AllHeaders.h"

float JSD;
float JSDtheta;
float Postion_Speed[400];
int Position_count;
int Position_count_flag=0;
extern int count;
extern float F_count;
extern float power_pf;
int limitDelta=0;
float resolver_angle = 0.0f;
//TypeRotorSpeedpara RotorSpeedclc;

/**
  * @brief 计算转子速度
  * @param  p: 转子速度结构体参数
  * @version 0.1
  */
void fnRotorSpeedclc(TypeRotorSpeedpara *p)
{
    const float SPEED_CALC_CONSTANT = 36.62109375f; //60*1/(4*4096*2*0.00005),一圈4096个plus,4倍频，中断采样周期T=0.00005s,速度采样时间Ts=2*T=2*0.00005,p->speedclc_countmax=2
   // const float SPEED_CALC_CONSTANT = 5.859375f; //60*1/(4096*50*0.00005),一圈4096个plus,中断采样周期T=50us,速度采样时间Ts=50*T=50*50us,p->speedclc_countmax=2
    /*读取编码器寄存器*/
    p->uThisPosition = TMR3->cval;
    p->positionflt = TMR3->cval;
   // TMR4->cval = 0;  //TMR4计数器清零
    //tmr_counter_value_set(TMR4, 0);
   RotorSpeedclc.rotorAngle = PI2-(RotorSpeedclc.uThisPosition - SynMotorVc.theta_preposition) * 0.000383495197f;
   if(RotorSpeedclc.rotorAngle >= PI2)
   {
       RotorSpeedclc.rotorAngle = 0;
   }
    
    //if(RotorSpeedclc.speedclc_count >= RotorSpeedclc.speedclc_countmax)//检测最大转速
    p->speedclc_count++;  //转速计算计数
    if(p->speedclc_count >= p->speedclc_countmax)//检测最大转速,p->speedclc_countmax=2,2个采样周期
    {
        p->uDeltaPosition = p->uThisPosition - p->uLastPosition;        //计算位置差值
        if(TMR3->ctrl1_bit.cnt_dir == 1)                               //根据转速方向做补偿,cnt_dir=1为减计数,为逆时针旋转
        {
            p->speed_direction = 1;                                     //记录转速方向
            if(p->uDeltaPosition <= 0)
            {
                p->uDeltaPosition = -p->uDeltaPosition;
            }
            else
            {
               p->uDeltaPosition = 16384-p->uDeltaPosition;
            }
        }
        else   //cnt_dir=0,增计数，为顺时针旋转
        {
            p->speed_direction = 0;
            if(p->uDeltaPosition >= 0)
            {
                // p->uDeltaPosition -= 4096;
                p->uDeltaPosition = -p->uDeltaPosition;               
            }
            else
            {
                p->uDeltaPosition = -(p->uDeltaPosition + 16384);
            }
        }
  
        //p->speed_Mrpm = 60 * p->uDeltaPosition * 0.0244140625;            //M法测速
        p->speed_Mrpm = p->uDeltaPosition * SPEED_CALC_CONSTANT; //未滤波采样时刻(100us)瞬时速度 
         if (p->speedave_count >= 5)  //速度采样均值滤波，连续采样5次，中断周期50us,2个中断周期采集一次，速度采样周期2*5*0.05ms=0.5ms,
         {
             p->speedave_count = 0;
             p->speedback_flt = p->uSumDelta * SPEED_CALC_CONSTANT* 0.2f;  //速度均值滤波，5个子采样周期
             p->uSumDelta = 0;
        }
        else
        {
            p->speedave_count++;
            p->uSumDelta +=  p->uDeltaPosition;
        }

        p->uLastLastLastPosition = p->uLastLastPosition;
        p->uLastLastPosition = p->uLastPosition;
        p->uLastPosition = p->uThisPosition;

        p->speedclc_count = 0;
    }
        p->speed_rpm_last = p->speed_rpm;
        p->speed_rpm = p->speed_Mrpm; 


}

/**
  * @brief 获取电角度
  *  @note  旋变极对数与电机极对数相等，故旋变角度等于电角度。
  * @return float 单位(rad)
  * @version 0.1
  */
float GetElectricalAngle()
{
    
    float angle = 0.0f;
    //resolver_angle = Ad2s_GetAngle();
		resolver_angle =0.0f;
    if(resolver_angle >= SynMotorVc.theta_preposition)
    {
        angle = resolver_angle - SynMotorVc.theta_preposition;
    }
    else
    {
        angle = 4096.0f + resolver_angle - SynMotorVc.theta_preposition;
    }
    return (angle * 0.001533980788f);  //旋变角度分辨率12位2PI/4096;    
}

float rotor_cont = 0.0f;
/**
  * @brief 获取转子机械角度
  * @param  elec_angle: 电角度(rad)
  * @return float 
  * @version 0.1
  */
float GetRotorAngle(float elec_angle)
{
    float current_angle = 0.0f;
    float angle = 0.0f;
    static  float cycler_counter = 0.0f;
    float delta = 0.0f;
    static float last_angle = 0.0f;
    static uint16_t start = 0;
    current_angle = elec_angle; 
    if(start == 1)
    {
        delta = last_angle - current_angle;
        if (delta > PI)
    {
        cycler_counter++;
        if (cycler_counter >= 4.0f) // 4对极
        {
            cycler_counter = 0.0f;
        }
    }
    else if (delta < -PI)
    {
        cycler_counter --;
        if (cycler_counter <= -4.0f)
        {
            cycler_counter = 0.0f;
        }
        
    }
    rotor_cont = cycler_counter;
    last_angle = current_angle;
    angle = current_angle * 0.25f + cycler_counter * 1.570796327f; // pi/2 ≈ 1.570796327
    if (angle >= PI2)
    {
        angle = angle - PI2;
    }
    if (angle < 0)
    {
        angle = PI2 + angle; 
    }
    
    }
    else   
    {
        start = 1;
    }
    return angle;
}

/**
  * @brief  获取转子速度
  * @param  angle: 转子角度(rad)
  * @return float 单位(rad/s)
  * @version 0.1
  */
float GetRotorSpeed(float angle)
{
    static float last_angle = 0.0f;
    float speed = 0.0f;
    float delta = 0.0f;
    delta = angle - last_angle;
    if(delta < -PI)  //正转零点
    {
        delta += PI2;
    }
    if(delta > PI)  //反转过零点
    {
        delta -= PI2;
    }
    speed = delta ; // 190985.9317 = 1/50e-6*60/(2*pi)/4 ; 47746.48293f
    last_angle = angle;
    return speed;
}


//===========================================================================
// No more.
//===========================================================================

/*
 * SysCtl_RotorGet.c
 *
 *  Created on: 2018年10月11日
 *      Author: Hiter
 */




