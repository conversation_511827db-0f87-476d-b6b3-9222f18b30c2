/**********************************************************
 * @file     Sys_TimerEvent.c
 * @brief    系统定时器事件驱动实现
 * <AUTHOR>
 * @date     2025-01-20
 * @version  V2.0.0
 *
 * 设计与功能:
 * ----------------------------------------------------------
 * 本模块实现了一个基于500us基准定时的标志轮询系统。主要功能包括：
 * 1. 基于500us硬件定时器中断的时间基准
 * 2. 提供1ms、1s、1min多级时间标志
 * 3. 提供系统运行时间(毫秒级)计数
 * 技术实现:
 * ----------------------------------------------------------
 * 1. 时间管理: 采用分级计数方式，从500us基准累加生成各级时间标志
 * 2. 标志轮询: 主循环中轮询不同时间标志进行任务处理
 * 使用方法:
 * ----------------------------------------------------------
 * 1. 初始化: TimerEvent_Init()
 * 2. 在硬件定时器中断(500us)中调用: TimerEvent_Handler()
 * 3. 在主循环中轮询标志: if(gTimerFlag.flag_1ms) { ... }
 * 4. 获取系统时间: Get_Runtime_Ms()
 * 注意事项:
 * ----------------------------------------------------------
 * 1. 使用完标志后需要手动清零
 * 2. 中断处理函数应尽量简短
 **********************************************************/

#include "Sys_TimerEvent.h"
#include <string.h>

// 全局变量定义
TimerFlag_t gTimerFlag = {0};
static TimerCount_t sTimerCount = {0};

volatile uint32_t sys_runtime_ms;

/**
 * @brief 定时器事件初始化
 */
void TimerEvent_Init(void)
{
    // 清空标志和计数器
    memset(&gTimerFlag, 0, sizeof(TimerFlag_t));
    memset(&sTimerCount, 0, sizeof(TimerCount_t));

    // 清零系统运行时间
    sys_runtime_ms = 0;
}


/**
 * @brief 定时器中断处理函数(500us调用一次)
 */
void TimerEvent_Handler(void)
{
    // 设置500us标志
    gTimerFlag.flag_500us = 1;

    // 1ms计数和标志
    if(++sTimerCount.cnt_1ms >= 2) {
        sys_runtime_ms++;
        sTimerCount.cnt_1ms = 0;
        gTimerFlag.flag_1ms = 1;

        // 1s计数和标志
        if(++sTimerCount.cnt_1s >= 1000) {
            sTimerCount.cnt_1s = 0;
            gTimerFlag.flag_1s = 1;

            // 1min计数和标志
            if(++sTimerCount.cnt_1min >= 60) {
                sTimerCount.cnt_1min = 0;
                gTimerFlag.flag_1min = 1;
            }
        }
    }
}

