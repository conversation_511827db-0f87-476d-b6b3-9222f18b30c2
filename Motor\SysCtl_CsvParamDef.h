
#ifndef SYSCTL_CSVPARAMDEF_H
#define SYSCTL_CSVPARAMDEF_H
#include "at32a403a.h"
extern uint16_t HMIBuffer[2000];

//#define pCsvParamHead		    ((float*)0x119000) // 必须定义为32位数指针，否则偏移量应乘以2。  是否应该 -
#define pCsvParamHead   (float*)&HMIBuffer[0]

//===== 系统控制字 =====
#define pCsvParamSysCtlWord          ((uint32_t*) (pCsvParamHead + 268))  //系统控制字

//#define pCsvParamDebugMD             ((float*) (pCsvParamHead + 58))  //控制电调试

#define pCsvParaSampleOffset       ((float*) (pCsvParamHead + 463))  //零漂设定值起始地址
 
#define pCsvParamOCPRO              ((float*) (pCsvParamHead + 140))  //瞬时过流 保护值
#define pCsvParaStartMod            ((float*) (pCsvParamHead + 289))  //启动方式

//=====载波频率============
#define pCsvCarrierFre				((float*) (pCsvParamHead + 383))  //开关频率，也称DSP主中断倍数
#define pCsvChopwaveFre				((float*) (pCsvParamHead + 384))  //斩波频率
#define pCsvChopDutyCycle			((float*) (pCsvParamHead + 385))  //载波占空比
#define pCsvARMInterMultiple		((float*) (pCsvParamHead + 386))  //ARM主中断倍数
#define pCsvDspFiberChopEnable		((float*) (pCsvParamHead + 387))  //光纤斩波使能

//=======VF曲线参数========
#define pCsvParamFreqStart            ((float*) (pCsvParamHead + 394))  //始动频率
#define pCsvParamVF_VoltMin           ((float*) (pCsvParamHead + 368))  //最低电压
#define pCsvParamVF_FreqPoint1        ((float*) (pCsvParamHead + 369))  //频率点1
#define pCsvParamVF_VoltPoint1        ((float*) (pCsvParamHead + 370))  //电压点1
#define pCsvParamVF_FreqPoint2        ((float*) (pCsvParamHead + 371))  //频率点2
#define pCsvParamVF_VoltPoint2        ((float*) (pCsvParamHead + 372))  //电压点2
#define pCsvParamVF_FreqMax           ((float*) (pCsvParamHead + 373))  //最高频率
#define pCsvParamVF_VoltMax           ((float*) (pCsvParamHead + 374))  //最高电压

//======采样整定系数===============
#define pCsvParamSample1K			((float*) (pCsvParamHead + 482))
#define pCsvParamSample1B 			((float*) (pCsvParamHead + 483))
#define pCsvParamSample2K			((float*) (pCsvParamHead + 484))
#define pCsvParamSample2B 			((float*) (pCsvParamHead + 485))
#define pCsvParamSample3K			((float*) (pCsvParamHead + 486))
#define pCsvParamSample3B 			((float*) (pCsvParamHead + 487))
#define pCsvParamSample4K			((float*) (pCsvParamHead + 488))
#define pCsvParamSample4B 			((float*) (pCsvParamHead + 489))
#define pCsvParamSample5K			((float*) (pCsvParamHead + 490))
#define pCsvParamSample5B 			((float*) (pCsvParamHead + 491))
#define pCsvParamSample6K			((float*) (pCsvParamHead + 492))
#define pCsvParamSample6B 			((float*) (pCsvParamHead + 493))

//===== 额定参数 =====
//#define pCsvParamMotorRatedVoltage   ((float*) (pCsvParamHead + 450))  //电机额定电压                                 
//#define pCsvParamMotorRatedCurrent   ((float*) (pCsvParamHead + 451))  //电机额定电流                                 
//#define pCsvParamMotorRatedFre       ((float*) (pCsvParamHead + 452))  //电机额定频率                                 
//#define pCsvParamMotorRatedPower     ((float*) (pCsvParamHead + 453))  //电机额定功率                                 
//#define pCsvParamMotorRateSpeed      ((float*) (pCsvParamHead + 454))  //电机额定转速                                 
//#define pCsvParamMotorPoleNum        ((float*) (pCsvParamHead + 455))  //电机极对数                                  
//#define pCsvParamMotorRatedSlip      ((float*) (pCsvParamHead + 456))  //电机额定转差率        
#define pCsvParamVFDInputVolatge     ((float*) (pCsvParamHead + 457))  //变频器额定输入电压                               
#define pCsvParamVFDInputCurrent     ((float*) (pCsvParamHead + 458))  //变频器额定输入电流                           
#define pCsvParamVFDRatedPower       ((float*) (pCsvParamHead + 459))  //变频器额定功率                               
#define pCsvParamVFDInputFre         ((float*) (pCsvParamHead + 460))  //变频器额定输入频率                           
#define pCsvParamVFDOutputVoltage    ((float*) (pCsvParamHead + 461))  //变频器额定输出电压                           
#define pCsvParamVFDOutputCurrent    ((float*) (pCsvParamHead + 462))  //变频器额定输出电流 

//#define pCsvParaConRunType           ((float*) (pCsvParamHead + 274))  //变频器控制方式
//#define pCsvParaVFDLoadType          ((float*) (pCsvParamHead + 275))  //负载类型
//#define pCsvParaConStrategy          ((float*) (pCsvParamHead + 276))  //控制策略

#define pCsvParamDACH1 				 ((float*) (pCsvParamHead + 741))
#define pCsvParamDACH2				 ((float*) (pCsvParamHead + 746))

#define pCsvParamVol_ManualSet1       ((float*) (pCsvParamHead + 20))  //手动设置逆变侧电压上限值
#define pCsvParamVol_ManualSet2       ((float*) (pCsvParamHead + 111))  //手动设置逆变侧电压上限值
#define pCsvParamPWMRectifier_Enable       ((float*) (pCsvParamHead + 21))  //PWM整流使能
#define pCsvParamPWMInverter_Enable       ((float*) (pCsvParamHead + 22))  //并网逆变使能
//PWM整流参数
#define pCsvParamVdcBus_Ref          ((float*) (pCsvParamHead + 23))  //母线电压参考值
#define pCsvParamVdcBus_Ref_Inv          ((float*) (pCsvParamHead + 24))  //母线电压参考值倒数
#define pCsvParamVdcBusControl_Kp          ((float*) (pCsvParamHead + 25))  //母线电压外环kp
#define pCsvParamVdcBusControl_Ki          ((float*) (pCsvParamHead + 26))  //母线电压外环ki
#define pCsvParamVdcBusControl_PIOutMax          ((float*) (pCsvParamHead + 27))  //母线电压外环输出PI最大限幅
#define pCsvParamCurrentControl_Kp          ((float*) (pCsvParamHead + 28))  //母线电压外环kp
#define pCsvParamCurrentControl_Ki          ((float*) (pCsvParamHead + 29))  //母线电压外环ki
#define pCsvParamCurrentControl_PIOutMax          ((float*) (pCsvParamHead + 30))  //电流内环输出PI最大限幅
//PWM并网逆变参数
#define pCsvParamP_Ref          ((float*) (pCsvParamHead + 31))  //有功功率参考值
#define pCsvParamQ_Ref          ((float*) (pCsvParamHead + 32))  //无功功率参考值
#define pCsvParamVdcBus          ((float*) (pCsvParamHead + 33))  //母线电压
#define pCsvParamVdcBus_Inv          ((float*) (pCsvParamHead + 34))  //母线电压倒数
#define pCsvParamCurrentControl_Inv_Kp          ((float*) (pCsvParamHead + 35))  //母线电压外环kp
#define pCsvParamCurrentControl_Inv_Ki          ((float*) (pCsvParamHead + 36))  //母线电压外环ki
#define pCsvParamCurrentControl_Inv_PIOutMax          ((float*) (pCsvParamHead + 37))  //电流内环输出PI最大限幅
#define pCsvParamCurrentControl_Inv_CurrentRef_Limit    ((float*) (pCsvParamHead + 38)) //电流给定限幅值
#define pCsvParamLoadType_Inv    ((float*) (pCsvParamHead + 39)) //负载类型
#define pCsvParamI_Ref    ((float*) (pCsvParamHead + 40)) //电流参考值
#define pCsvParamR_Ref    ((float*) (pCsvParamHead + 41)) //电阻参考值

#define pCsvParamM_Open    ((float*) (pCsvParamHead + 48)) //开环调制比
#define pCsvParamF_Output    ((float*) (pCsvParamHead + 49)) //输出频率
#define pCsvParamH    ((float*) (pCsvParamHead + 50)) //前馈系数
#define pCsvParamOpen_Loop    ((float*) (pCsvParamHead + 51)) //前馈系数
#define pCsvParamVref    ((float*) (pCsvParamHead + 52)) //电压给定值

//滤波器参数
#define pCsvParamGirdVoltage_FilterFreq          ((float*) (pCsvParamHead + 42))  //电网dq轴电压滤波参数
#define pCsvParamCurrentControl_FilterFreq          ((float*) (pCsvParamHead + 43))  //d、q轴电流环滤波参数
//锁相环参数
#define pCsvParamPLLControl_Kp          ((float*) (pCsvParamHead + 44))  //母线电压外环kp
#define pCsvParamPLLControl_Ki          ((float*) (pCsvParamHead + 45))  //母线电压外环ki
#define pCsvParamPLLControl_PIOutMax          ((float*) (pCsvParamHead + 46))  //母线电压外环输出PI最大限幅
//控制参数
#define pCsvParamPWMControl_Tsample          ((float*) (pCsvParamHead + 47))  //PWM控制周期
//#define pCsvParamSamDCVoltage        ((float*) (pCsvParamHead + 473))  //直流电压采样DSP 3V对应最大母线电压多少V
//#define pCsvParamSamOutCurrent       ((float*) (pCsvParamHead + 474))  //交流电流DSP 3V对应电流多少A
//#define pCsvParamSamOutVoltage       ((float*) (pCsvParamHead + 475))  //交流电压DSP 3V对应电压多少V
//#define pCsvParamSamNTC1             ((float*) (pCsvParamHead + 476))  //NTC1 DSP 3V对应热敏电阻阻止多少欧姆
//#define pCsvParamSamNTC2             ((float*) (pCsvParamHead + 477))  //NTC2 DSP 3V对应热敏电阻阻止多少欧姆

#define pCsvParamOCPrtVal  		((float*) (pCsvParamHead + 760)) //电流瞬时值保护
#define pStateNew				((uint32_t*) (pCsvParamHead + 261))

//故障波形通道设置
#define pCsvParamFWCh_A         ((float*)(pCsvParamHead + 628))
#define pCsvParamFWCh_B         ((float*)(pCsvParamHead + 629))
#define pCsvParamFWCh_C         ((float*)(pCsvParamHead + 630))
#define pCsvParamFWCh_D         ((float*)(pCsvParamHead + 631))
#define pCsvParamFWCh_E         ((float*)(pCsvParamHead + 632))
#define pCsvParamFWCh_F         ((float*)(pCsvParamHead + 633))
#define pCsvParamFWCh_G         ((float*)(pCsvParamHead + 634))
#define pCsvParamFWCh_H         ((float*)(pCsvParamHead + 635))
#define pCsvParamFWCh_I         ((float*)(pCsvParamHead + 636))
#define pCsvParamFWCh_J         ((float*)(pCsvParamHead + 637))
#define pCsvParamFWCh_K         ((float*)(pCsvParamHead + 638))
#define pCsvParamFWCh_L         ((float*)(pCsvParamHead + 639))
#define pCsvParamFWCh_M         ((float*)(pCsvParamHead + 640))
#define pCsvParamFWCh_N         ((float*)(pCsvParamHead + 641))
#define pCsvParamFWCh_O         ((float*)(pCsvParamHead + 642))
#define pCsvParamFWCh_P         ((float*)(pCsvParamHead + 643))


//========================永磁同步电机矢量控制算法=================================================
//#define pCsvParamDBT                ((float*) (pCsvParamHead + 56)) //死区补偿使能
//#define pCsvParamDBEN               ((float*) (pCsvParamHead + 57)) //死区补偿使能
#define pCsvParamDebugstep          ((float*) (pCsvParamHead + 58))  //iq或电压给定步长
#define pCsvParamChangeMarsF_ref    ((float*) (pCsvParamHead + 59)) //切Mars时频率
//#define pCsvParamPreid_ref          ((float*) (pCsvParamHead + 60)) //预定位id给定电流
//#define pCsvParamSynLsq              ((float*) (pCsvParamHead + 61)) //定子电感
//#define pCsvParamSynRs              ((float*) (pCsvParamHead + 62)) //定子电阻
//#define pCsvParamSynLsd              ((float*) (pCsvParamHead + 63)) //定子电感
//#define pCsvParamSynFlux            ((float*) (pCsvParamHead + 64)) //转子磁链

//#define pCsvParamSynVcSpeedKp       ((float*) (pCsvParamHead + 65)) //转速环Kp
//#define pCsvParamSynVcSpeedKi       ((float*) (pCsvParamHead + 66)) //转速环Ki

//#define pCsvParamSynVcCurrentKp     ((float*) (pCsvParamHead + 67)) //电流环Kp
//#define pCsvParamSynVcCurrentKi     ((float*) (pCsvParamHead + 68)) //电流环Ki

#define pCsvParamSynVcVoltageKp     ((float*) (pCsvParamHead + 69)) //电压外环Kp
#define pCsvParamSynVcVoltageKi     ((float*) (pCsvParamHead + 70)) //电压外环Ki

#define pCsvParamSynVcMRASKp     ((float*) (pCsvParamHead + 71)) //转速估计环Kp
#define pCsvParamSynVcMRASKi     ((float*) (pCsvParamHead + 72)) //转速估计环Ki

//#define pCsvParamSynVcFWKp     ((float*) (pCsvParamHead + 73)) //弱磁环Kp
//#define pCsvParamSynVcFWKi     ((float*) (pCsvParamHead + 74)) //弱磁环Ki

//#define pCsvParamSynVcLoseSpeedValue     ((float*) (pCsvParamHead + 75)) //失速保护参数

//#define pCsvParamSynVcIqmax     ((float*) (pCsvParamHead + 76)) //转速环输出电流限幅值

//#define pCsvParamSynVcUqmax     ((float*) (pCsvParamHead + 77))  //电流环输出电压限幅

#define pCsvParamtheta_preposition     ((float*) (pCsvParamHead + 78)) //电流环输出电压限幅

#define pCsvParamwc     ((float*) (pCsvParamHead + 79)) //电流环输出电压限幅

#define pCsvParamIFiq     ((float*) (pCsvParamHead + 80)) //电流环输出电压限幅

//#define pCsvParamfeedforwordFlag     ((float*) (pCsvParamHead + 81)) //电流环输出电压限幅

#define pCsvParamIFFlag     ((float*) (pCsvParamHead + 82)) //电流环输出电压限幅

#define pCsvParamyudingTIME     ((float*) (pCsvParamHead + 83)) //预定位时间

//#define pCsvParameConTc     ((float*) (pCsvParamHead + 84)) //电流环输出电压限幅

//#define pCsvParamPosCompEN     ((float*) (pCsvParamHead + 85)) //电流环输出电压限幅

//#define pCsvParammarslpfw    ((float*) (pCsvParamHead + 86)) //电流环输出电压限幅

//#define pCsvParamCurrentLimit    ((float*) (pCsvParamHead + 87)) //速度环控制次数

//#define pCsvParamfluxidmin    ((float*) (pCsvParamHead + 88)) //弱磁限幅Id限幅

#define pCsvParamRuociXIshu    ((float*) (pCsvParamHead + 89)) //弱磁限幅Id限幅

#define pCsvParamFilter_udclen    ((float*) (pCsvParamHead + 90)) //调试时，直流电压使用真实电压还是给定电压

//#define pCsvParamGiveUdc    ((float*) (pCsvParamHead + 91)) //弱磁限幅Id限幅

//#define pCsvParamPrePositionEN    ((float*) (pCsvParamHead + 92)) //预定位使能

#define pCsvParamvvvfreStep    ((float*) (pCsvParamHead + 93)) //VVVF频率给定步长
//
#define pCsvParamSensorlessmode    ((float*) (pCsvParamHead + 94)) //无感起动模式

#define pCsvParamLOWVOLt    ((float*) (pCsvParamHead + 95)) //最低整流电压限制

#define pCsvParammarsQH    ((float*) (pCsvParamHead + 96)) //电感查表标志位

#define pCsvParamIfoward    ((float*) (pCsvParamHead + 97)) //电感查表标志位
// 分段pi参数输入
//#define pCsvParamSynVcSpeedKp1       ((float*) (pCsvParamHead + 98)) //转速环Kp
//#define pCsvParamSynVcSpeedKi1       ((float*) (pCsvParamHead + 99)) //转速环Ki

//#define pCsvParamSynVcCurrentKp1     ((float*) (pCsvParamHead + 100)) //电流环Kp
//#define pCsvParamSynVcCurrentKi1     ((float*) (pCsvParamHead + 101)) //电流环Ki

//#define pCsvParamSynVcSpeedKp2       ((float*) (pCsvParamHead + 102)) //转速环Kp
//#define pCsvParamSynVcSpeedKi2       ((float*) (pCsvParamHead + 103)) //转速环Ki

//#define pCsvParamSynVcCurrentKp2     ((float*) (pCsvParamHead + 104)) //电流环Kp
//#define pCsvParamSynVcCurrentKi2     ((float*) (pCsvParamHead + 105)) //电流环Ki
//#define pCsvParamSpeed_mid    ((float*) (pCsvParamHead + 106)) //切换转速点_中间转速
//#define pCsvParamSpeed_high    ((float*) (pCsvParamHead + 107)) //切换转速点_高转速

#define pCsvParamSynLaad              ((float*) (pCsvParamHead + 108)) //定义d轴主自感
#define pCsvParamSynLaaq              ((float*) (pCsvParamHead + 109)) //定义q轴主自感
#define pCsvParamSynLaal              ((float*) (pCsvParamHead + 110)) //定义漏感
#define pCsvParamSynopenflag              ((float*) (pCsvParamHead + 112)) //三相容错标志位
#define pCsvParamSynVcCurrentKpfault     ((float*) (pCsvParamHead + 113)) //电流环Kp 容错运行
#define pCsvParamSynVcCurrentKifault     ((float*) (pCsvParamHead + 114)) //电流环Ki 容错运行
#define pCsvParamSynVcCurrentKpfaultx     ((float*) (pCsvParamHead + 115)) //电流环Kp 容错运行
#define pCsvParamSynVcCurrentKifaultx     ((float*) (pCsvParamHead + 116)) //电流环Ki 容错运行
#define pCsvParamSynopenflagC2              ((float*) (pCsvParamHead + 117)) //三相容错标志位
#define pCsvParamMRAS_flag              ((float*) (pCsvParamHead + 118)) //三相容错标志位 MRAS电流滤波标志位
#define pCsvParamUdUqfiltar              ((float*) (pCsvParamHead + 119)) //三相容错标志位 MRAS电流滤波标志位 ud\uq电压滤波
#define pCsvParamcountresolver              ((float*) (pCsvParamHead + 120)) //旋变转速/角度滤波
#define pCsvParampowerpf              ((float*) (pCsvParamHead + 121)) //旋变转速/角度滤波
#define pCsvParammrasqie              ((float*) (pCsvParamHead + 122)) //切换MRAS使用电角速度
//=========矢量控制算法=======
//#define pCsvParamAsynRs           ((float*) (pCsvParamHead + 57)) //定子电阻
//#define pCsvParamAsynRr           ((float*) (pCsvParamHead + 58)) //转子电阻
//#define pCsvParamAsynLs           ((float*) (pCsvParamHead + 59)) //定子漏感
//#define pCsvParamAsynLr           ((float*) (pCsvParamHead + 60)) //转子漏感
//#define pCsvParamAsynLm           ((float*) (pCsvParamHead + 61)) //互感
//
//#define pCsvParamExcitCurrentST   ((float*) (pCsvParamHead + 33)) //励磁电流初始值
//#define pCsvParamExcitationCurrent   ((float*) (pCsvParamHead + 34)) //激励电流
//#define pCsvParamExcitationTime    ((float*) (pCsvParamHead + 35)) //激励时间
//
//
//#define pCsvParamPhrKp             ((float*) (pCsvParamHead + 39)) //转子磁链闭环KP
//#define pCsvParamPhrKi             ((float*) (pCsvParamHead + 40)) //转子磁链闭环Ki
//#define pCsvParamPhrLoopStartFreq  ((float*) (pCsvParamHead + 54)) //转子磁链闭环起始频率
//
//#define pCsvParamCNT_FR_VALUE      ((float*) (pCsvParamHead + 53)) //转矩计算周期
//#define pCsvParamSLVCSpeedKpMAX      ((float*) (pCsvParamHead + 29)) //速度环KP最大值
//#define pCsvParamSLVCSpeedKpMIN      ((float*) (pCsvParamHead + 28)) //速度环KP最小值
//#define pCsvParamSpeedLoopKi       ((float*) (pCsvParamHead + 30)) //速度环Ki
//#define pCsvParamSpeedLOutMin       ((float*) (pCsvParamHead + 31)) //速度环输出最小值
//#define pCsvParamSpeedLOutMax       ((float*) (pCsvParamHead + 32)) //速度环输出最大值
//#define pCsvParamCurrentKp          ((float*) (pCsvParamHead + 36)) //电流环Kp
//#define pCsvParamCurrentKi          ((float*) (pCsvParamHead + 37)) //电流环Ki
//
//#define pCsvParamRotorFluxGiven     ((float*) (pCsvParamHead + 38)) //磁链给定值
//#define pCsvParamRotorFluxOutMin     ((float*) (pCsvParamHead + 41)) //磁链输出最小值
//#define pCsvParamRotorFluxOutMax     ((float*) (pCsvParamHead + 42)) //磁链输出最大值
//
//#define pCsvParamSpeedEstKp     ((float*) (pCsvParamHead + 43)) //估计环KP
//#define pCsvParamSpeedEstKiMIN     ((float*) (pCsvParamHead + 44)) //估计环KI最小值
//#define pCsvParamSpeedEstKiMID     ((float*) (pCsvParamHead + 45)) //估计环Ki中间值
//#define pCsvParamSpeedEstKiMAX     ((float*) (pCsvParamHead + 46)) //估计环Ki最大值
//
//#define pCsvParamSpeedEstFrRefFly  ((float*) (pCsvParamHead + 47)) //飞车启动初始频率
//#define pCsvParamSpeedEstKpFly     ((float*) (pCsvParamHead + 48)) //飞车启动估计Kp
//#define pCsvParamSpeedEstKiMINFly  ((float*) (pCsvParamHead + 49)) //飞车启动估计ki最小
//#define pCsvParamSpeedEstKiMIDFly  ((float*) (pCsvParamHead + 50)) //飞车启动估计ki中间
//#define pCsvParamSpeedEstKiMAXFly  ((float*) (pCsvParamHead + 51)) //飞车启动估计KI最大
#endif

//============================                                                                                                                                                                                                                                                                                                                       
// No more.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
