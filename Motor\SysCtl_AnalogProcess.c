//===========================================================================
//
// 文件名:  SysCtl_AnalogProcess.c
//
// 用途:   	模拟量处理
//
//===========================================================================
#include "SysCtl_AllHeaders.h"

#define LowVoltTest  0

#if LowVoltTest
float fUdcTest = 400.0;
#endif
float error_A=0.0;
float error_B=0.0;
float error_C=0.0;

uint16_t uCEFlag = 0;


/**
  * @brief 电流AD采样处理
  * @param  p: 结构体指针TypeAnalogInput*
  * @version 0.1
  */
#define ADC_TO_CURRENT_FACTOR  0.38672f  // 3.3/4096 * 400.0f
#define CURRENT_OFFSET        -1000.0f    // (-2.5f + 0.005f) * 400.0f
void fnAISample(TypeAnalogInput *p)
{
	// 1. 直接读取ADC数据
	p->iSysADResult[0] = (uint16_t)(ADC1->pdt1_bit.pdt1); // Iu1
	p->iSysADResult[1] = (uint16_t)(ADC1->pdt2_bit.pdt2); // Iv1
	p->iSysADResult[2] = (uint16_t)(ADC1->pdt3_bit.pdt3); // Iw1

    // 2. 处理所有通道
    p->fADResult[0] = p->iSysADResult[0] * ADC_TO_CURRENT_FACTOR + CURRENT_OFFSET-12.5f;
    p->fADResult[1] = p->iSysADResult[1] * ADC_TO_CURRENT_FACTOR + CURRENT_OFFSET-12.5f;
    p->fADResult[2] = p->iSysADResult[2] * ADC_TO_CURRENT_FACTOR + CURRENT_OFFSET-12.5f;
    
    // 4. 零偏校准
    if (SysSampOffset.uSampleOffsetEn == TRUE)
    {
        SysSampOffset.pfnSysOffsetParameterCal(&SysSampOffset);
    }
}

/**
  * @brief 采样通道整定参数设定
  * @version 0.1
  */
void fnParaUpdateSysSamScaParameter(void)
{

 uint16_t uLoopNum;

// SysSamScaParameter.fSysSamOutCurrent = *pCsvParamSamOutCurrent;//交流电流DSP 3V对应电流多少A
//
// SysSamScaParameter.fSysSamNTC1 = *pCsvParamSamNTC1;//NTC1 DSP 3V对应热敏电阻阻止多少欧姆
// SysSamScaParameter.fSysSamNTC2 = *pCsvParamSamNTC2;//NTC2 DSP 3V对应热敏电阻阻止多少欧姆
//
// SysSamScaParameter.fSysSamOutVoltage = *pCsvParamSamOutVoltage;//交流电压DSP 3V对应电压多少V
//
// SysSamScaParameter.fSysSamDCVoltage = *pCsvParamSamDCVoltage;//直流电压采样DSP 3V对应最大母线电压多少V


 if(SysBaseValue.fCurrBaseValue == 0.0)//为避免分母为0
  SysSamScaParameter.fSysSamValue[0] = 0.0;
 else
  SysSamScaParameter.fSysSamValue[0] = SysSamScaParameter.fSysSamOutCurrent /SysBaseValue.fCurrBaseValue;

 SysSamScaParameter.fSysSamValue[1] = SysSamScaParameter.fSysSamValue[0];

 if((SysBaseValue.fVoltBaseValue) == 0.0)//为避免分母为0
  SysSamScaParameter.fSysSamValue[2] = 0.0;
 else
  SysSamScaParameter.fSysSamValue[2] =  SysSamScaParameter.fSysSamOutVoltage / SysBaseValue.fVoltBaseValue;   //

 SysSamScaParameter.fSysSamValue[4] = SysSamScaParameter.fSysSamValue[3] = SysSamScaParameter.fSysSamValue[2];


 SysSamScaParameter.fSysSamValue[5] = 0.5 * SysSamScaParameter.fSysSamDCVoltage;//DSP 0~3V对应母线电压0~最大值

 for(uLoopNum = 0;uLoopNum < 8;uLoopNum ++)
 {
  //fnVarCopyToRam(sInComm.DSP_RAM,&SysSamScaParameter.fSysSamValue[uLoopNum],(BaseAddr_SysSamVal + (uLoopNum << 1)));// 
 }

}

void fnSysBaseValueCal(TypeSysBaseValue *p)
{
 float Temp1,Temp2;//
                     //
 p->fVoltBaseValueGrid = SQRT2By3 * SysRatedParameter.fVFDInputVolatge;	                 	                 
 //====电压基值====
 p->fVoltBaseValue = SQRT2By3 * SysRatedParameter.fMotorRatedVoltage;
 	 	 
 //====转速基值，弧度====
 p->fSpeedBaseValue = PI2 * SysRatedParameter.fMotorRatedFre;
 	 	 
 //====电流基值====                                                       
 p->fCurrBaseValue = SQRT2By3 * SysRatedParameter.fMotorRatedPower * 1000.0 / SysRatedParameter.fMotorRatedVoltage;		
  	 
 //====系统角度基值====
 p->fThetaBaseValue = PI2;	 
 	 
 //====功率基值(3/2) * Vbase * Ibase====
 p->fPowerBaseValue = 1.5 * p->fVoltBaseValue * p->fCurrBaseValue;
  	 
 //====系统阻抗基值Ubase/Ibase====
 p->fZBaseValue = p->fVoltBaseValue/p->fCurrBaseValue;
	 	 
 //====系统电感基值====
 p->fLBaseValue = p->fVoltBaseValue/(p->fSpeedBaseValue * p->fCurrBaseValue);
	 	 
 //====系统磁链基值====
 p->fPhirBaseValue = p->fVoltBaseValue/p->fSpeedBaseValue;
	 	 
 //====机械角度基值====
 p->fOmegBaseValue = SysRatedParameter.fMotorRateSpeed * KOmegBase;
	 
 //====系统转矩基值====
 p->fTeBaseValue = p->fPowerBaseValue/p->fOmegBaseValue;
	 
 //====系统转动惯量基值====
 Temp1 = POW2(SysRatedParameter.fMotorPoleNum);//
                                               //	 
 Temp2 = POW3(p->fSpeedBaseValue);//
                                  //
 p->fJBaseValue = (Temp1 * p->fPowerBaseValue)/Temp2;
	 
 //====系统摩擦系数基值====
 p->fFBaseValue = p->fTeBaseValue/p->fOmegBaseValue;
}


/**
  * @brief 零漂初始化
  * @param  p: 结构体指针TypeSysOffsetParameter*
  * @version 0.1
  */
void fnSysOffsetInit(TypeSysOffsetParameter *p)
{
	uint16_t i;

	p->uOffsetCounter = 0;//

	for(i=0;i<4;i++)
	{
		p->fOffsetValue[i] = *(pOffsetRam+i);
		p->fOffsetAdd[i] = 0;
	}

	for(i=0;i<4;i++)
	{
		p->fK[i] = 0.0;
		p->fB[i] = 0.0;
	}
	
	SysSampOffset.fK[0] = 100;
	SysSampOffset.fK[1] = 100;
	SysSampOffset.fK[2] = 100;
	SysSampOffset.fK[3] = 400;
	SysSampOffset.fK[4] = 10;
	SysSampOffset.fK[5] = 400;

	SysSampOffset.fB[0] = 750;
	SysSampOffset.fB[1] = 750;
	SysSampOffset.fB[2] = 750;
	SysSampOffset.fB[3] = 750;
	SysSampOffset.fB[4] = 50;
}

/**
  * @brief 零漂计算
  * @param  p: 结构体指针TypeSysOffsetParameter*
  * @version 0.1
  */
void fnSysOffsetParameterCal(TypeSysOffsetParameter *p)
{
	uint32_t CounterTemp;

	if(p->uOffsetCounter >= 30000)
	{
		for(CounterTemp = 0;CounterTemp < 4;CounterTemp++)
		{
			p->fOffsetValue[CounterTemp] = p->fOffsetAdd[CounterTemp] / 30000.0f;
																			  //
			p->fOffsetAdd[CounterTemp] = 0.0;   //零漂计算值
		}

			//fnVarCopyToRam(sInComm.DSP_RAM,&p->fOffsetValue[CounterTemp],(BaseAddr_OffsetCal + (CounterTemp<<1)));//
			//将零漂值计算结果存入RAM
		
		p->uOffsetCounter = 0;
												 
	     p->uSampleOffsetEn = FALSE;
//		 *(sInComm.DSP_RAM+926) = 0;
//		 *(sInComm.DSP_RAM+927) = 0;
									  
	}
	else
	{
		for(CounterTemp = 0;CounterTemp < 8;CounterTemp++)
		{
			p->fOffsetAdd[CounterTemp] += AnalogInput.fADResult[CounterTemp];

			p->fOffsetAdd[5] = 0;
			p->fOffsetAdd[6] = 0;
			p->fOffsetAdd[7] = 0;
		}
		p->uOffsetCounter++;
	}  
}

//===========================================================================
// No more.
//===========================================================================

