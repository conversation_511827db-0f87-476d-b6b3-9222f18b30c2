/*
// Version: V1.0 文件创建
// Date: 	Dec 12, 2012
// Author:	ch<PERSON><PERSON><PERSON> @YD
//==========================================================================
//
// 文件名:	SysCtl_GlobalVar.h
//
// 用途:    全局结构体外部声明
//
//==========================================================================
*/

#ifndef SYSCTL_GlOBALVAR_H
#define SYSCTL_GlOBALVAR_H
#include "at32a403a.h"
//#include "uart_queue.h"
#include "SysCtl_AllHeaders.h"
#define BUF_SIZE   0x80  // Sample buffer size


#define FltWord_DSPtoARM      *(volatile uint16_t*)&DMABuf1[0x40] //
#define FreqEn_DSPtoARM   *(volatile uint16_t*)&DMABuf1[0x46] //频率计算使能
#define Location_DSPtoARM      *(volatile uint16_t*)&DMABuf1[0x48]//
#define StMach_ARMtoDSP    *(volatile uint16_t*)(0x280500) //ARM状态机
#define FreqRef_ARMtoDSP   *(volatile float*)(0x280502)//周期性DAC数据传递
#define VolqRef_ARMtoDSP   *(volatile float*)(0x280504)//周期性DAC数据传递

#define BaseAddr_OffsetCal   928
//=================系统故障索引结构体========================================*
struct SysErrIndex_BITS {                                 // bits	description
                          uint16_t SysHFaultFlag:1;        //系统重故障标志
                          uint16_t  SysHFaultOspeed:1;        //系统重故障标志
						  uint16_t  rsvd:14;
                          };
union SysErrIndex_REG {
                       uint16_t			      	 all;
                       struct SysErrIndex_BITS   bit;
                      };

//=================ARM故障标志========================================
struct ARMErr_BITS {                                 // bits	description
                          uint16_t  ARMHFaultFlag:1;        //系统重故障标志
						  uint16_t  rsvd:15; 
                          };
union ARMErr_REG   {
                       uint16_t			     all;
                       struct  ARMErr_BITS   bit;
                    };
//=================DSP故障标志========================================
struct DSPErr_BITS {                                 // bits	description
                          uint16_t  OCFaultFlag:1;        //系统重故障标志
						  uint16_t  rsvd:15; 
                    };
union DSPFault_REG   {
                       uint16_t			     all;
                       struct  DSPErr_BITS   bit;
                     };

struct SysCtl_BITS {		                   // bits	description
                     uint16_t    StartInstr:1;   //启动指令
					 uint16_t    StopInstr:1;    //停机指令
					 uint16_t    EStopInstr:1;   //急停指令					 
					 uint16_t    ResetInstr:1;   //复位指令
                     uint16_t    OffSetInst:1;   //系统零漂指令
					 uint16_t    LVDebugInst:1;  //低压调试指令
					 uint16_t    MotorReverEn:1; //电机反转
					 uint16_t    HVDebugInst:1;  //电机测参数指令
					 uint16_t    SynSwitchInst:1;//同步投切指令
					 uint16_t    ErrMaskInstr:1; //故障屏蔽
                     uint16_t    rsvd:6;         // 7-15 reserved
                    };
union SysCtl_REG   {
                     uint16_t	   all;
                     struct SysCtl_BITS   bit;
                    };
//系统控制模式
struct SysCtlMode_BITS {                           // bits  description
                     uint16_t    IO1:1;   //位置1
                     uint16_t    IO2:1;    //位置2
                     uint16_t    IO3:1;   //位置3
                     uint16_t    IO4:1;   //位置4
                     uint16_t    IO5:1;  //位置5
                     uint16_t    IO6:1;  //位置6
                     uint16_t    rsvd:10;         // 6-15 reserved
                    };
union SysCtlMode_REG   {
                     uint16_t    all;
                     struct SysCtlMode_BITS   bit;
                    };

//===== 低压调试结构体 ====================================
typedef struct {
                //
				uint16_t  uLVDebugEN;
                float fTheta_base;//相位计算
				float fUu_base;//U相调制波
				float fUv_base;//U相调制波
				float fUw_base;//U相调制波

				float fUuBoost;
				float fUvBoost;
				float fUwBoost;

				uint16_t uUu_Fib;
				uint16_t uUv_Fib;
				uint16_t uUw_Fib;
                }LVDebugTEST;

                //初始值
#define LVDebug_DEFAULTS {0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0}
//===== 系统控制结构体 ====================================
typedef struct {
				//
				uint16_t  uStartMode;

				}TypeSYSCTRL;

				//初始值
#define SYSCtrl_DEFAULTS {0}
//===== 保护参数结构体 ================================
typedef struct {
                //参数
				float fOCProtect;//过流保护值 
                float fOCProtect_Per;//过流保护标幺值  

                } TypeSysProParameter;
                 
                //初始值
#define SysProParameter_DEFAULTS {0.0,0.0}
//===== 波形上传结构体 ====================================
typedef struct {
                float VarScope[30];
				uint16_t ScopeCount;
                }SCOPEDATA;

                //初始值
#define ScopeData_DEFAULTS {{0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,\
                             0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0},0}

//=================额定值参数=========================================
typedef struct {
                float fMotorRatedVoltage;//电机额定电压
				float fMotorRatedCurrent;//电机额定电流
				float fMotorRatedFre;//电机额定频率
				float fMotorRatedPower;//电机额定功率
				float fMotorRateSpeed;//电机额定转速，转/分
				float fMotorPoleNum;//电机极对数
				float fMotorRatedSlip;//电机转差率
				float fVFDInputVolatge;//变频器额定输入电压
				float fVFDInputCurrent;//变频器额定输入电流
				float fVFDRatedPower;//变频器额定功率
				float fVFDInputFre;//变频器额定输入频率
				float fVFDOutputVoltage;//变频器额定输出电压
				float fVFDOutputCurrent;//变频器额定输出电流
                } TypeSysRatedParameter;
//================额定参数默认变量初始化===============================
#define SysRatedParameter_DEFAULTS {0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0}
//===== 系统基值结构体 ====================================
typedef struct {   
                //输出
				float  fVoltBaseValueGrid;   //系统输出电压基值
				float  fVoltBaseValue;   //系统输出电压基值
				float  fCurrBaseValue;   //系统电流基值
				float  fSpeedBaseValue;  //系统转速基值
				float  fThetaBaseValue;  //系统电角度基值
				float  fPowerBaseValue; //系统功率基值
				float  fZBaseValue;//系统阻抗基值
				float  fLBaseValue;//系统电感基值
				float  fPhirBaseValue;//系统磁链基值
				float  fOmegBaseValue;//系统机械角度基值
				float  fTeBaseValue;//系统转矩基值
				float  fJBaseValue;//转动惯量基值
				float  fFBaseValue;//摩擦系数基值
				float  fHBaseValue;//惯性时间常数基值

				//函数
				  void   (*pfnSysBaseValueCal)();//系统基值计算
			   } TypeSysBaseValue;

                //初始值
#define SysBaseValue_DEFAULTS {0.0,0.0,0.0,0.0,0.0,0.0,0.0, \
                               0.0,0.0,0.0,0.0,0.0,0.0,0.0, \
                               (void (*)(uint32_t))fnSysBaseValueCal, \
                               }
//=================环境参数设定=========================================

typedef enum {
				AsynMotor = 0,       //异步电机
				SynMotor = 1,        //同步电机
				Reactor = 2,        //电抗器
				ThreeSmotor = 3,   //PWM整流
				PWM_Inverter = 4     //并网逆变

			} TypeSysVFDLoad;   //负载类型

typedef enum {
                PWM_Inverter_Constant_Power = 0,       //异步电机
                PWM_Inverter_Constant_current = 1,        //同步电机
                PWM_Inverter_Constant_resistance = 2        //电抗器

			  } TypeLoad_Inv;   //逆变负载类型


typedef enum {
				PGVVVF = 0,         //VVVF
				NPGVVVF = 1,      //VVVF
				VC = 2,             //VC
				SLVC = 3,            //SLVC
                Idealer = 4,            //怠速
                Generator = 5           //发电

			} TypeSysControlStrat;  //控制策略
typedef struct {
			     uint16_t uConRunType;//变频器类型
			     TypeSysVFDLoad uVFDLoadType;//负载类型
			     TypeSysControlStrat uConStrat;//电机控制策略

			   } TypeSysEnvironParameter;
//================环境参数默认变量初始化===============================
#define SysEnvironParameter_DEFAULTS {0,AsynMotor,NPGVVVF}

extern void fnSysBaseValueCal(TypeSysBaseValue *p);
extern TypeSysMoore SysMoore;
extern LVDebugTEST SysLVDebug;
extern union  SysErrIndex_REG SysErrIndexReg;
extern union  SysCtlMode_REG  SysCtlModeREG;
extern union SysCtl_REG SysCtlReg;
extern union ARMErr_REG ARMFaultReg;
extern union DSPFault_REG DSPFaultCodeReg;
extern TypeSysProParameter SysProParamReg;
extern SCOPEDATA *ScopeDATAUpLoad;
extern TypeSysRatedParameter SysRatedParameter;
extern TypeSysBaseValue SysBaseValue;
extern TypeSysEnvironParameter SysEnviConfg;
extern TypeSYSCTRL SysCtlParameter;
extern void fnSysHMICommInit(void);
// extern void fnSysFreCalHMI(void);
extern void fnSystemInitParam(void);
// extern void fnSysFreCalInit(void);
// extern void fnVFBaseCalculusCal(LVDebugTEST *p);
// extern void fnSysCtlLvDebug(void);
// extern void fnVarScopeAssign(void);

//extern void InitAlgorithm(void);
//extern void Algorithm(void);


extern uint32_t uDMA_Addr;
extern volatile uint16_t DMABuf1[BUF_SIZE];
extern volatile uint16_t *DMADest1;
extern volatile uint16_t *DMASource1;
extern volatile uint16_t *RdDSPtoARM;
extern volatile uint16_t *pARMParamRdFlag;
extern volatile float *pOffsetRam;
extern volatile uint16_t *pWaveParamStart;
extern volatile float *pEstSpeed;
extern volatile float *pRunSpeed;
//extern volatile float *pDsptoArmUab;
//extern volatile float *pDsptoArmUbc;
extern volatile float *pFaultWaveStart;
//extern INTERG Interg_Vector;
//extern VECTOR_ST SpeedTest;
//extern PIREG PhrPI;
extern uint16_t uDirCount;
extern uint16_t uDirCountMax;
extern uint32_t CNT_FR_VALUE;
extern float CutOff_Freq;
extern float machineAngle;
extern float eleAngle;

#endif  

