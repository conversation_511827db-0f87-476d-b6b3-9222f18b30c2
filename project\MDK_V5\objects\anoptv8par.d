./objects/anoptv8par.o: ..\..\AnoPTv8\AnoPTv8Par.c \
  ..\..\AnoPTv8\AnoPTv8Par.h ..\..\AnoPTv8\AnoPTv8.h \
  ..\..\AnoPTv8\AnoPTv8FrameFactory.h ..\..\AnoPTv8\HWInterface.h \
  ..\..\AnoPTv8\MotorParams.h ..\..\AnoPTv8\MotorData.h \
  ..\..\User\sysTypeDef.h \
  ..\..\libraries\cmsis\cm4\device_support\at32a403a.h \
  ..\..\libraries\cmsis\cm4\core_support\core_cm4.h \
  ..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h \
  ..\..\libraries\drivers\inc\at32a403a_def.h \
  ..\..\User\src_inc\at32a403a_conf.h \
  ..\..\libraries\drivers\inc\at32a403a_adc.h \
  ..\..\libraries\drivers\inc\at32a403a_crm.h \
  ..\..\libraries\drivers\inc\at32a403a_debug.h \
  ..\..\libraries\drivers\inc\at32a403a_dma.h \
  ..\..\libraries\drivers\inc\at32a403a_exint.h \
  ..\..\libraries\drivers\inc\at32a403a_flash.h \
  ..\..\libraries\drivers\inc\at32a403a_gpio.h \
  ..\..\libraries\drivers\inc\at32a403a_misc.h \
  ..\..\libraries\drivers\inc\at32a403a_pwc.h \
  ..\..\libraries\drivers\inc\at32a403a_spi.h \
  ..\..\libraries\drivers\inc\at32a403a_tmr.h \
  ..\..\libraries\drivers\inc\at32a403a_usb.h \
  ..\..\libraries\drivers\inc\at32a403a_wdt.h
