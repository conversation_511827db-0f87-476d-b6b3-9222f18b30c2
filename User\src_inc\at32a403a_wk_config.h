/* add user code begin Header */
/**
  **************************************************************************
  * @file     at32a403a_wk_config.h
  * @brief    header file of work bench config
  **************************************************************************
  *                       Copyright notice & Disclaimer
  *
  * The software Board Support Package (BSP) that is made available to
  * download from Artery official website is the copyrighted work of Artery.
  * Artery authorizes customers to use, copy, and distribute the BSP
  * software and its related documentation for the purpose of design and
  * development in conjunction with Artery microcontrollers. Use of the
  * software is governed by this copyright notice and the following disclaimer.
  *
  * THIS SOFTWARE IS PROVIDED ON "AS IS" BASIS WITHOUT WARRANTIES,
  * <PERSON><PERSON><PERSON><PERSON><PERSON>ES OR REPRESENTATIONS OF ANY KIND. ARTERY EXPRESSLY DISCLAIMS,
  * TO THE FULLEST EXTENT PERMITTED BY LAW, ALL EXPRESS, IMPLIED OR
  * STATUTORY OR OTHER WARRANTIES, GUARANTEES OR REPRESENTATIONS,
  * INCLUDING BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY,
  * FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT.
  *
  **************************************************************************
  */
/* add user code end Header */

/* define to prevent recursive inclusion -----------------------------------*/
#ifndef __AT32A403A_WK_CONFIG_H
#define __AT32A403A_WK_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* includes -----------------------------------------------------------------------*/
#include "stdio.h"
#include "at32a403a.h"

/* private includes -------------------------------------------------------------*/
/* add user code begin private includes */

/* add user code end private includes */

/* exported types -------------------------------------------------------------*/
/* add user code begin exported types */

/* add user code end exported types */

/* exported constants --------------------------------------------------------*/
/* add user code begin exported constants */

/* add user code end exported constants */

/* exported macro ------------------------------------------------------------*/
/* add user code begin exported macro */

/* add user code end exported macro */

/* add user code begin dma define */
/* user can only modify the dma define value */
#define DMA1_CHANNEL1_BUFFER_SIZE   0
#define DMA1_CHANNEL1_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL1_PERIPHERAL_BASE_ADDR  0

//#define DMA1_CHANNEL2_BUFFER_SIZE   0
//#define DMA1_CHANNEL2_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL2_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL3_BUFFER_SIZE   0
//#define DMA1_CHANNEL3_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL3_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL4_BUFFER_SIZE   0
//#define DMA1_CHANNEL4_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL4_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL5_BUFFER_SIZE   0
//#define DMA1_CHANNEL5_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL5_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL6_BUFFER_SIZE   0
//#define DMA1_CHANNEL6_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL6_PERIPHERAL_BASE_ADDR   0

//#define DMA1_CHANNEL7_BUFFER_SIZE   0
//#define DMA1_CHANNEL7_MEMORY_BASE_ADDR   0
//#define DMA1_CHANNEL7_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL1_BUFFER_SIZE   0
//#define DMA2_CHANNEL1_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL1_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL2_BUFFER_SIZE   0
//#define DMA2_CHANNEL2_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL2_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL3_BUFFER_SIZE   0
//#define DMA2_CHANNEL3_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL3_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL4_BUFFER_SIZE   0
//#define DMA2_CHANNEL4_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL4_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL5_BUFFER_SIZE   0
//#define DMA2_CHANNEL5_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL5_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL6_BUFFER_SIZE   0
//#define DMA2_CHANNEL6_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL6_PERIPHERAL_BASE_ADDR   0

//#define DMA2_CHANNEL7_BUFFER_SIZE   0
//#define DMA2_CHANNEL7_MEMORY_BASE_ADDR   0
//#define DMA2_CHANNEL7_PERIPHERAL_BASE_ADDR   0
/* add user code end dma define */

/* Private defines -------------------------------------------------------------*/
#define TEMP_Fail_PIN    GPIO_PINS_14
#define TEMP_Fail_GPIO_PORT    GPIOC
#define LS_Fail_PIN    GPIO_PINS_15
#define LS_Fail_GPIO_PORT    GPIOC
#define M_PT0_PIN    GPIO_PINS_0
#define M_PT0_GPIO_PORT    GPIOA
#define M_PT1_PIN    GPIO_PINS_1
#define M_PT1_GPIO_PORT    GPIOA
#define B_PT0_PIN    GPIO_PINS_3
#define B_PT0_GPIO_PORT    GPIOA
#define U_DC_PIN    GPIO_PINS_5
#define U_DC_GPIO_PORT    GPIOA
#define ADC2_IDC_PIN    GPIO_PINS_6
#define ADC2_IDC_GPIO_PORT    GPIOA
#define ADC2_IU_PIN    GPIO_PINS_7
#define ADC2_IU_GPIO_PORT    GPIOA
#define ADC2_IV_PIN    GPIO_PINS_0
#define ADC2_IV_GPIO_PORT    GPIOB
#define ENC_A_PIN    GPIO_PINS_6
#define ENC_A_GPIO_PORT    GPIOC
#define ENA_B_PIN    GPIO_PINS_7
#define ENA_B_GPIO_PORT    GPIOC
#define ENC_Z_PIN    GPIO_PINS_8
#define ENC_Z_GPIO_PORT    GPIOC
#define RDC_Reset_PIN    GPIO_PINS_9
#define RDC_Reset_GPIO_PORT    GPIOC
#define RDC_CS_PIN    GPIO_PINS_15
#define RDC_CS_GPIO_PORT    GPIOA
#define RDC_WR_PIN    GPIO_PINS_10
#define RDC_WR_GPIO_PORT    GPIOC
#define RDC_IN_DOS_PIN    GPIO_PINS_11
#define RDC_IN_DOS_GPIO_PORT    GPIOC
#define RDC_IN_LOT_PIN    GPIO_PINS_12
#define RDC_IN_LOT_GPIO_PORT    GPIOC
#define RDC_SAMPLE_PIN    GPIO_PINS_2
#define RDC_SAMPLE_GPIO_PORT    GPIOD
#define RDC_SCK_PIN    GPIO_PINS_3
#define RDC_SCK_GPIO_PORT    GPIOB
#define RDC_MISO_PIN    GPIO_PINS_4
#define RDC_MISO_GPIO_PORT    GPIOB
#define RDC_MOSI_PIN    GPIO_PINS_5
#define RDC_MOSI_GPIO_PORT    GPIOB
#define RDC_IN_DIR_PIN    GPIO_PINS_7
#define RDC_IN_DIR_GPIO_PORT    GPIOB
#define RDC_A1_PIN    GPIO_PINS_8
#define RDC_A1_GPIO_PORT    GPIOB
#define RDC_A0_PIN    GPIO_PINS_9
#define RDC_A0_GPIO_PORT    GPIOB

/* exported functions ------------------------------------------------------- */
  /* system clock config. */
  void wk_system_clock_config(void);

  /* config periph clock. */
  void wk_periph_clock_config(void);

  /* init debug function. */
  void wk_debug_config(void);

  /* nvic config. */
  void wk_nvic_config(void);

  /* init gpio function. */
  void wk_gpio_config(void);

  /* init adc1 function. */
  void wk_adc1_init(void);

  /* init adc2 function. */
  void wk_adc2_init(void);

  /* init spi3 function. */
  void wk_spi3_init(void);

  /* init tmr1 function. */
  void wk_tmr1_init(void);

  /* init tmr3 function. */
  void wk_tmr3_init(void);

  /* init tmr6 function. */
  void wk_tmr6_init(void);

  /* init usbfs function. */
  void wk_usbfs_init(void);

  /* init wdt function. */
  void wk_wdt_init(void);

  /* init dma1 channel1 */
  void wk_dma1_channel1_init(void);

  /* config dma channel transfer parameter */
  /* user need to modify parameters memory_base_addr and buffer_size */
  void wk_dma_channel_config(dma_channel_type* dmax_channely, uint32_t peripheral_base_addr, uint32_t memory_base_addr, uint16_t buffer_size);

/* add user code begin exported functions */

/* add user code end exported functions */

#ifdef __cplusplus
}
#endif

#endif
