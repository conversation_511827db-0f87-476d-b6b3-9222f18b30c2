<html>
    <head>
    <META http-equiv="Content-Type" content="text/html">
        <style type="text/css">
    		h1, h2, h3, h4, h5, h6 { 
    			font-family : sans-serif;
    			color : white;
    			background-color : #21489e;
    			padding: 0.3em;
    		}
    		body { 
    			font-family : verdana;
    		}
    		td, th {  
    			padding: 0.5em;
    			text-align : left;
    			width: auto;	
    		}
    		th { 
    			background-color : #A6CAF0;
    		
    		}
    		th.column1, td.column1 { 
    			text-align: left;
    			width : 18%; 
    		}
    		table { 
    			width : 100%; 
    		}
    		.front_matter, .front_matter_column1, .front_matter_column2, .front_matter_column3 {
    			padding-top : 0.1em;
    			padding-bottom : 0.1em;
    			border : 0px solid black;
    			width : auto;
    			vertical-align: top
    		}
    		.front_matter_column1 {
    			text-align : right;
    		}
    		.total_column1, .total_column {
    			font-weight : bold;	
    		}
    		.total_column1 {
    			text-align : right;
    		}
    		.front_matter_column2 {
    			text-align : center;
    		}
    		.front_matter_column3 {
    			text-align : left;
    		}
    		.warning, .error { 
    			color : red;
    			font-weight : bold;
    		}	
    		tr.onmouseout_odd { 
    			background-color : #EEEEE0 
    		}
    		tr.onmouseout_even { 
    			background-color : #F3F3E3   
    		}
    		tr.onmouseover_odd, tr.onmouseover_even { 
    			background-color : lightYellow 
    		} 
    		a:link, a:visited, .q a:link,.q a:active,.q {
    			color: #21489e; 
    		}
    		a:link.callback, a:visited.callback { 
    			color: #008000 
    		}
    		a:link.customize, a:visited.customize {
    			color: #C0C0C0;
    			position: absolute; 
    			right: 10px;
    		}	
    		p.contents_level1 {
    			font-weight : bold;
    			font-size : 110%;
    			margin : 0.5em;
    		}
    		p.contents_level2 {
    			position : relative;
    			left : 20px;
    			margin : 0.5em;
    		}
    	</style>
    </head>
    
    <body>
        <img ALT="Altium" src="file://D:\PROGRAM FILES (X86)\ALTIUM DESIGNER S09 VIEWER\Templates\AD-rgb_logo_223x64.gif">
        <a href="dxpprocess://Client:SetupPreferences?Server=PCB|PageName=General" class="customize"><acronym title="dxpprocess://Client:SetupPreferences?Server=PCB|PageName=General">Reporting Options</acronym></a>
        <h1>File in Newer Format</h1>
        
        <table class="front_matter">
            <tr class="front_matter">
            <td class="front_matter_column1">Date</td>
            <td class="front_matter_column2">:</td>
            <td class="front_matter_column3">2025/8/11</td>
            </tr>
            <tr class="front_matter">
            <td class="front_matter_column1">Time</td>
            <td class="front_matter_column2">:</td>
            <td class="front_matter_column3">14:38:36</td>
            </tr>
            <tr class="front_matter">
            <td class="front_matter_column1">Filename</td>
            <td class="front_matter_column2">:</td>
            <td class="front_matter_column3"><a href="file://E:\AT32_Work\AT32A403ARG_154_33_Work\AT32A403ARGT7_154_33\DOC\J-SM-800W_MCU_V1.0_Project\LV.PcbLib" class="file"><acronym title="E:\AT32_Work\AT32A403ARG_154_33_Work\AT32A403ARGT7_154_33\DOC\J-SM-800W_MCU_V1.0_Project\LV.PcbLib">E:\AT32_Work\AT32A403ARG_154_33_Work\AT32A403ARGT7_154_33\DOC\J-SM-800W_MCU_V1.0_Project\LV.PcbLib</acronym></a></td>
            </tr>
        </table>
        
        <br>
        
        <table>
            <tr>
                <th style="text-align : left" colspan="1" class="">Version</th>
                <th style="text-align : left" colspan="1" class="">Warning</th>
            </tr>
    
            <tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
                <td class="column1">Release 10</td>
                <td class="column2"><b>CAUTION</b> - New Custom Grids and Guides were introduced. Be aware that your design might contain Custom Grids and Guides that cannot be read in previous versions. </td>
            </tr>
        </table>
        <br><hr>
        <p>This file was generated by <b>a later</b> version of the software</p>
    </body>
</html>
