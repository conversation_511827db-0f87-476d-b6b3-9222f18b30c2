/*
 * Motor_VectorControl.c
 *
 *  Created on: 2018年9月17日
 *      Author: Hiter
 */

#include "SysCtl_AllHeaders.h"
#include "arm_math.h"

// #define ARR_20kHz 7500
static inline float _pi_speed_inc_fun_calc(PI_SPEED *v, float ref, float fdb);
#define _SVPWM_ // 使用SVPWM七矢量合成算法，否则开启SPWM+零序电压注入算法
LPF_TypeDef Ia_flt = {0.0f, 0.0f, 0.381f, 0.0f};
LPF_TypeDef Ib_flt = {0.0f, 0.0f, 0.381f, 0.0f};
LPF_TypeDef Ic_flt = {0.0f, 0.0f, 0.381f, 0.0f};
LPF_TypeDef Id1_flt = {0.0f, 0.0f, 0.0f, 0.0f};
LPF_TypeDef Iq1_flt = {0.0f, 0.0f, 0.0f, 0.0f};

float laal = 0.00002f;
float laad = 0.0001f;
float laaq = 0.00013f;

float v_max1 = 0.0f;
float v_min1 = 0.0f;
float v_max2 = 0.0f;
float v_min2 = 0.0f;
float v_zero1 = 0.0f;
float v_zero2 = 0.0f;

float Tc1;
float Tc2;
float Tc3; // svpwm各相占空比时间
float limit_iq_ref=0.0f;

// extern float FeedforwordFlag;
float FeedforwordFlag = 0.0f; // 前馈标志
extern float QH;
extern float vvvf_zero;
extern float vvvf_max;
extern float vvvf_min;

// Vector_ctrl SynMotorVc = Vector_ctrl_DEFAULTS;//矢量控制结构体声明,初始化矢量控制
//----------------------------------------------------------

// extern union ContactorCtl_REG ContactorCtl;                            //接触器控制字
// extern union ContactorFlag_REG ContactorFlag;                            //接触器触点反馈控制字
extern Filter Filter_idc;
extern Filter Filter_idc1;
extern Filter Filter_idc2;
float P = 0.0f;
float Ifoward;
float Ufor = 300.0f;
float US = 300.0f;
float power_cos = 1.0f;
float Isfor = 0.0f;
// float IF_theta; //定义IF给定角度
// float motor_theta;//定义电机真实角度

CurvePara_TypeDef trapeLine = {0.0f,0.0f,0.0f,0.0f,10000.0f,-10000.0f,0,1000,0.0f,0,10000};
//---------------------------------------------------------
// extern drive_ctrl _c;
//------------------------------------------------//
Parameter_set para = Parameter_set_DEFAULTS; // 电机参数
Parameter_used para2;                        // 实际用到的电机参数，为标幺值
//------------------------------------------------//
PARK park1 = PARK_DEFAULTS;
CLARKE clarke1 = CLARKE_DEFAULTS;         //
VSDCLARKE vsdclarke = VSDCLARKE_DEFAULTS; // 定义VSD变换
// IVSDCLARKE ivsdclarke = IVSDCLARKE_DEFAULTS;
// VSDCLARKEfault vsdclarkefault = VSDCLARKEfault_DEFAULTS;
// IVSDCLARKEfault ivsdclarkefault = IVSDCLARKEfault_DEFAULTS;
IPARK ipark1 = IPARK_DEFAULTS;       // dq电压转化为alphabeta电压求调制波
IPARK ipark2 = IPARK_DEFAULTS;       // dq电流转化为alphabeta电流滤波
ICLARKE iclarke1 = ICLARKE_DEFAULTS; // alphabeta电流转化为abc电流
ICLARKE iclarke2 = ICLARKE_DEFAULTS; // alphabeta电压转化为abc电压

LPFL lpf_isd = LPFL_DEFAULTS;     //
LPFL lpf_isd1 = LPFL_DEFAULTS;    //
LPFL lpf_isd2 = LPFL_DEFAULTS;    //
LPFL lpf_isd_ref = LPFL_DEFAULTS; // d轴电流参考滤波结构体
LPFL lpf_isq = LPFL_DEFAULTS;     // q轴电流滤波结构体
LPFL lpf_isq1 = LPFL_DEFAULTS;    // q轴电流滤波结构体
LPFL lpf_isq2 = LPFL_DEFAULTS;    // q轴电流滤波结构体
LPFL lpf_usd1 = LPFL_DEFAULTS;    //
LPFL lpf_usd2 = LPFL_DEFAULTS;    //
LPFL lpf_usq1 = LPFL_DEFAULTS;    // q轴电流滤波结构体
LPFL lpf_usq2 = LPFL_DEFAULTS;    // q轴电流滤波结构体
LPFL lpf_isq_ref = LPFL_DEFAULTS; // q轴电流参考滤波结构体
LPFL lpf_ekf_w = LPFL_DEFAULTS;   // 反电动势滤波结构体

LPFL lpf_w = LPFL_DEFAULTS;     // 实际转速滤波结构体
LPFL lpf_w_ref = LPFL_DEFAULTS; // 参考转速滤波结构体
//=======================================================================================//

PI_SPEED_CONST pi_speed_const = PI_SPEED_CONST_DEFAULTS;
PI_CURRENT_CONST pi_current_const = PI_CURRENT_CONST_DEFAULTS;

PI_SPEED pi_speed = PI_SPEED_DEFAULTS;
PI_VOLTAGE pi_voltage = PI_VOLTAGE_DEFAULTS;
PI_fun pi_isd = PI_DEFAULTS;
PI_fun pi_isq = PI_DEFAULTS;
PI_fun pi_isd1 = PI_DEFAULTS;
PI_fun pi_isq1 = PI_DEFAULTS;
PI_fun pi_isd2 = PI_DEFAULTS;
PI_fun pi_isq2 = PI_DEFAULTS;         // 额外定义了两个电流PI计算环
PI_fun pi_isqz = PI_DEFAULTS;         // 定义故障电流PI计算环
PI_fun pi_isdz = PI_DEFAULTS;         // 定义故障电流PI计算环
PI_fun pi_isxz = PI_DEFAULTS;         // 定义故障电流PI计算环
PI_flux pi_flux_w = PI_flux_DEFAULTS; // PI for the adaptive flux weakening
                                      //------------------------------------------------//
                                      // variables for the flux estimation
                                      //------------------------------------------------//



// 定义全局变量
OpenLoopFreqCtrl_TypeDef gOpenLoopFreqCtrl = {0};

void UpdatePwm(float tu, float tv, float tw);
void SVPWM(float U_alpha, float U_beta, float U_dc);
void pi_speed_inc_fun_calc(PI_SPEED *v);
float lpf_filter(LPF_TypeDef *v, float x);
float WeakFluxCtr(float vref, float vs, float delta);
void PI_Speed_Incremental_Calc(PI_SPEED *v);
void Vector_ctrl_ResetTs(Vector_ctrl *v)
{
    v->PrePosition_Flag = 0;
    para.Ts = v->Ts;                 //
    para.ts = para.Ts * para.BASE_w; //
    para2.Ts = para.ts;              //
                                     //--------------------------------------------------------------------------------------------//
    lpf_w.Tc = para.Ts;              // para.ts;
    lpf_w_ref.Tc = para.Ts;          //
    lpf_isd.Tc = para.Ts;
    lpf_isd1.Tc = para.Ts;
    lpf_isd2.Tc = para.Ts;    // para.ts;
    lpf_isd_ref.Tc = para.Ts; // para.ts;
    lpf_isq.Tc = para.Ts;
    lpf_isq1.Tc = para.Ts;
    lpf_isq2.Tc = para.Ts;    //  para.ts;
    lpf_isq_ref.Tc = para.Ts; //  para.ts;
    lpf_ekf_w.Tc = para.Ts;
    //===============================================================================================//
    pi_speed.Tc = para.Ts * 10.0; // para.ts*10.0;
    // pi_voltage.Tc = para.Ts*10.0;                                      // para.ts*10.0;
    //------------------------------------------------------------//

    //------------------------------------------------------------//
    pi_isd.Tc = para.Ts; // para.ts;
    pi_isq.Tc = para.Ts; // para.ts;

    //------------------------------------------------------------//
    pi_flux_w.Tc = para.Ts;
}

//====================================================矢量控制参数初始化============================//
void Vector_ctrl_init(Vector_ctrl *v)
{
    // 对转速获取进行初始化清零
    // F_count=*pCsvParamcountresolver;
    // lpf_mras_iqest.y_old =0;
    // lpf_mras_iqest.y=0;
    // lpf_mras_idest.y_old =0;
    // lpf_mras_idest.y=0;
    // lpf_mras_iq.y_old =0;
    // lpf_mras_iq.y=0;
    // lpf_mras_id.y_old =0;
    // lpf_mras_id.y=0;
    // lpf_mras_we.y_old =0;
    // lpf_mras_we.y =0;
    // lpf_mras_w.y_old =0;
    // lpf_mras_w.y =0;
    // lpf_mras_uq.y_old =0;
    // lpf_mras_uq.y=0;
    // lpf_mras_ud.y_old =0;
    // lpf_mras_ud.y=0;
    v->controlLoopMode = (unsigned int)*pCsvControlLoopMode; // 控制环路选择
    v->YUDINGtime = 4;
    lpf_w.y = 0;
    lpf_w.y_old = 0;
    lpf_isd.y = 0;
    lpf_isd.y_old = 0;
    lpf_isd1.y = 0;
    lpf_isd1.y_old = 0;
    lpf_isd2.y = 0;
    lpf_isd2.y_old = 0;
    lpf_isq.y = 0;
    lpf_isq.y_old = 0;
    lpf_isq1.y = 0;
    lpf_isq1.y_old = 0;
    lpf_isq2.y = 0;
    lpf_isq2.y_old = 0;
    lpf_isq_ref.y = 0;
    lpf_isq_ref.y_old = 0;

    lpf_usd1.y = 0;
    lpf_usd1.y_old = 0;
    lpf_usd2.y = 0;
    lpf_usd2.y_old = 0;
    lpf_usq1.y = 0;
    lpf_usq1.y_old = 0;
    lpf_usq2.y = 0;
    lpf_usq2.y_old = 0;
    v->Ts = *pCsvParameConTc;
    //--------------------------------------------------------------------------------------------//
    para.Fs_cpu_M = 150; // 设置CPU主频150MHz
    para.Ts = v->Ts;     // 设置控制周期20kHz
    //===================================电机的程序==================================================//
    para.P_N = SysRatedParameter.fMotorRatedPower * 1000; // 250kW
    para.UL_N = SysRatedParameter.fMotorRatedVoltage;     // 380.0;
    para.I_N = SysRatedParameter.fMotorRatedCurrent;      // 450A
    para.rpm_N = SysRatedParameter.fMotorRateSpeed;       // 18000rpm
    para.F_N = SysRatedParameter.fMotorRatedFre;          // 600Hz rated frequency，800HzMax
    para.p = SysRatedParameter.fMotorPoleNum;             // 2;
    //===============================以下参数需要谨慎设计================================================//  // NOLINT
    //=================永磁同步电机参数更新==============================
    para.Rs = *pCsvParamSynRs;   // 0.00204429
    para.Lsd = *pCsvParamSynLsd; // 0.0000433705178
    para.Lsq = *pCsvParamSynLsq;

    // 这个数对应的是相电动势的峰值除以转速
    para.flux_r_N = *pCsvParamSynFlux; // 0.07994172826
    para.Ld = para.Lsd;                // not used
    para.Lq = para.Lsq;
    para.friction = 0.000128; // 转动惯量
    para.Inertia = 0.00072;
    //------------------------------------------//
    para.H = 5;                     // PI参数
    para.Wc_wr = 20.0 * 2.0 * PI;   // 转速环截止频率
    para.Wc_cur = 300.0 * 2.0 * PI; // 电流环截止频率

    //--------------------------------------------------------------------------------------------//
    para.derive(&para); // 计算系统其它参数以及更新标幺值
    //--------------------------------------------------------------------------------------------//
    // parameter used in the control module p.u.
    para2.Ls = para.ls; // 电感
    para2.Rs = para.rs; // 电阻
    para2.Ld = para.ld; // not used                                           //电感
    para2.Lq = para.lq; // not used                                           //电感

    para2.p = para.p; // 极对数

    para2.flux_rN = para.flux_r_pu;    // 磁链
    para2.Inertia = para.inertia_pu;   // 转动惯量
    para2.friction = para.friction_pu; // 摩擦系数
    para2.Wc_cur = para.wc_cur_pu;     // 速度环截止频率
    para2.Wc_wr = para.wc_wr_pu;       // 电流环截止频率

    para2.Imax = para.Imax_pu;             // 电流最大值
    para2.Umax = para.Umax_pu;             // 电压最大值
    para2.Idq_max = para.Idq_max_pu;       // dq电流最大值
    para2.Udq_max = para.Udq_max_pu;       // dq电压最大值
    para2.Torque_max = para.Torque_max_pu; // 转矩最大值
    para2.Freq_max = para.freq_max_pu;     // 频率最大值
    para2.flux_max = para.flux_max_pu;     // 磁链最大值

    para2.Ts = para.ts; // 时间
    //------------------------------------------------------------//
    //---------------------------------------------
    v->flux_level = 1; // unused
    v->k_comp = 0.0;   // unused
    //---------------------------------------------
    v->Tem_max = 1.0 * para2.Torque_max; // 修改转矩限幅*1.0

    v->isdref_max = para2.Idq_max; // 标幺值 dq轴电流最大值
    v->isqref_max = para2.Idq_max; // 标幺值 dq轴电流最大值
    v->Vsdref_max = para2.Udq_max; // 标幺值 dq轴电压最大值
    v->Vsqref_max = para2.Udq_max; // 标幺值 dq轴电压最大值
    //============================转速、电流滤波截止频率的设置位置 标幺值=========================================//
    lpf_w.wc = para.Wc_wr; // para.wc_wr_pu;
    lpf_w.Tc = para.Ts;    // para.ts;

    lpf_w_ref.wc = para.Wc_wr;
    lpf_w_ref.Tc = para.Ts;

    // lpf_isd.wc = para.Wc_cur;   //para2.Wc_wr; // para.wc_wr_pu;
    lpf_isd.wc = 8000;
    lpf_isd.Tc = para.Ts; // para.ts;
    lpf_isd1.wc = 8000;
    lpf_isd1.Tc = para.Ts;
    lpf_isd2.wc = 8000;
    lpf_isd2.Tc = para.Ts;

    // lpf_isq.wc = para.Wc_cur;  //para2.Wc_wr; // para.wc_wr_pu;
    lpf_isq.wc = 8000;
    lpf_isq.Tc = para.Ts; // para.ts;
    lpf_isq1.wc = 8000;
    lpf_isq1.Tc = para.Ts;
    lpf_isq2.wc = 8000;
    lpf_isq2.Tc = para.Ts;
    // lpf_mras_w.Tc = para.Ts;
    // lpf_mras_w.wc = 20*2*PI;
    // lpf_mras_we.Tc = para.Ts;
    // lpf_mras_we.wc = 20*2*PI;
    // lpf_mras_iqest.Tc = para.Ts;
    // lpf_mras_iqest.wc = 20*2*PI;
    // lpf_mras_idest.Tc = para.Ts;
    // lpf_mras_idest.wc = 20*2*PI;

    // lpf_mras_iq.Tc = para.Ts;
    // lpf_mras_iq.wc = 20*2*PI;
    // lpf_mras_id.Tc = para.Ts;
    // lpf_mras_id.wc = 20*2*PI;
    // lpf_mras_uq.Tc = para.Ts;
    // lpf_mras_uq.wc = 20*2*PI;
    // lpf_mras_ud.Tc = para.Ts;
    // lpf_mras_ud.wc = 20*2*PI;

    lpf_usd1.wc = 8000;
    lpf_usd1.Tc = para.Ts;
    lpf_usd2.wc = 8000;
    lpf_usd2.Tc = para.Ts;
    lpf_usq1.wc = 8000;
    lpf_usq1.Tc = para.Ts;
    lpf_usq2.wc = 8000;
    lpf_usq2.Tc = para.Ts;

    lpf_ekf_w.wc = para.Wc_wr;
    lpf_ekf_w.Tc = para.Ts;

    //=============================电流环参数更新==================================================//
    pi_current_const.wc_current = para2.Wc_cur; // para.wc_cur_pu;
    pi_current_const.ls = para2.Ls;             // para.ls;
    pi_current_const.rs = para2.Rs;             // para.rs;
    pi_current_const.calc(&pi_current_const);
    //=============================PI参数赋值==================================================//
    //---------------------------电压环PI参数设置---------------------------------//
    v->i_voltage_coe = *pCsvParamSynVcVoltageKi; // 转速环积分增益
    v->p_voltage_coe = *pCsvParamSynVcVoltageKp; // 转速环比例增益
    //---------------------------转速环PI参数设置---------------------------------//
    v->i_speed_coe = *pCsvParamSynVcSpeedKi; // 转速环积分增益
    v->p_speed_coe = *pCsvParamSynVcSpeedKp; // 转速环比例增益
    //---------------------------电流环PI参数设置---------------------------------//
    v->i_isd_coe = *pCsvParamSynVcCurrentDKi; //*pi_current_const.Ki;
    v->p_isd_coe = *pCsvParamSynVcCurrentDKp; //*pi_current_const.Kp;
    v->i_isq_coe = *pCsvParamSynVcCurrentQKi; //*pi_current_const.Ki;
    v->p_isq_coe = *pCsvParamSynVcCurrentQKp; //*pi_current_const.Kp;
    //--------------------------磁链环PI参数设置----------------------------------//
    v->p_flux_coe = *pCsvParamSynVcFWKp;
    v->i_flux_coe = *pCsvParamSynVcFWKi;

    v->SpeedLimit = *pCsvParamSynVcLoseSpeedValue;
    v->GiveUdc = *pCsvParamGiveUdc; // 母线电压
    //---------------------------电压环PI参数设置 实际值SI---------------------------------//
    // pi_voltage.Ki = v->i_voltage_coe;                       //电压环积分增益
    // pi_voltage.Kp = v->p_voltage_coe;                       //转速环比例增益
    // pi_voltage.pi_out_max = para.Idq_max;                           //转速环输出限幅
    // pi_voltage.Tc = para.Ts*10.0;                        //转速环控制时间

    //---------------------------转速环PI参数设置 实际值SI---------------------------------//
    pi_speed.Ki = v->i_speed_coe;       // 转速环积分增益
    pi_speed.Kp = v->p_speed_coe;       // 转速环比例增益
    pi_speed.pi_out_max = para.Idq_max; // 转速环输出限幅
    pi_speed.Tc = para.Ts * 10.0f;      // 转速环控制时间
    pi_speed.err1 = 0.0f;               // 上次误差
    //--------------------------磁链环PI参数设置  实际值SI----------------------------------//
    pi_flux_w.Kp = v->p_flux_coe;
    pi_flux_w.Ki = v->i_flux_coe;
    pi_flux_w.pi_out_max = 0; // para2.Idq_max;
    // pi_flux_w.pi_out_min = -para.Idq_max;
    // pi_flux_w.pi_out_min = -15;
    pi_flux_w.pi_out_min = 0 - *pCsvParamfluxidmin;
    pi_flux_w.Tc = para.Ts * 10.0; // 磁链环控制时间，时间应当与转速环一致

    //---------------------------电流环PI参数设置  实际值SI---------------------------------//
    // pi_isd.Ki = v->i_isd_coe;                           //*pi_current_const.Ki;
    // pi_isd.Kp = v->p_isd_coe;                           //*pi_current_const.Kp;
    // pi_isd.pi_var_max = para.Udq_max;                  //  para.Udq_max_pu;
    // pi_isd.pi_out_max = para.Udq_max;                  // para.Udq_max_pu;
    pi_isd1.Ki = v->i_isd_coe; //*pi_current_const.Ki;
    pi_isd1.Kp = v->p_isd_coe;
    pi_isd1.pi_var_max = para.Udq_max; //  para.Udq_max_pu;
    pi_isd1.pi_out_max = para.Udq_max; // para.Udq_max_pu;

    // pi_isdz.Ki = v->i_isd_coe;                           //*pi_current_const.Ki;
    // pi_isdz.Kp = v->p_isd_coe;
    // pi_isdz.pi_var_max = para.Udq_max;                  //  para.Udq_max_pu;
    // pi_isdz.pi_out_max = para.Udq_max;                  // para.Udq_max_pu;
    // pi_isxz.Ki = v->i_isd_coe;                           //*pi_current_const.Ki;
    // pi_isxz.Kp = v->p_isd_coe;
    // pi_isxz.pi_var_max = para.Udq_max;                  //  para.Udq_max_pu;
    // pi_isxz.pi_out_max = para.Udq_max;                  // para.Udq_max_pu;

    // pi_isd2.Ki = v->i_isd_coe;                           //*pi_current_const.Ki;
    // pi_isd2.Kp = v->p_isd_coe;
    // pi_isd2.pi_var_max = para.Udq_max;                  //  para.Udq_max_pu;
    // pi_isd2.pi_out_max = para.Udq_max;                  // para.Udq_max_pu;
    // pi_isd.Tc = para.Ts;                               // para.ts;
    pi_isd1.Tc = para.Ts;
    // pi_isd2.Tc = para.Ts;                  //
    // pi_isdz.Tc = para.Ts;
    // pi_isxz.Tc = para.Ts;

    // pi_isq.Ki = v->i_isq_coe;                           //*pi_current_const.Ki;
    // pi_isq.Kp = v->p_isq_coe;                           //*pi_current_const.Kp;
    // pi_isq.pi_var_max = para.Udq_max;                  // para.Udq_max_pu;
    // pi_isq.pi_out_max = para.Udq_max;                  // para.Udq_max_pu;

    pi_isq1.Ki = v->i_isq_coe;         //*pi_current_const.Ki;
    pi_isq1.Kp = v->p_isq_coe;         //*pi_current_const.Kp;
    pi_isq1.pi_var_max = para.Udq_max; // para.Udq_max_pu;
    pi_isq1.pi_out_max = para.Udq_max; // para.Udq_max_pu;

    // pi_isqz.Ki = v->i_isq_coe;                           //*pi_current_const.Ki;
    // pi_isqz.Kp = v->p_isq_coe;                           //*pi_current_const.Kp;
    // pi_isqz.pi_var_max = para.Udq_max;                  // para.Udq_max_pu;
    // pi_isqz.pi_out_max = para.Udq_max;                  // para.Udq_max_pu;

    // pi_isq2.Ki = v->i_isq_coe;                           //*pi_current_const.Ki;
    // pi_isq2.Kp = v->p_isq_coe;                           //*pi_current_const.Kp;
    // pi_isq2.pi_var_max = para.Udq_max;                  // para.Udq_max_pu;
    // pi_isq2.pi_out_max = para.Udq_max;                  // para.Udq_max_pu;
    // pi_isq.Tc = para.Ts;                               //para.ts;
    // pi_isq2.Tc = para.Ts;
    pi_isq1.Tc = para.Ts;
    // pi_isqz.Tc = para.Ts;
    //===========================死区补偿时间=============================//
    v->Ts_dead_us = *pCsvParamDBT;
    // v->DB_com = (v->Ts_dead_us  * 0.000001 * 0.5)/ v->Ts; // 1.0全补,0.5补偿系数0.5
    // v->DB_com = (v->Ts_dead_us  * 0.0000005 )/ v->Ts; // 1.0全补,0.5补偿系数0.5
    v->DB_com = v->Ts_dead_us * 0.01;
    OpenLoopFreqCtrl_Init(&gOpenLoopFreqCtrl);
}
//=====================================矢量控制重置=====================================
void Vector_ctrl_reset(Vector_ctrl *v)
{
    // pi_voltage.ui = 0.0;                //电压外环积分项清零
    pi_speed.ui = 0.0; // 转速外环积分项清零
    pi_speed.ui_delta = 0.0;
    pi_speed.err = 0.0;
    pi_speed.pi_out = 0.0;

    pi_isd.ui = 0.0; // 电流内环积分项清零
    pi_isd.ui_delta = 0.0;
    pi_isd.err = 0.0;
    pi_isd.pi_out = 0.0;
    pi_isq.ui = 0.0; // 电流内环积分项清零
    pi_isq.ui_delta = 0.0;
    pi_isq.err = 0.0;
    pi_isq.pi_out = 0.0;
    pi_isd1.ui = 0.0; // 电流内环积分项清零
    pi_isd1.ui_delta = 0.0;
    pi_isd1.err = 0.0;
    pi_isd1.pi_out = 0.0;
    pi_isq1.ui = 0.0; // 电流内环积分项清零
    pi_isq1.ui_delta = 0.0;
    pi_isq1.err = 0.0;
    pi_isq1.pi_out = 0.0;
    pi_isd2.ui = 0.0; // 电流内环积分项清零
    pi_isd2.ui_delta = 0.0;
    pi_isd2.err = 0.0;
    pi_isd2.pi_out = 0.0;
    pi_isq2.ui = 0.0; // 电流内环积分项清零
    pi_isq2.ui_delta = 0.0;
    pi_isq2.err = 0.0;
    pi_isq2.pi_out = 0.0;
    pi_isdz.ui = 0.0; // 电流内环积分项清零
    pi_isdz.ui_delta = 0.0;
    pi_isdz.err = 0.0;
    pi_isdz.pi_out = 0.0;
    pi_isqz.ui = 0.0; // 电流内环积分项清零
    pi_isqz.ui_delta = 0.0;
    pi_isqz.err = 0.0;
    pi_isqz.pi_out = 0.0;
    pi_isxz.ui = 0.0; // 电流内环积分项清零
    pi_isxz.ui_delta = 0.0;
    pi_isxz.err = 0.0;
    pi_isxz.pi_out = 0.0;

    // SysVVVFCtrl.fTheta_base=0;
    // vvvf_zero=0;
    // vvvf_max=0;
    // vvvf_min=0;
    // SysVVVFCtrl.fVoltageAmp=0;
    // SysVVVFCtrl.fregive=0;
    // SysVVVFCtrl.fFreqRefGive=0;
    v->wr_elec_flt = 0; // 旋变转速清零
    //v->flux_theta = 0;
    v->Vsd_ref = 0.0; // 电压清零
    v->Vsq_ref = 0.0;
    v->GenLowVolt = *pCsvParamLOWVOLt;
    //v->isd_ref = 0;
    v->isq_ref = 0;
    //v->flux_theta_cal = 0;
    v->wr_elec_flt_cal = 0.0;
    v->wr_elec_flt_est = 0.0;
    v->Vsd_comp = 0.0;
    v->Vsq_comp = 0.0;
    v->speed_pi_count = 0;   // 外环计数清零
    v->voltage_pi_count = 0; // 外环计数清零
    //======预定位标志位及计数清零============
    v->PrePositionEN = *pCsvParamPrePositionEN;
    SynMotorVc.Sensorlessmode = *pCsvParamSensorlessmode; // 1，mars起动模式；2，if起动模式；3，有感切mars启动模式
    if ((SynMotorVc.Sensorlessmode == 1 || SynMotorVc.Sensorlessmode == 2) && SysEnviConfg.uConStrat == SLVC)
    {
        v->PrePosition_Flag = 0; // 预定位标志位置0
    }
    else
    {
        if (v->PrePositionEN == 1)
        {
            v->PrePosition_Flag = 0; // 预定位标志位置0
        }
        else
        {
            // v->PrePosition_Flag = 1;
        }
    }
    v->SensorlessCtl_Flag = 0; // 无速度切换标志位置0

    v->counter = 0;              // 预定位计数值置0
    v->counter2 = 0;             // 预定位计数值置0
    Ifoward = *pCsvParamIfoward; // 标志位
    // lpf_mras_w.wc = *pCsvParamwc;
    // lpf_mras_we.wc = *pCsvParamwc;
    // lpf_mras_iq.wc = *pCsvParammarslpfw;
    // lpf_mras_id.wc = *pCsvParammarslpfw;
    // lpf_mras_iqest.wc = *pCsvParammarslpfw;
    // lpf_mras_idest.wc = *pCsvParammarslpfw;
    // lpf_mras_uq.wc = *pCsvParammarslpfw;
    // lpf_mras_ud.wc = *pCsvParammarslpfw;
    lpf_isq1.wc = *pCsvParammarslpfw;
    lpf_isq2.wc = *pCsvParammarslpfw;
    lpf_isd1.wc = *pCsvParammarslpfw;
    lpf_isd2.wc = *pCsvParammarslpfw;
    lpf_usq1.wc = *pCsvParamUdUqfiltar;
    lpf_usq2.wc = *pCsvParamUdUqfiltar;
    lpf_usd1.wc = *pCsvParamUdUqfiltar;
    lpf_usd2.wc = *pCsvParamUdUqfiltar;
    FeedforwordFlag = *pCsvParamfeedforwordFlag;
    SynMotorVc.PreId = *pCsvParamPreid_ref;
    // SysVVVFCtrl.frestep =*pCsvParamvvvfreStep;
    // ChangeMarsF = *pCsvParamChangeMarsF_ref;

    SynMotorVc.theta_preposition = *pCsvParamtheta_preposition;
    SynMotorVc.debugstep = *pCsvParamDebugstep;
    SynMotorVc.DB_com_enable = *pCsvParamDBEN;
    v->CurrentLimit = *pCsvParamCurrentLimit;
    // v->YUDINGtime = *pCsvParamyudingTIME;
    v->YUDINGtime = 4;
    v->ZhengBuChang = *pCsvParamPosCompEN;

    v->RUOCIxishu = *pCsvParamRuociXIshu;
    v->FILTERUDC = *pCsvParamFilter_udclen;
    v->GiveUdc = *pCsvParamGiveUdc;

    v->start = 0; // 矢量控制禁止
    v->Ts_dead_us = *pCsvParamDBT;
    // v->DB_com = (v->Ts_dead_us  * 0.000001 * 0.5)/ v->Ts; // 1.0全补,0.5补偿系数0.5
    // v->DB_com = (v->Ts_dead_us  * 0.0000005 )/ v->Ts; // 1.0全补,0.5补偿系数0.5
    v->DB_com = v->Ts_dead_us * 0.01;
}
//---------------------------------------------------------------
#define iq_step 0.5
float trap_data;
void Vector_ctrl_calc(Vector_ctrl *v)
{
    float temp;
    //==================有速度传感器控制，角度立即更新||无速度传感器还未切换至无速度模式====================================//
    // if ((v->SensorlessCtl == 0) || (v->SensorlessCtl_Flag == 0 && v->Sensorlessmode == 3))
    // {
    //     v->flux_theta_cal = v->flux_theta;
         v->wr_elec_flt_cal = v->wr_elec_flt;
    //     v->counter = 0;
    //     v->counter2 = 0;
    // }
    //----------------------------------------------------------------
    // if (v->PrePosition_Flag == 0 && *pCsvParamPrePositionEN == 1) // 开始预定位
    // {
    //     // 预定位参数设置
    //     const uint32_t PREPOSITION_TIME = 12;           // 总预定位时间
    //     const uint32_t RAMP_TIME = 8;                   // 斜坡增加电流时间 8s  
    //     const uint32_t PREPOSITION_COUNTS = PREPOSITION_TIME * 20000;  // 预定位总计数
    //     const uint32_t RAMP_COUNTS = RAMP_TIME * 1000;  // 斜坡计数(ms)
    //     static float ramp_id = 0;
        
    //     // 磁链角度设为0
    //     v->flux_theta_cal = 0.0f;
        
    //     // 电流处理：每1ms更新一次，实现线性增加
    //     if (v->counter % 20 == 0) {
    //         // 初始电流设置为预定位电流的一半
    //         if (v->counter2 == 0) {
    //             v->isd_ref = v->PreId * 0.5f;
    //             ramp_id = v->PreId * 0.5f / RAMP_COUNTS;
    //         }
    //         // 在设定的斜坡时间内线性增加到预定位电流
    //         else if (v->counter2 < RAMP_COUNTS) {
    //             v->isd_ref += ramp_id;
    //         }
    //         // 剩余时间保持在预定位电流
    //         else {
    //             v->isd_ref = v->PreId;
    //         }
    //     }
        
    //     v->isq_ref = 0.0f;  // q轴电流保持为0
        
    //     // 计数更新逻辑
    //     v->counter++;
    //     if (v->counter >= 20) {  // 每1ms更新一次
    //         v->counter2++;
    //         v->counter = 0;
    //     }
        
    //     // 预定位结束判断
    //     if (v->counter2 >= PREPOSITION_COUNTS / 20) {
    //         v->PrePosition_Flag = 1;  // 预定位结束
    //         *pCsvParamPrePositionEN = 0;
    //         v->counter = 0;
    //         v->counter2 = 0;
    //         ramp_id = 0;  // 重置斜坡增量
    //         // 在定位完成要对所有变量进行初始化;
    //         v->flux_theta_est = 0;
    //         v->flux_theta_cal = 0;
    //         v->wr_elec_flt_cal = 0;
    //         v->wr_elec_flt_est = 0;
    //         v->Vsd_comp = 0;
    //         v->Vsq_comp = 0;
    //         v->Vsd_ref = 0;
    //         v->Vsq_ref = 0;
    //         v->wr_elec_flt = 0; // 旋变转速清零
    //                             // PI变量清零
    //         pi_speed.ui = 0.0;  // 转速外环积分项清零
    //         pi_speed.ui_delta = 0.0;
    //         pi_speed.err = 0.0;
    //         pi_speed.pi_out = 0.0;
    //         pi_isd.ui = 0.0; // 电流内环积分项清零
    //         pi_isd.ui_delta = 0.0;
    //         pi_isd.err = 0.0;
    //         pi_isd.pi_out = 0.0;
    //         pi_isq.ui = 0.0; // 电流内环积分项清零
    //         pi_isq.ui_delta = 0.0;
    //         pi_isq.err = 0.0;
    //         pi_isq.pi_out = 0.0;
    //         RotorSpeedclc.uLastPosition = 0;
    //         RotorSpeedclc.uLastLastPosition = 0;
    //         RotorSpeedclc.uLastLastLastPosition = 0;
    //         RotorSpeedclc.uDeltaPosition = 0;
    //         RotorSpeedclc.uLastDeltaPosition = 0; // 2020521增加
    //         RotorSpeedclc.uSumDelta = 0;
    //         RotorSpeedclc.speedave_count = 0;
    //         RotorSpeedclc.speed_rpm_last = 0;
    //         RotorSpeedclc.speed_rpm = 0;
    //         RotorSpeedclc.speed_Mrpm = 0;
    //         RotorSpeedclc.speed_Trpm = 0;
    //         RotorSpeedclc.speedclc_count = 0;
    //         RotorSpeedclc.speedclc_flag = 0;
    //         RotorSpeedclc.speed_direction = 0;
    //         RotorSpeedclc.speedclc_offset = 0;
    //         lpf_w.y = 0;
    //         lpf_w.y_old = 0;
    //         lpf_isd.y = 0;
    //         lpf_isd.y_old = 0;
    //         lpf_isd1.y = 0;
    //         lpf_isd1.y_old = 0;
    //         lpf_isd2.y = 0;
    //         lpf_isd2.y_old = 0;
    //         lpf_isq.y = 0;
    //         lpf_isq.y_old = 0;
    //         lpf_isq1.y = 0;
    //         lpf_isq1.y_old = 0;
    //         lpf_isq2.y = 0;
    //         lpf_isq2.y_old = 0;
    //         lpf_usd1.y = 0;
    //         lpf_usd1.y_old = 0;
    //         lpf_usd2.y = 0;
    //         lpf_usd2.y_old = 0;
    //         lpf_usq1.y = 0;
    //         lpf_usq1.y_old = 0;
    //         lpf_usq2.y = 0;
    //         lpf_usq2.y_old = 0;
    //         lpf_isq_ref.y = 0;
    //         lpf_isq_ref.y_old = 0;

    //         if ((v->SensorlessCtl == 0) || (v->Sensorlessmode == 3)) // 有感记录位置 或 有感切mars起动
    //         {
    //             tmr_counter_value_set(TMR3, 0);
    //             v->theta_preposition = 0;
    //             *pCsvParamtheta_preposition = v->theta_preposition;
    //         }
    //     }
    // }
    //==============================根据当前工作状态来执行矢量控制=====================//
    // Starter Mode带速度传感器的矢量控制
    v->flux_theta_cal = v->flux_theta;
    if (v->control_method == 1 )
    {
        // if (v->Sensorlessmode == 2 ) // IF iq给定
        // {
        //     v->isd_ref = 0;
        //     temp = v->isq_ref - v->IFiq; // 上位机给定iq
        //     if (temp > v->debugstep)
        //     {
        //         v->isq_ref -= v->debugstep;
        //     }
        //     else if (temp < v->debugstep)
        //     {
        //         v->isq_ref += v->debugstep;
        //     }
        //     else
        //     {
        //         v->isq_ref = v->IFiq;
        //     }
        // }
        if (v->controlLoopMode == 0) // 速度闭环
        {
            v->speed_pi_count++;
            if (v->speed_pi_count >= 8) // 控制周期10个采样周期，10*0.05ms,0.5ms执行一次
            {

                pi_speed.pi_fdb = SynMotorVc.speedback_flt;
                pi_speed.pi_ref = SynMotorVc.speed_ref*0.01666667f; // 转速给定值

                // pi_speed.calc(&pi_speed); // PI计算
                pi_speed_inc_fun_calc(&pi_speed);
                //PI_Speed_Incremental_Calc(&pi_speed);
                //_pi_speed_inc_fun_calc(&pi_speed, *pCsvParamSpeed_ref, SynMotorVc.speedback_flt);
                v->speed_pi_count = 0;

                v->isd_ref = *pCsvParamId_ref;               // id=0控制方式
                v->isq_ref = pi_speed.pi_out; // 速度换PI OUT
                // v->isq_ref = *pCsvParamIq_ref;
                // float temp = *pCsvParamSynVcIqmax;
                // // 使用无分支预测优化
                // float upper_limit = (v->isq_ref > temp) ? temp : v->isq_ref;
                // float lower_limit = (upper_limit < -temp) ? -temp : upper_limit;
                // v->isq_ref = lower_limit;
               // v->isq_ref = *pCsvParamIq_ref;
            }
            /*弱磁控制环， d轴电流给定*/
        //        if(v->fluxweaken_pi_count > 10)  //弱磁控制周期
        //         {
        //                 pi_flux_w.pi_fdb = v->Us_ref ;
        //                 if(v->FILTERUDC==0)
        //                 {pi_flux_w.pi_ref = v->GiveUdc*v->RUOCIxishu*0.57735027f;}
        //                 else
        //                 {
        //                  //pi_flux_w.pi_ref = Filter_udcl.y*v->RUOCIxishu*0.57735027f;
        //                 }
        //                 pi_flux_w.calc(&pi_flux_w);
        //                 v->fluxweaken_pi_count = 0;
        //         }
        //         v->fluxweaken_pi_count++;
        //                    v->isd_ref=v->PreId;    //id给定值；
        //     v->isd_ref = pi_flux_w.pi_out; //弱磁控制输出id
        //     //temp = sqrt(v->CurrentLimit * v->CurrentLimit - v->isd_ref * v->isd_ref); // 最大电流限幅
        //     arm_sqrt_f32(v->CurrentLimit * v->CurrentLimit - v->isd_ref * v->isd_ref, &temp);
        //    if(v->isq_ref > temp )
        //     {
        //       v->isq_ref=temp;
        //     }
        }
        else if (v->controlLoopMode == 1) // 电流环
        {
            v->flux_theta_cal = *pCsvParamSetAngle * 0.0174533f;
            v->isd_ref = *pCsvParamId_ref;
            v->isq_ref = *pCsvParamIq_ref;; // 调id电流环PI，设置iq=0;
            // pi_isq1.Kp = 0.0f;
            // pi_isq1.Ki = 0.0f;
            // v->flux_theta_cal = 0.0f;
            // v->isq_ref = *pCsvParamIq_ref;
            // v->isd_ref = 0;  //调id电流环PI，设置iq=0;
            // pi_isd1.Kp = 0.0f;
            // pi_isd1.Ki = 0.0f;
            // v->flux_theta_cal = 0.0f;
        }
    }

    if (v->controlLoopMode == 2)
    {
        // 更新开环频率控制器
        OpenLoopFreqCtrl_Update(&gOpenLoopFreqCtrl);
        
        // 使用开环频率控制器生成的角度
       // v->flux_theta_cal = gOpenLoopFreqCtrl.angle;
        
        // 获取q轴电流给定
        // v->isq_ref = *pCsvParamIq_ref;
        // float temp = *pCsvParamSynVcIqmax;
        // // 使用无分支预测优化
        // float upper_limit = (v->isq_ref > temp) ? temp : v->isq_ref;
        // float lower_limit = (upper_limit < -temp) ? -temp : upper_limit;
        // v->isq_ref = lower_limit;
        v->sin_theta = arm_sin_f32(v->flux_theta_cal); // 计算正弦
        v->cos_theta = arm_cos_f32(v->flux_theta_cal); // 计算余弦

        ipark1.de = 0.0f;
        ipark1.qe = *pCsvParamIq_ref;
        // 开环频率控制

        /*第一套绕组*/
        ipark1.sin_ang = v->sin_theta;
        ipark1.cos_ang = v->cos_theta;

        // ipark1.de = v->Vsd_ref_new;
        // ipark1.qe = v->Vsq_ref_new;
        ipark1.calc(&ipark1);
        iclarke2.ds = ipark1.ds;
        iclarke2.qs = ipark1.qs;
        // SVPWM(ipark1.ds, ipark1.qs, 60.0f);
        SVPWM(ipark1.ds, ipark1.qs, v->GiveUdc);
    }
    if (v->controlLoopMode == 3)
    {
        ipark1.de = 3.0f;
        ipark1.qe = 0.0f;
        v->flux_theta_cal = *pCsvParamSetAngle * 0.0174533f;
        ipark1.sin_ang = arm_sin_f32(v->flux_theta_cal);
        ipark1.cos_ang = arm_cos_f32(v->flux_theta_cal);
        ipark1.calc(&ipark1);
        iclarke2.ds = ipark1.ds;
        iclarke2.qs = ipark1.qs;
        SVPWM(ipark1.ds, ipark1.qs, 60.0f);
    }
     
    clarke1.as = lpf_filter(&Ia_flt, v->Isa1_SI);  // U相电流滤波
    clarke1.bs = lpf_filter(&Ib_flt, v->Isb1_SI);  // V相电流滤波
    clarke1.cs = lpf_filter(&Ic_flt, v->Isc1_SI);  // W相电流滤波 
    if ((v->controlLoopMode == 0)|| (v->controlLoopMode == 1))
    {
        /*提前计算角度正余弦*/
    v->sin_theta = arm_sin_f32(v->flux_theta_cal); // 计算正弦
    v->cos_theta = arm_cos_f32(v->flux_theta_cal); // 计算余弦
    //==================转速参考指令换算为rpm==========================================//
    // v->speed_ref = v->speed_ref_Hz*60/para.p;                //给定的是电频率，转化为rpm
    //=============================== 电流的clarke坐标变换   实际值 ========================//
    // /********  滤波模块 ************/

    // clarke1.as = v->Isa1_SI;  // U相电流滤波
    // clarke1.bs = v->Isb1_SI;  // V相电流滤波
    // clarke1.cs = v->Isc1_SI;  // W相电流滤波
    clarke1.calc(&clarke1);

    //=============================== 电流的park坐标变换    实际值 =========================//
    //=======双-dq控制方法坐标变换
    park1.ds = clarke1.ds; // 第一套绕组的alph电流
    park1.qs = clarke1.qs; // 第一套绕组的bet电流
    park1.sin_ang = v->sin_theta;
    park1.cos_ang = v->cos_theta;
    park1.calc(&park1);
    v->isd1 = park1.de;
    v->isq1 = park1.qe;
    // v->isd1 =  lpf_filter(&Id1_flt, park1.de);
    // v->isq1 = lpf_filter(&Iq1_flt, park1.qe); // 第一套绕组的dq电流
    //==================================电流同步滤波===================================//
    /*电流的同步滤波，与死区补偿相关*/
    //    lpf_isd.x = park1.de;
    //    lpf_isq.x = park1.qe;
    //    lpf_isd.calc(&lpf_isd);
    //    lpf_isq.calc(&lpf_isq);
    //=========双三相死区补偿
    /****** 等待确定，是否需要滤波  *******************/
    // lpf_isd1.x = v->isd1;
    // lpf_isq1.x = v->isq1;
    // lpf_isd1.calc(&lpf_isd1);
    // lpf_isq1.calc(&lpf_isq1);

    // //=============================== 电流的反park坐标变换    实际值 =========================//
    // //=========双三相死区
    // ipark2.de = lpf_isd1.y;
    // ipark2.qe = lpf_isq1.y;
    // ipark2.ang = v->flux_theta_cal;
    // ipark2.sin_ang = v->sin_theta;
    // ipark2.cos_ang = v->cos_theta;
    // ipark2.calc(&ipark2);
    // //=======================转换为滤波后的三相电流=====================================//
    // iclarke1.ds = ipark2.ds;
    // iclarke1.qs = ipark2.qs;
    // iclarke1.calc(&iclarke1);
    // v->Isa1_flt = iclarke1.as;
    // v->Isb1_flt = iclarke1.bs;
    // v->Isc1_flt = iclarke1.cs;
    /**计算弱磁 */
    //v->isd_ref = WeakFluxCtr(v->GiveUdc*0.54848f, v->VSdq_ref,0.25f);
    //======================2个电流PI=====================================================//
    /*定子D轴电流闭环*/       
    pi_isd1.pi_fdb = v->isd1;    // id电流反馈值
    pi_isd1.pi_ref = v->isd_ref; // id电流给定值
    pi_isd1.calc(&pi_isd1);      // PI计算

    /*定子Q轴电流闭环*/
    // arm_sqrt_f32(pi_speed.pi_out_max * pi_speed.pi_out_max - v->isd_ref * v->isd_ref, &limit_iq_ref);
    // if(v->isq_ref >= limit_iq_ref)
    // {
    //     v->isq_ref = limit_iq_ref;
    // }
    pi_isq1.pi_fdb = v->isq1;    // iq电流反馈值
    pi_isq1.pi_ref = v->isq_ref; // iq电流给定值
    pi_isq1.calc(&pi_isq1);      // PI计算     //第一套绕组电流PI环
    arm_sqrt_f32(pi_isd1.pi_out * pi_isd1.pi_out + pi_isq1.pi_out * pi_isq1.pi_out, &v->VSdq_ref);  //计算输出电压幅值
    //======================交叉解耦项=====================================================//
    // v->Vsd1_comp = -v->isq_ref * para.Lq * v->wr_elec_flt_cal;
    // v->Vsq1_comp = (v->isd_ref * para.Ld + para.flux_r) * v->wr_elec_flt_cal;
    // v->Vsd2_comp = -v->isq_ref * para.Lq * v->wr_elec_flt_cal;
    // v->Vsq2_comp = (v->isd_ref * para.Ld + para.flux_r) * v->wr_elec_flt_cal; // 使用参考值做前馈补偿信号值

    if (FeedforwordFlag == 1.0)
    {
        v->Vsd1_comp = -v->isq_ref * para.Lq * v->wr_elec_flt_cal;
        v->Vsq1_comp = (v->isd_ref * para.Ld + para.flux_r) * v->wr_elec_flt_cal;
        /*加入前馈*/
        v->Vsd1_ref = pi_isd1.pi_out + v->Vsd1_comp; // 输出赋值
        v->Vsq1_ref = pi_isq1.pi_out + v->Vsq1_comp; // 输出赋值
    }
    else
    {
        /*无前馈直接输出PI结果*/
        v->Vsd1_ref = pi_isd1.pi_out; // 输出赋值
        v->Vsq1_ref = pi_isq1.pi_out; // 输出赋值
    }

    //========================逆变换求三相调制电压========================//
    ipark1.de = v->Vsd1_ref;
    ipark1.qe = v->Vsq1_ref;
    // 开环频率控制

    /*第一套绕组*/
    ipark1.sin_ang = v->sin_theta;
    ipark1.cos_ang = v->cos_theta;

    // ipark1.de = v->Vsd_ref_new;
    // ipark1.qe = v->Vsq_ref_new;
    ipark1.calc(&ipark1);
    // iclarke2.ds = ipark1.ds;
    // iclarke2.qs = ipark1.qs;
    /******上位机波形******/
    
#ifdef _SVPWM_
    SVPWM(ipark1.ds, ipark1.qs, v->GiveUdc);
    //SVPWM(ipark1.ds, ipark1.qs, 60.0f);


#else

    /*第二套绕组*/
    // ipark1.sin_ang = v->sin_theta;
    // ipark1.cos_ang = v->cos_theta;

    // ipark1.de = v->Vsd2_ref;
    // ipark1.qe = v->Vsq2_ref;

    // ipark1.calc(&ipark1);
    // iclarke2.ds = ipark1.ds;
    // iclarke2.qs = ipark1.qs;
    // iclarke2.calc(&iclarke2);
    // v->Ua2_ref = iclarke2.as;
    // v->Ub2_ref = iclarke2.bs;
    // v->Uc2_ref = iclarke2.cs;                //求三相静止坐标系电压值

    ////////////////////////////////////  ===================================================以下涉及到PWM调制与FPGA通讯部分
    // SysVoltBase.fUu2_base = v->Ua2_ref ;//U相电压
    // SysVoltBase.fUv2_base = v->Ub2_ref ;//V相电压
    // SysVoltBase.fUw2_base = v->Uc2_ref ;//W相电压
    SysVoltBase.fUu1_base = v->Ua1_ref; // U相电压
    SysVoltBase.fUv1_base = v->Ub1_ref; // V相电压
    SysVoltBase.fUw1_base = v->Uc1_ref; // W相电压
    // 零序电压注入（第一套绕组零序电压注入）
    v_max1 = SysVoltBase.fUu1_base;
    if (SysVoltBase.fUu1_base < SysVoltBase.fUv1_base)
    {
        v_max1 = SysVoltBase.fUv1_base;
        if (SysVoltBase.fUv1_base < SysVoltBase.fUw1_base)
        {
            v_max1 = SysVoltBase.fUw1_base;
        }
    }
    else
    {
        if (SysVoltBase.fUu1_base < SysVoltBase.fUw1_base)
        {
            v_max1 = SysVoltBase.fUw1_base;
        }
    }

    v_min1 = SysVoltBase.fUu1_base;
    if (SysVoltBase.fUu1_base > SysVoltBase.fUv1_base)
    {
        v_min1 = SysVoltBase.fUv1_base;
        if (SysVoltBase.fUv1_base > SysVoltBase.fUw1_base)
        {
            v_min1 = SysVoltBase.fUw1_base;
        }
    }
    else
    {
        if (SysVoltBase.fUu1_base > SysVoltBase.fUw1_base)
        {
            v_min1 = SysVoltBase.fUw1_base;
        }
    }
    // 零序电压注入（第二套绕组电压计算）
    //  v_max2=SysVoltBase.fUu2_base;
    //  if(SysVoltBase.fUu2_base < SysVoltBase.fUv2_base)
    //      {
    //      v_max2=SysVoltBase.fUv2_base;
    //          if(SysVoltBase.fUv2_base < SysVoltBase.fUw2_base)
    //          {
    //              v_max2=SysVoltBase.fUw2_base;
    //          }

    //     }
    // else
    // {
    //     if(SysVoltBase.fUu2_base < SysVoltBase.fUw2_base)
    //        {
    //         v_max2=SysVoltBase.fUw2_base;
    //        }
    // }

    // v_min2=SysVoltBase.fUu2_base;
    // if(SysVoltBase.fUu2_base > SysVoltBase.fUv2_base)
    //     {
    //     v_min2=SysVoltBase.fUv2_base;
    //         if(SysVoltBase.fUv2_base > SysVoltBase.fUw2_base)
    //         {
    //             v_min2=SysVoltBase.fUw2_base;
    //         }

    //     }
    // else
    // {
    //     if(SysVoltBase.fUu2_base > SysVoltBase.fUw2_base)
    //        {
    //         v_min2=SysVoltBase.fUw2_base;
    //        }
    // }
    v_zero1 = -0.5 * (v_max1 + v_min1);
    // v_zero2=-0.5*(v_max2+v_min2);
    ///////////////////////////////////////
    // v->Vdc_inv=2/Filter_udcl.y;
    if (v->FILTERUDC == 0)
    {
        // v->Vdc_inv=2/v->GiveUdc;  //SPWM可输出最大电压为Udc/2
        float _sqrt_3_;
        arm_sqrt_f32(3, &_sqrt_3_);
        v->Vdc_inv = _sqrt_3_ / v->GiveUdc;
    }
    // if(v->ZhengBuChang>0)
    if (1)
    {
        SysVoltBase.fUu1_base = ((SysVoltBase.fUu1_base + v_zero1) * v->Vdc_inv + 1.0f) * 0.5f; // U相电压
        SysVoltBase.fUv1_base = ((SysVoltBase.fUv1_base + v_zero1) * v->Vdc_inv + 1.0f) * 0.5f; // V相电压
        SysVoltBase.fUw1_base = ((SysVoltBase.fUw1_base + v_zero1) * v->Vdc_inv + 1.0f) * 0.5f; // W相电压

        // SysVoltBase.fUu1_base = ((SysVoltBase.fUu1_base+v_zero1+v->Vsq1_ref) * v->Vdc_inv)/2;//U相电压
        // SysVoltBase.fUv1_base = ((SysVoltBase.fUv1_base+v_zero1+v->Vsq1_ref) * v->Vdc_inv)/2;//V相电压
        // SysVoltBase.fUw1_base = ((SysVoltBase.fUw1_base+v_zero1+v->Vsq1_ref) * v->Vdc_inv)/2;//W相电压
        //      SysVoltBase.fUu2_base = (SysVoltBase.fUu1_base+v_zero1) * v->Vdc_inv;//U相电压
        //      SysVoltBase.fUv2_base = (SysVoltBase.fUv1_base+v_zero1) * v->Vdc_inv;//V相电压
        //      SysVoltBase.fUw2_base = (SysVoltBase.fUw1_base+v_zero1) * v->Vdc_inv;//W相电压
    }
    else
    {
        // SysVoltBase.fUu1_base = (SysVoltBase.fUu1_base) * v->Vdc_inv;//U相电压
        // SysVoltBase.fUv1_base = (SysVoltBase.fUv1_base) * v->Vdc_inv;//V相电压
        // SysVoltBase.fUw1_base = (SysVoltBase.fUw1_base) * v->Vdc_inv;//W相电压
        SysVoltBase.fUu1_base = ((SysVoltBase.fUu1_base) * v->Vdc_inv + 1.0f) * 0.5f; // U相电压
        SysVoltBase.fUv1_base = ((SysVoltBase.fUv1_base) * v->Vdc_inv + 1.0f) * 0.5f; // V相电压
        SysVoltBase.fUw1_base = ((SysVoltBase.fUw1_base) * v->Vdc_inv + 1.0f) * 0.5f; // W相电压
        // SysVoltBase.fUu2_base = (SysVoltBase.fUu2_base) * v->Vdc_inv;//U相电压
        // SysVoltBase.fUv2_base = (SysVoltBase.fUv2_base) * v->Vdc_inv;//V相电压
        // SysVoltBase.fUw2_base = (SysVoltBase.fUw2_base) * v->Vdc_inv;//W相电压
    }

    //===============================死区补偿=====================================//
    if (v->DB_com_enable == 1)
    {
        if (v->Isa1_flt > 0)
        {
            SysVoltBase.fUu1_base = SysVoltBase.fUu1_base + v->DB_com;
        }
        else if (v->Isa1_flt < 0)
        {
            SysVoltBase.fUu1_base = SysVoltBase.fUu1_base - v->DB_com;
        }

        if (v->Isb1_flt > 0)
        {
            SysVoltBase.fUv1_base = SysVoltBase.fUv1_base + v->DB_com;
        }
        else if (v->Isb1_flt < 0)
        {
            SysVoltBase.fUv1_base = SysVoltBase.fUv1_base - v->DB_com;
        }

        if (v->Isc1_flt > 0)
        {
            SysVoltBase.fUw1_base = SysVoltBase.fUw1_base + v->DB_com;
        }
        else if (v->Isc1_flt < 0)
        {
            SysVoltBase.fUw1_base = SysVoltBase.fUw1_base - v->DB_com;
        }
        //------第二套
        //    if (v->Isa2_flt > 0)
        //    {
        //        SysVoltBase.fUu2_base = SysVoltBase.fUu2_base + v->DB_com;
        //    }
        //    else if (v->Isa2_flt < 0)
        //    {
        //        SysVoltBase.fUu2_base = SysVoltBase.fUu2_base - v->DB_com;
        //    }

        //    if (v->Isb2_flt > 0)
        //    {
        //        SysVoltBase.fUv2_base = SysVoltBase.fUv2_base + v->DB_com;
        //    }
        //    else if (v->Isb2_flt < 0)
        //    {
        //        SysVoltBase.fUv2_base = SysVoltBase.fUv2_base - v->DB_com;
        //    }

        //    if (v->Isc2_flt > 0)
        //    {
        //        SysVoltBase.fUw2_base = SysVoltBase.fUw2_base + v->DB_com;
        //    }
        //    else if (v->Isc2_flt < 0)
        //    {
        //        SysVoltBase.fUw2_base = SysVoltBase.fUw2_base - v->DB_com;
        //    }  //------需要设置FPGA对应程序
    }
    // SysVoltBase.fUu1_base = (1 - SysVoltBase.fUu1_base)/2;
    // SysVoltBase.fUv1_base = (1 - SysVoltBase.fUv1_base)/2;
    // SysVoltBase.fUw1_base = (1 - SysVoltBase.fUw1_base)/2;

    gMotorData.f1.svpwm_wave_a = SysVoltBase.fUu1_base;
    gMotorData.f1.svpwm_wave_b = SysVoltBase.fUv1_base;
    gMotorData.f1.svpwm_wave_c = SysVoltBase.fUw1_base;

    UpdatePwm(SysVoltBase.fUu1_base, SysVoltBase.fUv1_base, SysVoltBase.fUw1_base);
#endif
    }
}
//======================================================================================================================================//
void Para_derive(Parameter_set *p)
{
    //-----------------------------------------//
    //-----------------------------------------//
    p->UP_N = p->UL_N * 0.5773672;           // 额定相电压
    p->wmeca_N = DPI * p->rpm_N * 0.0166667; // 额定机械转速
    p->ws_N = DPI * p->F_N;                  // 同步角速度
    p->T_N = p->P_N / p->wmeca_N;            // 额定转矩
    //-----------------------------------------//

    p->Umax = p->UP_N * 1.414; // 最大电压设置为额定相电压的幅值
    //    p->Udq_max = p->UP_N*1.414*1.22474487;      //设置为线电压的有效值
    p->Udq_max = *pCsvParamSynVcUqmax; // 260.0;      //设置为线电压的有效值
    //    p->Udq_max =260.0;
    //  p->Idq_max = p->I_N*1.414*1.2247*2.0;
    p->Imax = p->I_N * 1.414 * 1.5; // 1.5对应允许过流倍数，电流幅值          //倍数可以修改
    //    p->Idq_max = p->Imax*1.5;                   //电流幅值
    p->Idq_max = *pCsvParamSynVcIqmax;
    //    p->Idq_max = 20.0;                   //电流幅值
    p->Torque_max = p->T_N * 1.5; // 最大转矩     1.5倍额定转矩                     //倍数可以修改
    p->Freq_max = p->F_N * 1.5;   // 最大频率                                  1.5倍900Hz
    p->flux_r = p->flux_r_N;      // 转子磁链更新
    p->flux_max = p->flux_r_N;    // 最大磁链直接赋磁链额定值
    //---------------------------//
    p->BASE_U = p->UP_N * 1.414;                                    // 电压标幺值为相电压的幅值
    p->BASE_I = p->I_N * 1.414;                                     // 电流标幺值为相电流的幅值
    p->BASE_Power = p->BASE_U * p->BASE_I;                          // 功率标幺值
    p->BASE_FREQ = p->F_N;                                          // 频率标幺值
    p->BASE_w = DPI * p->BASE_FREQ;                                 // 角频率标幺值
    p->BASE_time = 1.0 / p->BASE_w;                                 // 时间标幺值
    p->BASE_Torque = p->BASE_Power * p->BASE_time;                  // 转矩标幺值
    p->BASE_Flux = p->BASE_U * p->BASE_time;                        // 磁链标幺值
    p->BASE_R = p->BASE_U / p->BASE_I;                              // 电阻标幺值
    p->BASE_L = p->BASE_R * p->BASE_time;                           // 电感标幺值
    p->BASE_Interia = p->BASE_Torque * p->BASE_time * p->BASE_time; // p->BASE_Torque*p->BASE_time/p->BASE_w;
    p->BASE_I_inv = 1.0 / p->BASE_I;
    p->BASE_U_inv = 1.0 / p->BASE_U;
    //---------------求取标幺值------------//
    p->rs = p->Rs / p->BASE_R;
    p->ls = p->Lsd / p->BASE_L;

    p->ld = p->Ld / p->BASE_L;
    p->lq = p->Lq / p->BASE_L;

    p->flux_r_pu = p->flux_r / p->BASE_Flux;

    p->inertia_pu = p->Inertia / p->BASE_Interia;
    p->friction_pu = p->friction * p->BASE_w / (p->BASE_Torque); // p->friction/(p->BASE_Torque/p->BASE_w);
    p->wc_wr_pu = p->Wc_wr / p->BASE_w;
    p->wc_cur_pu = p->Wc_cur / p->BASE_w;

    p->Umax_pu = p->Umax / p->BASE_U;
    p->Imax_pu = p->Imax / p->BASE_I;
    p->Udq_max_pu = p->Udq_max / p->BASE_U;
    p->Idq_max_pu = p->Idq_max / p->BASE_I;
    p->Torque_max_pu = p->Torque_max / p->BASE_Torque;
    p->flux_max_pu = p->flux_max / p->BASE_Flux;
    p->ts = p->Ts / p->BASE_time;
    p->freq_max_pu = p->Freq_max / p->BASE_FREQ;
}

//============================park变换========================================//
void park_calc(PARK *v)
{
    v->de = v->ds * v->cos_ang + v->qs * v->sin_ang;
    v->qe = v->qs * v->cos_ang - v->ds * v->sin_ang;
}
//===================================反Park变换================================//
void ipark_calc(IPARK *v)
{
    /* Using look-up IQ sine table */
    v->ds = v->de * v->cos_ang - v->qe * v->sin_ang;
    v->qs = v->qe * v->cos_ang + v->de * v->sin_ang;
}
//==================================Clarke变换=================================//
// const float CLARK_RATIO = 0.57735023f;
void clarke_calc(CLARKE *v)
{
    //************************恒功率变换*******************************************//
    //    v->ds = 0.81649658*(v->as - 0.5*v->bs - 0.5* v->cs);
    //    v->qs = 0.81649658*(0.8660254*v->bs - 0.8660254*v->cs);
    //************************恒幅值变换*******************************************//
    // v->ds = 0.66666667 * (v->as - 0.5 * v->bs - 0.5 * v->cs);
    // v->qs = 0.66666667 * (0.8660254 * v->bs - 0.8660254 * v->cs);
    v->ds = v->as;
    v->qs = (v->bs - v->cs) * 0.57735023f;
}
//==================================反Clarke变换================================//
// const float ICLARK_SQRT3 = 1.7320508f;
const float ICLARK_SQRT3 = 0.8660254f;
const float ICLARK_RATIO = 0.5f;
void iclarke_calc(ICLARKE *v)
{
    //************************恒功率变换*******************************************//
    //    v->as = 0.81649658*v->ds;
    //    v->bs = 0.81649658*(-0.5*v->ds + 0.8660254*v->qs);
    //    v->cs = 0.81649658*(-0.5*v->ds - 0.8660254*v->qs);
    //************************恒幅值变换*******************************************//
    v->as = v->ds;
    // v->bs = -0.5 * v->ds + 0.8660254 * v->qs;
    // v->cs = -0.5 * v->ds - 0.8660254 * v->qs;
    v->bs = -ICLARK_RATIO * v->ds + ICLARK_SQRT3 * v->qs;
    v->cs = -ICLARK_RATIO * v->ds - ICLARK_SQRT3 * v->qs;
    // v->bs = ICLARK_RATIO * (-v->ds + ICLARK_SQRT3 * v->qs);
    // v->cs = -ICLARK_RATIO * (v->ds + ICLARK_SQRT3 * v->qs);
}

float lpf_filter(LPF_TypeDef *v, float x)
{
    v->filter_vlaue = v->a * x + (1.0f - v->a) * v->last_value;
    v->last_value = v->filter_vlaue;
    return v->filter_vlaue;
}

//------------------------------------------------------------
void lpfl_calc(LPFL *v)
{
    v->y += v->Tc * (v->wc * (v->x - v->y_old));
    v->y_old = v->y;
}
//-------------------------------------------------------------
void lpfi_calc(LPFI *v)
{
    // v->y = v->y + v->Tc * (v->x - (v->wc * v->y_old));
    // v->y_old = v->y;
    float tmp1, tmp2;
    tmp1 = v->x - v->y_old;
    tmp2 = v->Tc * v->wc;
    tmp2 = tmp2 * tmp1;
    v->y += tmp2;
    // v->y += v->Tc * v->wc* (v->x - v->y_old);
    v->y_old = v->y;
}
//=============================转速环PI控制器参数计算==============================//
void pi_speed_const_calc(PI_SPEED_CONST *v)
{
    float taun = v->h / v->wc_wr;
    v->Kp = (v->h + 1.0) / (2.0 * taun) * v->inertia;
    v->Ki = v->Kp / taun;
}
//=============================电流环PI控制器参数计算===============================//
void pi_current_const_calc(PI_CURRENT_CONST *v)
{
    v->Kp = v->wc_current * v->ls;
    v->Ki = v->wc_current * v->rs; // 常规的方法
}
//==============================速度环PI=======================================//
void pi_speed_calc(PI_SPEED *v)
{
    v->err = v->pi_ref - v->pi_fdb;
    /* Integral segment */
    v->ui_delta = v->err * v->Ki * v->Tc;
    v->ui += v->ui_delta;
    /* Proportional segment */
    v->up = v->Kp * v->err;
    /* Proportional and integral repeated addition */
    v->pi_out = v->ui + v->up;
    if (v->pi_out > v->pi_out_max)
    {
        v->pi_out = v->pi_out_max;
        v->ui -= v->ui_delta; /* Stop integral when saturation */
    }
    else if (v->pi_out < -v->pi_out_max)
    {
        v->pi_out = -v->pi_out_max;
        v->ui -= v->ui_delta; /* Stop integral when saturation */
    }
}

void pi_speed_inc_fun_calc(PI_SPEED *v)
{
    float delta;
    float tmp;
    v->err = v->pi_ref - v->pi_fdb;
    tmp = v->pi_ref * 0.2f;
    v->ui_delta = v->Ki * v->err;
    // if(v->err <= tmp && v->err >= -tmp)
    // {
    //     /* Integral segment */
        
    // }
    // else
    // {
    //     v->ui_delta = 0.0f;
    // }
   

    /* Proportional segment */
    v->up = v->Kp * (v->err - v->err1);
    /* Proportional and integral repeated addition */
    
    delta = v->ui_delta + v->up;
    v->pi_out += delta;
    v->err1 = v->err;
    if (v->pi_out > v->pi_out_max)
    {
        v->pi_out = v->pi_out_max;
        
        //v->ui -= v->ui_delta; /* Stop integral when saturation */
    }
    else if (v->pi_out < -v->pi_out_max)
    {
         v->pi_out = -v->pi_out_max;
        
        // v->ui -= v->ui_delta; /* Stop integral when saturation */
    }
}

// 第二个实现的改进版本，添加抗饱和处理
static inline float _pi_speed_inc_fun_calc(PI_SPEED *v, float ref, float fdb) {
    float kp = v->Kp;
    float ki = v->Ki;
    float err1 = v->err1;
    float max_out = v->pi_out_max;
    float pi_out = v->pi_out;
    
    float err = ref - fdb;
    float ki_err = ki * err;  // 保存积分增量
    float delta = (kp + ki) * err - kp * err1;
    
    float new_pi_out = pi_out + delta;
    
    // 检测饱和并进行处理
    if (new_pi_out > max_out) {
        new_pi_out = max_out;
        delta -= ki_err;  // 减去积分增量，实现抗饱和
    }
    else if (new_pi_out < -max_out) {
        new_pi_out = -max_out;
        delta -= ki_err;  // 减去积分增量，实现抗饱和
    }
    
    v->err1 = err;
    v->pi_out = new_pi_out;
    
    return new_pi_out;
}

//======================================================================================================================================//
// void pi_fun_calc(PI_fun *v)
// {
//     // v->err = v->pi_ref - v->pi_fdb;

//     // /* Integral segment */
//     // v->ui_delta = v->Tc * v->Ki * v->err;

//     // /* if ( v->ui_delta > v->pi_var_max )  v->ui_delta = v->pi_var_max;
//     // else if (v->ui_delta < -v->pi_var_max ) v->ui_delta = -v->pi_var_max; */

//     // v->ui += v->ui_delta;

//     // /* Proportional segment */
//     // v->up = v->Kp * v->err;
//     // /*
//     //     if ( v->up > v->pi_var_max)     v->up = v->pi_var_max;
//     //     else if ( v->up< -v->pi_var_max )   v->up = -v->pi_var_max;
//     // */
//     // /* Proportional and integral repeated addition */
//     // v->pi_out = v->ui + v->up;
//     v->err = v->pi_ref - v->pi_fdb;

//     /* Integral segment */
//     v->ui_delta = v->Ki * v->err;

//     /* Proportional segment */
//     v->up = v->Kp * (v->err - v->err1);
//     /* Proportional and integral repeated addition */
//     v->pi_out += v->ui_delta + v->up;
//     v->err1 = v->err;
//     if (v->pi_out > v->pi_out_max)
//     {
//         v->pi_out = v->pi_out_max;
//         v->ui -= v->ui_delta; /* Stop integral when saturation */
//     }
//     else if (v->pi_out < -v->pi_out_max)
//     {
//         v->pi_out = -v->pi_out_max;
//         v->ui -= v->ui_delta; /* Stop integral when saturation */
//     }
// }

void pi_fun_calc(PI_fun *v)
{
    v->err = v->pi_ref - v->pi_fdb;

    /* 比例项计算 */
    v->up = v->Kp * v->err;
    
    /* 积分项计算*/
    float ui_delta = v->Ki * v->err;
    
    // /* 积分增量限幅 (可选) */
    // if (v->pi_var_max > 0) {
    //     if (ui_delta > v->pi_var_max)
    //         ui_delta = v->pi_var_max;
    //     else if (ui_delta < -v->pi_var_max)
    //         ui_delta = -v->pi_var_max;
    // }
    
    /* 更新积分项 */
    v->ui += ui_delta;
    v->ui_delta = ui_delta; // 保存用于抗饱和
    
    /* 计算PI输出 */
    v->pi_out = v->ui + v->up;

    /* 输出限幅与抗饱和处理 */
    if (v->pi_out > v->pi_out_max) {
        v->pi_out = v->pi_out_max;
        v->ui -= v->ui_delta; // 反馈抗饱和
    }
    else if (v->pi_out < -v->pi_out_max) {
        v->pi_out = -v->pi_out_max;
        v->ui -= v->ui_delta; // 反馈抗饱和
    }
}

//==========================弱磁环PI===================================================//
void pi_flux_calc(PI_flux *v)
{
    v->err = v->pi_ref - v->pi_fdb;

    /* Integral segment */
    v->ui_delta = v->Tc * v->Ki * v->err;
    /*
        if ( v->ui_delta > v->pi_var_max )  v->ui_delta = v->pi_var_max;
        else if (v->ui_delta < -v->pi_var_max ) v->ui_delta = -v->pi_var_max;
    */
    v->ui += v->ui_delta;

    /* Proportional segment */
    v->up = v->Kp * v->err;
    /*
        if ( v->up > v->pi_var_max)     v->up = v->pi_var_max;
        else if ( v->up< -v->pi_var_max )   v->up = -v->pi_var_max;
    */
    /* Proportional and integral repeated addition */
    v->pi_out = v->ui + v->up;

    if (v->pi_out > v->pi_out_max)
    {
        v->pi_out = v->pi_out_max;
        v->ui -= v->ui_delta; /* Stop integral when saturation */
    }
    else if (v->pi_out < v->pi_out_min)
    {
        v->pi_out = v->pi_out_min;
        v->ui -= v->ui_delta; /* Stop integral when saturation */
    }
}
//===VSD矢量控制方法
void VSDclarke_calc(VSDCLARKE *v)
{
    //************************恒功率变换*******************************************//
    //    v->ds = 0.81649658*(v->as - 0.5*v->bs - 0.5* v->cs);
    //    v->qs = 0.81649658*(0.8660254*v->bs - 0.8660254*v->cs);
    //************************恒幅值变换*******************************************//
    v->alp = 0.333333334 * (v->ias - 0.5 * v->ibs - 0.5 * v->ics + 0.8660254 * v->ixs - 0.8660254 * v->iys);
    v->bet = 0.333333334 * (0.8660254 * v->ibs - 0.8660254 * v->ics + 0.5 * v->ixs + 0.5 * v->iys - 1 * v->izs);
    v->xs = 0.333333334 * (v->ias - 0.5 * v->ibs - 0.5 * v->ics - 0.8660254 * v->ixs + 0.8660254 * v->iys);
    v->ys = 0.333333334 * (-0.8660254 * v->ibs + 0.8660254 * v->ics + 0.5 * v->ixs + 0.5 * v->iys - 1 * v->izs);
}

void IVSDclarke_calc(IVSDCLARKE *v)
{
    //************************恒功率变换*******************************************//
    //    v->as = 0.81649658*v->ds;
    //    v->bs = 0.81649658*(-0.5*v->ds + 0.8660254*v->qs);
    //    v->cs = 0.81649658*(-0.5*v->ds - 0.8660254*v->qs);
    //************************恒幅值变换*******************************************//
    v->ias = v->alp + v->xs;
    v->ibs = -0.5 * v->alp + 0.8660254 * v->bet - 0.5 * v->xs - 0.8660254 * v->ys;
    v->ics = -0.5 * v->alp - 0.8660254 * v->bet - 0.5 * v->xs + 0.8660254 * v->ys;
    v->ixs = 0.8660254 * v->alp + 0.5 * v->bet - 0.8660254 * v->xs + 0.5 * v->ys;
    v->iys = -0.8660254 * v->alp + 0.5 * v->bet + 0.8660254 * v->xs + 0.5 * v->ys;
    v->izs = -1 * v->bet - 1 * v->ys;
}

/** 
 * @brief SVPWM算法
 * @param  U_alpha: α轴电压
 * @param  U_beta: β轴电压
 * @param  U_dc: 母线额定电压
 * @version 0.1
 */
void SVPWM(float U_alpha, float U_beta, float U_dc)
{
    float U1, U2, U3;
    float T0, T1, T2, T3, T4, T5, T6, T7; // 基矢量作用时间
    float K;                              // 基矢量作用时间计算辅助参数
    float T_x, T_y;                       // 扇区作用时间计算辅助变量
    float Ta, Tb, Tc;
    const float _sqrt_3_ = 1.732050808f;
    const float T = 1.0f;
    uint8_t N = 0;
    uint8_t sector = 0; // 扇区号
    uint8_t A, B, C;
    //T = 1.0f;
    Ta = 0;
    Tb = 0;
    Tc = 0;

    // U1 =  U_beta;
    // U2 = (_SQRT_3 * U_alpha -  U_beta) * 0.5f;
    // U3 = (-_SQRT_3 * U_alpha -  U_beta) * 0.5f;
    // K = _SQRT_3 * T / Udc;
    // arm_sqrt_f32(3, &_sqrt_3_);

    U1 = U_beta;
    U2 = (_sqrt_3_ * U_alpha - U_beta) * 0.5f;
    U3 = (-_sqrt_3_ * U_alpha - U_beta) * 0.5f;
    K = _sqrt_3_ * T / U_dc;

    A = (U1 > 0) ? 1 : 0;
    B = (U2 > 0) ? 1 : 0;
    C = (U3 > 0) ? 1 : 0;
    N = A + (B << 1) + (C << 2); // N=A+2*B+4*C

    switch (N)
    {
    case 1:
        sector = 2; // 扇区2
        break;
    case 2:
        sector = 6; // 扇区3
        break;
    case 3:
        sector = 1; // 扇区1
        break;
    case 4:
        sector = 4; // 扇区4
        break;
    case 5:
        sector = 3; // 扇区3
        break;
    case 6:
        sector = 5; // 扇区5
        break;
    default:

        break;
    }

    switch (sector)
    {
    case 1: {      //N=3
        T4 = U2 * K;
        T6 = U1 * K;
        T0 = (T - T4 - T6) * 0.5f;
        T7 = T0;
        if (T4 + T6 <= T)
        {
            T_x = T4;
            T_y = T6;
        }
        else // 过调制
        {
            T_x = T4 * T / (T4 + T6);
            T_y = T6 * T / (T4 + T6);
            T0 = 0;
            T7 = 0;
        }
        /*计算三相PWM占空比时间*/
        Ta = (T - T_x - T_y) * 0.25f;
        Tb = Ta + T_x * 0.5f;
        Tc = Tb + T_y * 0.5f;
        Tc1 = Ta;
        Tc2 = Tb;
        Tc3 = Tc;
    }
    break;
    case 2: {  //N=1
        T2 = -U2 * K;
        T6 = -U3 * K;
        T0 = (T - T2 - T6) * 0.5f;
        T7 = T0;
        if (T2 + T6 <= T)
        {
            T_x = T2;
            T_y = T6;
        }
        else // 过调制
        {
            T_x = T2 * T / (T2 + T6);
            T_y = T6 * T / (T2 + T6);
            T0 = 0;
            T7 = 0;
        }
        Ta = (T - T_x - T_y) * 0.25f;
        Tb = Ta + T_x * 0.5f;
        Tc = Tb + T_y * 0.5f;
        Tc1 = Tb;
        Tc2 = Ta;
        Tc3 = Tc;
    }
    break;
    case 3: {   //N=5
        T2 = U1 * K;
        T3 = U3 * K;
        T0 = (T - T2 - T3) * 0.5f;
        T7 = T0;
        if (T2 + T3 <= T)
        {
            T_x = T2;
            T_y = T3;
        }
        else // 过调制
        {
            T_x = T2 * T / (T2 + T3);
            T_y = T3 * T / (T2 + T3);
            T0 = 0;
            T7 = 0;
        }
        Ta = (T - T_x - T_y) * 0.25f;
        Tb = Ta + T_x * 0.5f;
        Tc = Tb + T_y * 0.5f;
        Tc1 = Tc;
        Tc2 = Ta;
        Tc3 = Tb;
    }
    break;
    case 4: {    //N=4
        T1 = -U1 * K;
        T3 = -U2 * K;
        T0 = (T - T1 - T3) * 0.5f;
        T7 = T0;
        if (T1 + T3 <= T)
        {
            T_x = T1;
            T_y = T3;
        }
        else // 过调制
        {
            T_x = T1 * T / (T1 + T3);
            T_y = T3 * T / (T1 + T3);
            T0 = 0;
            T7 = 0;
        }
        Ta = (T - T_x - T_y) * 0.25f;
        Tb = Ta + T_x * 0.5f;
        Tc = Tb + T_y * 0.5f;
        Tc1 = Tc;
        Tc2 = Tb;
        Tc3 = Ta;
    }
    break;
    case 5: {    //N=6
        T1 = U3 * K;
        T5 = U2 * K;
        T0 = (T - T1 - T5) * 0.5f;
        T7 = T0;
        if (T1 + T5 <= T)
        {
            T_x = T1;
            T_y = T5;
        }
        else // 过调制
        {
            T_x = T1 * T / (T1 + T5);
            T_y = T5 * T / (T1 + T5);
            T0 = 0;
            T7 = 0;
        }
        Ta = (T - T_x - T_y) * 0.25f;
        Tb = Ta + T_x * 0.5f;
        Tc = Tb + T_y * 0.5f;
        Tc1 = Tb;
        Tc2 = Tc;
        Tc3 = Ta;
    }
    break;
    case 6: {   //N=2
        T4 = -U3 * K;
        T5 = -U1 * K;
        T0 = (T - T4 - T5) * 0.5f;
        T7 = T0;
        if (T4 + T5 <= T)
        {
            T_x = T4;
            T_y = T5;
        }
        else // 过调制
        {
            T_x = T4 * T / (T4 + T5);
            T_y = T5 * T / (T4 + T5);
            T0 = 0;
            T7 = 0;
        }
        Ta = (T - T_x - T_y) * 0.25f;
        Tb = Ta + T_x * 0.5f;
        Tc = Tb + T_y * 0.5f;
        Tc1 = Ta;
        Tc2 = Tc;
        Tc3 = Tb;
    }
    break;
    default:
        break;
    }
    // 上位机波形显示
   
    gMotorData.f1.svpwm_wave_a = Tc1;
     gMotorData.f1.svpwm_wave_b = Tc2;
     gMotorData.f1.svpwm_wave_c = Tc3;
    // 更新PWM寄存器
    UpdatePwm(Tc1, Tc2, Tc3);

   
}

/**
 * @description: 限制pwm幅值
 * @param t  占空比，参数范围：0-1
 * @retval:
 */
inline float contain_pwm_time(float t)
{
#define SHORT_PULSE_H 0.459f    // 
#define SHORT_PULSE_L 0.041f    // 窄脉冲时间2.5us+死区时间1.5us=4us,PWM周期50us,4/50/2=0.04
    // const float SHORT_PULSE = 0.02f; 
    float tmp;
     if (t >= SHORT_PULSE_H)
    {
        tmp = SHORT_PULSE_H;
    }
    else if (t >= SHORT_PULSE_L)
    {
        tmp = t;
    }
    else if (t >= 0.011f)   //0.013us = 1.3/50/2
    {
        tmp =SHORT_PULSE_L;  
    }
    else
    {
        tmp = 0.0f;
    }
    return tmp;
}

void UpdatePwm(float tu, float tv, float tw)
{
#ifdef _SVPWM_
#define ARR_20kHz 7500
#else
#define ARR_20kHz 3750
#endif
    // 对时间大小进行限制
    tu = contain_pwm_time(tu);
    tv = contain_pwm_time(tv);
    tw = contain_pwm_time(tw);
    TMR1->c1dt = (uint32_t)(tu * ARR_20kHz);
    TMR1->c2dt = (uint32_t)(tv * ARR_20kHz);
    TMR1->c3dt = (uint32_t)(tw * ARR_20kHz);
}

/**
 * @brief 旋变初始角计算
 * @param  U_1: α轴电压
 * @param  U_dc: 额定电压
 * @param  trigger: 触发计算
 * @return float
 * @version 0.1
 */
float ResolverInitialAngleCalibration(float U_1, float U_dc, uint8_t *trigger)
{
    /*50us执行一次*/
    const uint16_t TIME1_CNT_MAX = 2000; // 0.05ms*2000=100ms
    const uint16_t TIME2_CNT_MAX = 20000;
    const uint8_t PRE_POSTION_CNT_MAX = 5;
    float angle_buf[PRE_POSTION_CNT_MAX];
    float angle;
    float u_a;
    float sum = 0.0f;
    uint16_t delay_time1 = 0;
    uint16_t delay_time2 = 0;
    static uint16_t cnt = 0;
    if (*trigger == 1)
    {
        cnt = 0;
        *trigger = 0;
    }

    if (cnt < PRE_POSTION_CNT_MAX)
    {
        if (delay_time1 <= TIME1_CNT_MAX)
        {
            u_a = U_1;
        }
        else
        {
            angle_buf[cnt] = Ad2s_GetAngle();
            u_a = 0.0f;
            sum += angle_buf[cnt];
            delay_time2++;
        }
        if (delay_time2 >= TIME2_CNT_MAX)
        {
            delay_time1 = 0;
        }
        else
        {
            delay_time1++;
        }
        SVPWM(u_a, 0, U_dc);
        cnt++;
    }
    else
    {
        return (sum / PRE_POSTION_CNT_MAX);
    }
    return 0;
}

void FixedAngle(float us, float angle, uint16_t poles)
{
    float u_d, u_q;
    float we;
    we = angle * poles * 0.01745329252f; // 角度换算成弧度
    u_d = us * arm_cos_f32(we);
    u_q = us * arm_sin_f32(we);
}

/**
  * @brief 计算梯形加速曲线斜率
  * @param  trap: 梯形加速曲线结构体CurvePara_TypeDef*
  * @param  target: 目标速度(r/min)
  * @param  time: 加速时间(ms)
  * @version 0.1
  */
void CalcTRAP(CurvePara_TypeDef *trap,float target,float time)
{
    if(target != trap->target_speed)
    {
        trap->target_speed = target;
        trap->start_speed = trap->out_speed;
        if (trap->target_speed > trap->speed_max)
        {
            trap->target_speed = trap->speed_max;
        }

    if (trap->target_speed < trap->speed_min)
    {
        trap->target_speed = trap->speed_min;
    }
    /*加速步长 = v0+(v-v0)/(60*1000*t)*0.05*CNT 加速度单位：rpm/ms^2*/
    trap->step_speed = ((trap->target_speed - trap->start_speed)* 0.05f * trap->step_time) / time ;
   }      
}

/**
  * @brief 梯形加速曲线计算
  * @param  trap: 梯形加速曲线结构体CurvePara_TypeDef*
  * @return float 
  * @version 0.1
  */
float TrapeAccelerationCurve(CurvePara_TypeDef *trap)
{
   static uint16_t time_cnt = 0;
   static float acc_speed = 0.0f;
   if (trap->out_speed != trap->target_speed) // 是否到达目标速度
   {
       time_cnt++;
       if (time_cnt >= trap->step_time)
       {
          time_cnt = 0;
          trap->out_speed += trap->step_speed;  // 计算输出速度
          /*限幅输出*/
          if (trap->step_speed > 0)
          {
              if (trap->out_speed >= trap->target_speed)
              {
                  trap->out_speed = trap->target_speed;
                  acc_speed = 0.0f;
              }
           }
           else if (trap->step_speed < 0)
           {
               if (trap->out_speed <= trap->target_speed)
               {
                   trap->out_speed = trap->target_speed;
                   acc_speed = 0.0f;
               }
           }

           if (trap->out_speed > trap->speed_max)
           {
               trap->out_speed = trap->speed_max;
           }
           else if (trap->out_speed < trap->speed_min)
           {
               trap->out_speed = trap->speed_min;
           }
       }
    }
    else
    {
        trap->out_speed = trap->target_speed;
        acc_speed = 0.0f;
    }
    return trap->out_speed;
}

/**
  * @brief 更新上位机波形
  * @version 0.1
  */
void update_wave()
{
    gMotorData.f1.channel_count = 15; // F1帧发送波形数量
    gMotorData.f1.i_phase_a = clarke1.as;
    gMotorData.f1.i_phase_b = clarke1.bs;
    gMotorData.f1.i_phase_c = clarke1.cs;
    gMotorData.f1.id_ref = SynMotorVc.isd_ref;
    gMotorData.f1.id_actual = SynMotorVc.isd1;
    gMotorData.f1.iq_ref = SynMotorVc.isq_ref;
    gMotorData.f1.iq_actual = SynMotorVc.isq1;
    gMotorData.f1.motor_speed_ref = *pCsvParamSpeed_ref;
    gMotorData.f1.motor_speed = SynMotorVc.speedback_flt*60.0f;
    // //gMotorData.f1.motor_speed = gOpenLoopFreqCtrl.angle * 57.2957795f;
    // gMotorData.f1.motor_position = SynMotorVc.flux_theta;
    gMotorData.f1.motor_position = machineAngle;
    //gMotorData.f1.motor_position = SynMotorVc.VSdq_ref;
    //gMotorData.f1.ud_actual = SynMotorVc.Vsd1_ref;
    gMotorData.f1.ud_actual = faultId;
    gMotorData.f1.uq_actual = SynMotorVc.Vsq1_ref;
    //
    // gMotorData.f1.svpwm_wave_a = pi_speed.pi_out;
    // gMotorData.f1.svpwm_wave_b = pi_isd1.pi_out;
    // gMotorData.f1.svpwm_wave_c = pi_isq1.pi_out;
    MotorDataSendFrame(FRAME_F1);
}


/*为移植编译通过定义空函数*/
void VoltageBalance_calc(Vector_ctrl *p)
{
}
/*为移植编译通过定义空函数*/
void marsest_calc(Vector_ctrl *p)
{
}
/*为移植编译通过定义空函数*/
void pi_voltage_calc(PI_VOLTAGE *p)
{
}


/********************************** 开环频率控制 Start **********************************/


// 初始化开环频率控制
void OpenLoopFreqCtrl_Init(OpenLoopFreqCtrl_TypeDef *ctrl)
{
    ctrl->target_freq = 0.0f;
    ctrl->current_freq = 0.0f;
    ctrl->freq_step = 0.0f;
    ctrl->max_freq = 0.0f;
    ctrl->angle = 0.0f;
    ctrl->angle_step = 0.0f;
    ctrl->angle_coef = 0.0f;
    ctrl->Ts = 0.00005f; // 默认20kHz控制周期
}

// 更新开环频率控制
void OpenLoopFreqCtrl_Update(OpenLoopFreqCtrl_TypeDef *ctrl)
{
    // 读取参数
    ctrl->target_freq = *pCsvParamOpenLoopFreq;
    ctrl->freq_step = *pCsvParamOpenLoopFreqStep * ctrl->Ts; // Hz/s 转换为 Hz/周期
    ctrl->max_freq = *pCsvParamOpenLoopFreqMax;
    ctrl->angle_coef = *pCsvParamOpenLoopAngleCoef;
    
    // 限制目标频率
    if (ctrl->target_freq > ctrl->max_freq)
    {
        ctrl->target_freq = ctrl->max_freq;
    }
    else if (ctrl->target_freq < -ctrl->max_freq)
    {
        ctrl->target_freq = -ctrl->max_freq;
    }
    
    // 平滑调整频率
    if (ctrl->current_freq < ctrl->target_freq)
    {
        ctrl->current_freq += ctrl->freq_step;
        if (ctrl->current_freq > ctrl->target_freq)
        {
            ctrl->current_freq = ctrl->target_freq;
        }
    }
    else if (ctrl->current_freq > ctrl->target_freq)
    {
        ctrl->current_freq -= ctrl->freq_step;
        if (ctrl->current_freq < ctrl->target_freq)
        {
            ctrl->current_freq = ctrl->target_freq;
        }
    }
    
    // 计算角度步长(rad/周期)
    ctrl->angle_step = PI2 * ctrl->current_freq * ctrl->Ts * ctrl->angle_coef;
    
    // 更新角度
    ctrl->angle += ctrl->angle_step;
    
    // 角度限制在 0-2π 范围内
    if (ctrl->angle >= PI2)
    {
        ctrl->angle -= PI2;
    }
    else if (ctrl->angle < 0)
    {
        ctrl->angle += PI2;
    }
}

/**
  * @brief 弱磁控制程序
  * @param  vref: 0.95倍的母线电压
  * @param  vs: 输出电压幅值
  * @param  delta:  id增量
  * @return float 
  * @version 0.1
  */
float WeakFluxCtr(float vref,float vs,float delta)
{
    static float out = 0.0f;
    const float LIMIT_WEAK_CURRENT = -10.0f;
    float err = vref - vs;
    out += (delta*err);
    // if(err >= 0.0f)
    // {
        
    // }
    // else if(err < 0.0f)
    // {
    //     out += (delta*err);
    // }
    if(out >= 0.0f)
    {
        out = 0.0f;
    }
    else if(out <= LIMIT_WEAK_CURRENT)
    {
        out = LIMIT_WEAK_CURRENT;
    }
    return out;
}

/**
 * @brief 增量式PI速度控制器计算函数 (最终审查版)
 *
 * @param v 指向 PI_SPEED 结构体的指针
 *
 * @note
 * 1. 这是一个增量式PI控制器。
 * 2. 包含了积分分离法，用于改善大误差下的动态响应。
 * 3. 包含了正确的增量式抗饱和逻辑。
 * 4. 确保传入的 v->Ki 已经是离散化后的增益 (即 理论Ki * 采样时间Tc)。
 */
void PI_Speed_Incremental_Calc(PI_SPEED *v)
{
    float delta_total; // 本次计算的总增量
    float integral_threshold; // 积分分离的误差阈值

    // 1. 计算当前误差
    v->err = v->pi_ref - v->pi_fdb;

    // 2. 积分项计算 (使用积分分离法)
    // 只有当误差小于参考值的一定比例时，才启用积分作用，以防止在误差较大时产生过大的积分累积。
    // 0.2f 是一个可调参数，代表20%。
    integral_threshold = v->pi_ref * 0.25f;
    if (v->err < integral_threshold && v->err > -integral_threshold)
    {
        // 误差在指定范围内，计算积分增量
        v->ui_delta = v->Ki * v->err;
    }
    else
    {
        // 误差过大，暂时禁用积分作用，防止积分饱和
        v->ui_delta = 0.0f;
    }

    // 3. 比例项计算 (基于误差的变化量)
    v->up = v->Kp * (v->err - v->err1);

    // 4. 计算本次总增量 (比例增量 + 积分增量)
    delta_total = v->up + v->ui_delta;

    // 5. 核心：正确的增量式抗饱和逻辑
    // 在更新输出值之前，检查是否已经达到饱和，并且本次的增量是否会使饱和情况恶化。
    if ((v->pi_out >= v->pi_out_max && delta_total > 0) ||   // 已在上限且还想增加
        (v->pi_out <= -v->pi_out_max && delta_total < 0))  // 已在下限且还想减小
    {
        // 情况满足，说明输出已饱和且增量方向错误。
        // 此时我们不执行累加操作，让输出保持在限幅值，从而实现抗饱和。
        // (do nothing)
    }
    else
    {
        // 未饱和，或者增量方向是“退出”饱和区的，则正常累加
        v->pi_out += delta_total;
    }

    // 6. 更新历史误差，为下一次计算做准备
    v->err1 = v->err;

    // 7. 安全钳位：为确保万无一失，在所有计算后对输出进行最终限幅。
    // (在上面的抗饱和逻辑正常工作时，这一步通常不会被触发)
    if (v->pi_out > v->pi_out_max)
    {
        v->pi_out = v->pi_out_max;
    }
    else if (v->pi_out < -v->pi_out_max)
    {
        v->pi_out = -v->pi_out_max;
    }
}
/********************************** 开环频率控制 End **********************************/
