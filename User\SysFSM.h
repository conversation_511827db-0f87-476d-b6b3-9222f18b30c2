/**********************************************************
  * @file     sys_config.h
  * @brief    系统配置总头文件
  * <AUTHOR>
  * @date     2024-01-05
  * @version  V1.0.0
  * @note     包含所有系统需要的头文件和全局定义
************************************************************/

#ifndef _SYS_FSM_H
#define _SYS_FSM_H

#include "sysTypeDef.h"

/* 版本信息 */
#define FIRMWARE_VERSION    "V1.0.0"

/* 系统参数定义 */
#define SYSTEM_POWER_28V     28.0f   // 28V供电电压
#define SYSTEM_POWER_750V    750.0f  // 750V母线电压
#define SYSTEM_POWER_MAX     800.0f  // 最大允许电压
#define SYSTEM_POWER_MIN     650.0f  // 最小工作电压

#define MOTOR_POWER_PEAK     125000.0f  // 电机峰值功率(W)
#define MOTOR_POWER_CONT     50000.0f  // 电机持续功率(W)

/* 状态机相关定义 */
typedef enum {
    STATE_POWER_ON = 0,
    STATE_INIT_EXTERNAL = 1,
    STATE_SELF_TEST = 2,
    STATE_HOLD = 3,
    STATE_READY = 4,
    STATE_RUNNING = 5,
    STATE_FAULT = 6,
    STATE_SHUTDOWN = 7,
    STATE_MAX = 8 // 边界检查
} SystemState_TypeDef;

/* 状态机事件定义 */
typedef enum {
    EVENT_NONE = 0,
    EVENT_POWER_STABLE = 1, // 电源稳定
    EVENT_INIT_DONE,        // 初始化完成
    EVENT_SELF_TEST_PASS,   // 自检通过
    EVENT_SELF_TEST_FAIL,   // 自检失败
    EVENT_WARNING_DETECTED, // 检测到告警
    EVENT_WARNING_CLEARED,  // 系统解除
    EVENT_FAULT_DETECTED,   // 检测到故障
    EVENT_SHUTDOWN_CMD,     // 停机命令
    EVENT_RESTART_CMD       // 重启命令
} SystemEvent_TypeDef;

/* 系统参数定义 */
//#define POWER_STABLE_DELAY_MS  1000    // 电源稳定延时
#define INIT_RETRY_MAX_COUNT   3       // 初始化重试次数
#define POWER_MONITOR_PERIOD   100     // 功率监测周期(ms)

/* 状态上下文结构体定义 */
typedef struct {
    uint16_t entry_count;      // 进入次数
    uint32_t last_update_time; // 上次进入时间init_done
    uint8_t flag;              // 状态内部标志位
} StateContext_t;


/* 状态转换表定义 */
typedef struct {
    SystemState_TypeDef current_state;    // 当前状态
    SystemState_TypeDef next_state;       // 下一个状态
    uint8_t priority;                     // 转换优先级(数值越小优先级越高)
    check_t (*condition)(void);           // 状态转换条件判断函数
} StateTransition_TypeDef;

/* 状态机主上下文结构体 */
typedef struct {
    SystemState_TypeDef current_state;    // 当前状态
    SystemState_TypeDef previous_state;   // 上一个状态
    SystemState_TypeDef entry_state;      // 切入状态(记录从哪个状态切入)
    uint32_t state_entry_time;            // 进入当前状态的时间
    StateContext_t state_ctx[8];          // 各状态上下文(对应8个状态)
} StateMachineContext_TypeDef;

/* 状态机上下文初始化宏定义 */
#define STATEMACHINE_CONTEXT_INIT  { \
    .current_state = STATE_POWER_ON, \
    .previous_state = STATE_POWER_ON, \
    .entry_state = STATE_POWER_ON, \
    .state_entry_time = 0, \
    .state_ctx = {{0}} \
}

/* 系统状态总结构体 */
typedef struct {
    PowerStatus_TypeDef power;              // 电源状态
    WorkStatus_TypeDef work;                // 工作状态
    SystemWarn_TypeDef warn;                // 告警状态
    SystemFlags_TypeDef status;             // 系统标志位
    WarnFault_TypeDef warn_fault;           // 告警对应的故障标志
    SysFault_TypeDef sys_fault;             // 系统级故障标志
    SystemCmd_TypeDef cmd;                  // 系统命令标志
} SystemStatus_TypeDef;

/* 全局变量声明 */
extern SystemStatus_TypeDef g_SystemStatus;
extern StateMachineContext_TypeDef g_state_machine_ctx;  

/* 定时任务相关函数声明 */
void Timer_Tasks_Init(void);
void Timer_Tasks_Execute(void);

/* 系统状态管理函数声明 */
void System_StatusUpdate(void);
void System_ProtectCheck(void);
check_t System_SelfTest(void);
WarnLevel_TypeDef Get_Max_Warning_Level(void);
check_t Is_Fault_Recoverable(void);
ret_t Attempt_Fault_Recovery(void);
void Perform_Safe_Shutdown(void);

/* 外设初始化函数声明 */
init_t External_Devices_Init(void);

/* 状态检查函数声明 */
check_t Check_PowerStable(void);
check_t Check_InitComplete(void);
check_t Check_SelfTestPass(void);
check_t Check_SelfTestFail(void);
check_t Check_SelfTestRetry(void);
check_t Check_SystemNormal(void);
check_t Check_StartCondition(void);
check_t Check_StopCondition(void);
check_t Check_EmergencyStop(void);
check_t Check_WarningLevel2(void);
check_t Check_Warning_Cleared(void);
check_t Check_RecoveryCondition(void);
check_t Check_ShutdownCondition(void);
check_t Check_Warning_Condition(void);
check_t Check_Host_Stop_Command(void);
check_t Check_FaultCondition(void);
check_t Check_Host_SelfTest_Command(void);
check_t Check_Fault_Unrecoverable(void);  /* 检查故障是否无法排除 */

/* 电机控制相关函数声明 */
void Motor_Control_Execute(void);
void Update_Motion_Status(void);

/* 告警处理相关函数声明 */
void Log_Warning_Info(void);
void Send_Warning_To_Host(void);
void Monitor_Warning_Parameters(void);
void Limit_Output_Power(void);
void Prepare_For_Fault_State(void);
void Update_Warning_Status(void);

/* 功率监控相关函数声明 */
void Update_Power_Status(float power);   /* 更新功率状态 */
void Disable_All_Outputs(void);          /* 关闭所有输出 */

void Print_System_Status_Distributed(void);

#endif /* _SYS_FSM_H */

