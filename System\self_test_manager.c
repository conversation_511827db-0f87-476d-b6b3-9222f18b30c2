/**
 * @file self_test_manager.c
 * @brief 自检模块管理器层和接口抽象层实现
 * <AUTHOR> @date 2025-07-14
 */

#include "self_test_manager.h"
#include <stddef.h>
#include <string.h>

/****************************** 内部数据结构 ******************************/

/**
 * @brief 自检管理器内部状态结构体
 */
typedef struct {
    bool initialized;                                    // 是否已初始化
    self_test_manager_config_t config;                   // 管理器配置
    self_test_manager_state_t state;                     // 当前状态
    self_test_item_config_t items[SELF_TEST_ITEM_MAX];   // 注册的自检项目
    uint32_t registered_count;                           // 已注册项目数量
    bool abort_requested;                                // 是否请求中止
} self_test_manager_t;

/****************************** 内部变量 ******************************/

static self_test_manager_t g_self_test_manager = {0};

/****************************** 内部函数声明 ******************************/

static uint32_t get_system_time_ms(void);
static bool is_item_registered(self_test_item_t item);
static self_test_item_config_t* find_item_config(self_test_item_t item);

/****************************** 字符串映射表 ******************************/

static const char* g_item_names[] = {
    "无效项目",
    "ADC电流自检",
    "母线电压自检",
    "电机温度自检",
    "板上温度自检",
    "旋变自检",
    "看门狗自检"
};

static const char* g_result_strings[] = {
    "通过",
    "失败", 
    "警告",
    "未测试",
    "不支持"
};

/****************************** 管理器API实现 ******************************/

/**
 * @brief 初始化自检管理器
 */
bool SelfTestManager_Init(const self_test_manager_config_t* config)
{
    if (config == NULL) {
        return false;
    }
    
    // 清零管理器状态
    memset(&g_self_test_manager, 0, sizeof(g_self_test_manager));
    
    // 保存配置
    g_self_test_manager.config = *config;
    g_self_test_manager.state = SELF_TEST_MANAGER_IDLE;
    g_self_test_manager.initialized = true;
    
    return true;
}

/**
 * @brief 注册自检项目
 */
bool SelfTestManager_RegisterItem(const self_test_item_config_t* item_config)
{
    if (!g_self_test_manager.initialized || item_config == NULL) {
        return false;
    }
    
    if (item_config->item <= SELF_TEST_ITEM_NONE || item_config->item >= SELF_TEST_ITEM_MAX) {
        return false;
    }
    
    if (item_config->test_func == NULL) {
        return false;
    }
    
    // 检查是否已注册
    if (is_item_registered(item_config->item)) {
        return false;
    }
    
    // 检查注册数量限制
    if (g_self_test_manager.registered_count >= SELF_TEST_ITEM_MAX) {
        return false;
    }
    
    // 注册项目
    g_self_test_manager.items[g_self_test_manager.registered_count] = *item_config;
    g_self_test_manager.registered_count++;
    
    return true;
}

/**
 * @brief 启用/禁用指定自检项目
 */
bool SelfTestManager_EnableItem(self_test_item_t item, bool enabled)
{
    if (!g_self_test_manager.initialized) {
        return false;
    }
    
    self_test_item_config_t* config = find_item_config(item);
    if (config == NULL) {
        return false;
    }
    
    config->enabled = enabled;
    return true;
}

/**
 * @brief 执行所有已注册的自检项目
 */
bool SelfTestManager_RunAll(self_test_manager_result_t* result)
{
    if (!g_self_test_manager.initialized || result == NULL) {
        return false;
    }
    
    if (g_self_test_manager.state == SELF_TEST_MANAGER_RUNNING) {
        return false; // 已在运行中
    }
    
    // 初始化结果结构体
    memset(result, 0, sizeof(self_test_manager_result_t));
    result->state = SELF_TEST_MANAGER_RUNNING;
    result->item_results = NULL; // 由调用者分配内存
    
    g_self_test_manager.state = SELF_TEST_MANAGER_RUNNING;
    g_self_test_manager.abort_requested = false;
    
    uint32_t start_time = get_system_time_ms();
    
    // 遍历所有注册的项目
    for (uint32_t i = 0; i < g_self_test_manager.registered_count; i++) {
        if (g_self_test_manager.abort_requested) {
            result->state = SELF_TEST_MANAGER_ABORTED;
            break;
        }
        
        self_test_item_config_t* config = &g_self_test_manager.items[i];
        
        if (!config->enabled) {
            result->not_tested_items++;
            continue;
        }
        
        result->total_items++;
        
        // 执行单个自检项目
        self_test_item_result_t item_result = {0};
        bool success = SelfTestManager_RunSingle(config->item, &item_result);
        
        if (success) {
            switch (item_result.result) {
                case SELF_TEST_RESULT_PASS:
                    result->passed_items++;
                    break;
                case SELF_TEST_RESULT_FAIL:
                    result->failed_items++;
                    if (g_self_test_manager.config.stop_on_first_fail) {
                        g_self_test_manager.abort_requested = true;
                    }
                    break;
                case SELF_TEST_RESULT_WARNING:
                    result->warning_items++;
                    break;
                default:
                    result->not_tested_items++;
                    break;
            }
        } else {
            result->failed_items++;
            if (g_self_test_manager.config.stop_on_first_fail) {
                g_self_test_manager.abort_requested = true;
            }
        }
        
        // 检查总时间限制
        uint32_t elapsed_time = get_system_time_ms() - start_time;
        if (elapsed_time >= g_self_test_manager.config.max_total_time_ms) {
            g_self_test_manager.abort_requested = true;
        }
    }
    
    result->total_time_ms = get_system_time_ms() - start_time;
    
    if (g_self_test_manager.abort_requested) {
        result->state = SELF_TEST_MANAGER_ABORTED;
    } else {
        result->state = SELF_TEST_MANAGER_COMPLETED;
    }
    
    g_self_test_manager.state = result->state;
    
    return true;
}

/**
 * @brief 执行指定的自检项目
 */
bool SelfTestManager_RunSingle(self_test_item_t item, self_test_item_result_t* result)
{
    if (!g_self_test_manager.initialized || result == NULL) {
        return false;
    }

    self_test_item_config_t* config = find_item_config(item);
    if (config == NULL || !config->enabled) {
        return false;
    }

    // 初始化结果结构体
    memset(result, 0, sizeof(self_test_item_result_t));
    result->item = item;
    result->result = SELF_TEST_RESULT_NOT_TESTED;

    uint32_t start_time = get_system_time_ms();

    // 执行自检函数
    bool success = config->test_func(result);

    result->test_time_ms = get_system_time_ms() - start_time;

    // 检查超时
    if (result->test_time_ms >= config->timeout_ms) {
        result->result = SELF_TEST_RESULT_FAIL;
        result->error_code = 0xFFFFFFFF; // 超时错误代码
        return false;
    }

    return success;
}

/**
 * @brief 获取管理器当前状态
 */
self_test_manager_state_t SelfTestManager_GetState(void)
{
    return g_self_test_manager.state;
}

/**
 * @brief 中止正在进行的自检
 */
bool SelfTestManager_Abort(void)
{
    if (!g_self_test_manager.initialized) {
        return false;
    }

    if (g_self_test_manager.state == SELF_TEST_MANAGER_RUNNING) {
        g_self_test_manager.abort_requested = true;
        return true;
    }

    return false;
}

/**
 * @brief 获取自检项目名称字符串
 */
const char* SelfTestManager_GetItemName(self_test_item_t item)
{
    if (item >= 0 && item < SELF_TEST_ITEM_MAX) {
        return g_item_names[item];
    }
    return "未知项目";
}

/**
 * @brief 获取自检结果状态字符串
 */
const char* SelfTestManager_GetResultString(self_test_result_t result)
{
    if (result >= 0 && result < (sizeof(g_result_strings) / sizeof(g_result_strings[0]))) {
        return g_result_strings[result];
    }
    return "未知状态";
}

/**
 * @brief 反初始化自检管理器
 */
void SelfTestManager_Deinit(void)
{
    memset(&g_self_test_manager, 0, sizeof(g_self_test_manager));
}

/****************************** 内部函数实现 ******************************/

/**
 * @brief 获取系统时间（毫秒）
 * @note 这里需要根据实际系统实现
 */
static uint32_t get_system_time_ms(void)
{
    // TODO: 实现系统时间获取
    // 可以使用系统滴答定时器或其他时间源
    static uint32_t dummy_time = 0;
    return dummy_time++;
}

/**
 * @brief 检查项目是否已注册
 */
static bool is_item_registered(self_test_item_t item)
{
    for (uint32_t i = 0; i < g_self_test_manager.registered_count; i++) {
        if (g_self_test_manager.items[i].item == item) {
            return true;
        }
    }
    return false;
}

/**
 * @brief 查找项目配置
 */
static self_test_item_config_t* find_item_config(self_test_item_t item)
{
    for (uint32_t i = 0; i < g_self_test_manager.registered_count; i++) {
        if (g_self_test_manager.items[i].item == item) {
            return &g_self_test_manager.items[i];
        }
    }
    return NULL;
}
