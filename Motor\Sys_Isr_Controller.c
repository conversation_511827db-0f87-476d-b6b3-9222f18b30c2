/*
//===========================================================================
//
// 文件名:    SysCtl_ISR.c
//
// 用途:   	  系统主中断
//
//===========================================================================
*/
#include "SysCtl_AllHeaders.h"
#include "at32a403a_wk_config.h"
#include "ENC_Speed.h"
#include "adc_pmsm.h"

uint16_t overspeedflag=0;
float fTimeCount = 0.0;
extern uint16_t uCEFlag;
extern float JSD;
float OPEN_flag=0.0;
float C2OPEN_flag=0.0;
extern void DATAPeriodDSPtoARM(void);
extern void DATAPeriodARMtoDSP(void);
extern int8_t SysCurProtect(void);
//extern void MotorType(void);
extern void flux_thetaChange(void);

//Vector_ctrl SynMotorVc = Vector_ctrl_DEFAULTS;//矢量控制结构体声明,初始化矢量控制

uint16_t uDMAFlag=0;
Filter Filter_udch = Filter_DEFAULTS;
Filter Filter_udcl = Filter_DEFAULTS;
Filter Filter_idc = Filter_DEFAULTS;
Filter Filter_idc1 = Filter_DEFAULTS;
Filter Filter_idc2 = Filter_DEFAULTS;
Filter Filter_speed = Filter_DEFAULTS;

//float FeedforwordFlag=0.0;
float QH=0.0;
float shijiWelec;
float ChangeMarsF;
float mid_speed;
float high_speed;
float Ld_mras=0.0;
float Lq_mras=0.0;
float MRAS_w_flag=0.0;
float power_pf=0.0;
float F_count = 50;
int MRAS_OPEN =0;
uint16_t adc_cnt = 0;
int8_t faultId;

float I_overCru[4];
/**
  * @brief ADC1中断，系统主中断
  * @version 0.1
  */
void ADC1_IRQHandler(void) //ADC1中断
{
    if (adc_interrupt_flag_get(ADC1, ADC_PCCE_FLAG) != RESET) // 检查ADC1抢占通道转换完成中断标志位
    {
        adc_flag_clear(ADC1, ADC_PCCE_FLAG); // 清除ADC1抢占通道转换完成中断标志
        //=====================AD采样=======================================
        float ia_filt, ib_filt, ic_filt;
        ADC_PMSM_ProcessCurrents(&ia_filt, &ib_filt, &ic_filt);
        SynMotorVc.Isa1_SI = -ia_filt;
        SynMotorVc.Isb1_SI = -ib_filt;
        SynMotorVc.Isc1_SI = -ic_filt;

         //=====================转速和位置采样=======================================
        faultId = SysCurProtect(); // 系统电流保护&转速保护
        pi_isd.Ki = *pCsvParamSynVcCurrentDKi;    // 低速d轴电流环积分增益
        pi_isd.Kp = *pCsvParamSynVcCurrentDKp;    // 低速d轴电流环比例增益 
        pi_isq.Ki = *pCsvParamSynVcCurrentQKi;    // 低速q轴电流环积分增益
        pi_isq.Kp = *pCsvParamSynVcCurrentQKp;    // 低速q轴电流环比例增益
        //SynMotorVc.theta_preposition =  1044; // 500  2393 835 2200  2441
        /***************获取电角度***************/
        SynMotorVc.flux_theta = GetElectricalAngle_ENC();

        /***************获取母线电压***************/
        //SynMotorVc.Vdc_SI = Get_Bus_Voltage();  
			SynMotorVc.Vdc_SI=270.0f;
        //============电机状态机=======//
        if (SysMoore.SysStateNew == SystemRun)
        {

		    SynMotorVc.calc(&SynMotorVc);                   //矢量控制程序
        }
        //系统非运行状态下进行参数复位
        else
        {
            SynMotorVc.reset(&SynMotorVc);   //矢量控制复位
            tmr_output_enable(TMR1, FALSE);  //禁止PWM输出
        }

        if (SynMotorVc.wr_elec_flt<1 && SynMotorVc.speed_ref_Hz < 1 && SynMotorVc.PrePosition_Flag == 1)
        {
            SynMotorVc.StopFlag = 1;
        }
        else
        {
            SynMotorVc.StopFlag = 0;
        } 
        }
}


/***********************************************************
//函数名称：DATAPeriodDSPtoARM
//函数功能：汇总系统周期性数据传递 DSP->ARM
//函数参数：无
************************************************************/
void DATAPeriodDSPtoARM(void)
{
//	uint16_t u, uchaddr;

//	static float fWaveData = 0;

	FltWord_DSPtoARM = SysErrIndexReg.all;
 	if(uCEFlag == 1)
 	{
 		//MemCopy((uint16_t*)&SysSamDSPtoARM.fSamDSPtoARMIu1,(uint16_t*)&SysSamDSPtoARM.fSamDSPtoARMIu1 + 34,(uint16_t*)&DMABuf1[80]);//采样数据传递

 	}
 	//sInComm.pfnWaveDisplay(&sInComm);//实时波形数据传递

 	*pRunSpeed = SynMotorVc.speedback_flt;
 	*pEstSpeed = 1.0;

}
/***********************************************************
//函数名称：DATAPeriodARMtoDSP
//函数功能：汇总系统周期性数据传递 ARM->DSP
//函数参数：无
************************************************************/
void DATAPeriodARMtoDSP(void)
{
    SysMoore.SysStateNew = (TypeSysMooreState)StMach_ARMtoDSP;//读ARM侧状态机
    FreqEn_DSPtoARM = 0xc3;
    SynMotorVc.speed_ref_Hz = FreqRef_ARMtoDSP*50;  //从ARM侧读给定频率   ARM计算时间50Hz标幺化
    if(SynMotorVc.Sensorlessmode==1&&SysEnviConfg.uConStrat==SLVC)   //无感mars直接起动
    {
       if(SynMotorVc.speed_ref_Hz<=7)
       {
           SynMotorVc.speed_ref_Hz=7;
       }
    }
    SynMotorVc.voltage_ref =VolqRef_ARMtoDSP;
}
/***********************************************************
//函数名称：SysCurProtect
//函数功能：系统瞬时过流保护
//函数参数：无
************************************************************/

/**
 * @brief 系统电流和速度保护
 * @note  高效实现，优先检查硬件保护，使用瞬时值保护参数
 * @author: czy
 */
int8_t SysCurProtect(void)
{
    
        uint8_t software_fault = (_fabsf(SynMotorVc.Isa1_SI) > 60 ||
                                  _fabsf(SynMotorVc.Isb1_SI) > 60 ||
                                  _fabsf(SynMotorVc.Isc1_SI) > 60);
        
        if(software_fault) {
            tmr_output_enable(TMR1, FALSE);  // 禁止PWM输出
            SysMoore.SysStateNew = SysErr;
            SysErrIndexReg.bit.SysHFaultFlag = TRUE;
            I_overCru[0] = SynMotorVc.Isa1_SI;
            I_overCru[1] = SynMotorVc.Isb1_SI;
            I_overCru[2] = SynMotorVc.Isc1_SI;
            I_overCru[3] = SynMotorVc.isq_ref;
            return -1;
        }
    return 0;

}

// void SysCurProtect(void)
// {
// 	if(SysMoore.SysStateNew == SystemRun && 
// 	(fabs(SynMotorVc.Isa1_SI) > SysProParamReg.fOCProtect || 
// 	fabs(SynMotorVc.Isb1_SI) > SysProParamReg.fOCProtect  || 
// 	fabs(SynMotorVc.Isc1_SI) > SysProParamReg.fOCProtect) ||
//     gpio_input_data_bit_read(GPIOA, OVER_UI_CUR0_PIN) == false ||    // U相电流硬件下限保护
//     gpio_input_data_bit_read(GPIOD, OVER_UI_CUR1_PIN) == false ||    // U相电流硬件上限保护
//     gpio_input_data_bit_read(GPIOD, OVER_VI_CUR0_PIN) == false ||    // V相电流硬件下限保护
//     gpio_input_data_bit_read(GPIOD, OVER_VI_CUR1_PIN) == false ||    // V相电流硬件上限保护
//     gpio_input_data_bit_read(GPIOD, OVER_WI_CUR0_PIN) == false ||    // W相电流硬件下限保护
//     gpio_input_data_bit_read(GPIOD, OVER_WI_CUR1_PIN) == false       // W相电流硬件上限保护
//     )  
//     {
//         /***************瞬时过流保护方式待定，不可直接禁止PWM输出**********************/
//          tmr_output_enable(TMR1, FALSE);  //禁止PWM输出
//     	SysMoore.SysStateNew = SysErr;
//     	SysErrIndexReg.bit.SysHFaultFlag = TRUE;
//     }
// 	   //保护条件：系统运行 且预定位标志位为1时，转速小于-1000rpm或者大于给定转速限幅值,或者转速检测误差大于100
// 	    if(SysMoore.SysStateNew == SystemRun && \
// 	            SynMotorVc.PrePosition_Flag == 1 &&
// 	            ( (RotorSpeedclc.speed_rpm<-12000) || (fabs(RotorSpeedclc.speed_rpm)>SynMotorVc.SpeedLimit) ) )
// 	    {
//              tmr_output_enable(TMR1, FALSE);  //禁止PWM输出
// 	        SysMoore.SysStateNew = SysErr;
// 	        SysErrIndexReg.bit.SysHFaultOspeed = TRUE;
// 	     //   DSPFaultCodeReg.bit.OSFaultFlag = TRUE;                 //DSP故障失速故障置1
// 	        //索存失速故障信息
// 	        // Fault_recordU = JSD;//RotorSpeedclc.uDeltaPosition;
// 	        // Fault_recordV = RotorSpeedclc.speed_rpm_last;
// 	        // Fault_recordW = RotorSpeedclc.speed_rpm;
// 	        // Fault_recordFlag = DSPFaultCodeReg.all;
// 	    }
// }
/***********************************************************
//函数名称：ControllerRunMode
//函数功能：检测控制器位置判断运行模式
//函数参数：无
************************************************************/
/**
 * @brief 优化flux_theta的计算
 * @note  使用快速取模运算和无条件分支优化
 * @author: czy
 */
void flux_thetaChange(void) {

    float normalized_theta = SynMotorVc.flux_theta;
    
    // 使用快速取模运算
    normalized_theta = normalized_theta - PI4 * ((int)(normalized_theta/PI4));
    
    // 修正负值（避免分支预测失败）
    float correction = (normalized_theta < 0.0f) ? PI4 : 0.0f;
    normalized_theta += correction;
    
    //SynMotorVc.flux_theta = normalized_theta;
}



// void flux_thetaChange(void)
// {
// //    if (SynMotorVc.flux_theta < 0)
// //        SynMotorVc.flux_theta = SynMotorVc.flux_theta + PI40;
// //    if (SynMotorVc.flux_theta > PI40)
// //        SynMotorVc.flux_theta = SynMotorVc.flux_theta - PI40;
//     if(SynMotorVc.flux_theta > PI4)
//     {
//         SynMotorVc.flux_theta = SynMotorVc.flux_theta - PI4;
//         if(SynMotorVc.flux_theta > PI4)
//         {
//             SynMotorVc.flux_theta = SynMotorVc.flux_theta - PI4;
//             if(SynMotorVc.flux_theta > PI4)
//                    {
//                        SynMotorVc.flux_theta = SynMotorVc.flux_theta - PI4;
//                    }
//         }
//     }
//     else if (SynMotorVc.flux_theta < 0)
//     {
//         SynMotorVc.flux_theta = SynMotorVc.flux_theta + PI4;
//         if(SynMotorVc.flux_theta < 0)
//                 {
//                     SynMotorVc.flux_theta = SynMotorVc.flux_theta + PI4;
//                     if(SynMotorVc.flux_theta < PI4)
//                            {
//                                SynMotorVc.flux_theta = SynMotorVc.flux_theta + PI4;
//                            }
//                 }
//     }
// }
