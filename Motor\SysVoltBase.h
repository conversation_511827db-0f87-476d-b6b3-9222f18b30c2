#ifndef SYSVoltBASE_H
#define SYSVoltBASE_H
#include "at32a403a.h"
//=====================================================

typedef struct{
	void 	(*pfnCalc)();	//计算函数指针
	void	(*pfnReset)();	//复位函数指针
	void 	(*pfnInit)();	//初始化函数指针
	
	
	float fUu_base; 	//U相调制波
	float fUv_base;	//v相调制波
	float	fUw_base;	//w相调制波
	uint32_t  uCarrCount;	//载波计数最大值
	uint32_t  uChopCount;
	uint32_t  uUu_Fib;    //写入光纤板调制波数据
	uint32_t  uUv_Fib;    //写入光纤板调制波数据
	uint32_t  uUw_Fib;    //写入光纤板调制波数据
	uint32_t  chopwave_Fib;
    float fUu1_base;   //U相调制波
    float fUv1_base;   //v相调制波
    float fUw1_base;   //w相调制波
    float fUu2_base;   //U相调制波
    float fUv2_base;   //v相调制波
    float fUw2_base;   //w相调制波
    uint32_t  uUu1_Fib;    //写入光纤板调制波数据
    uint32_t  uUv1_Fib;    //写入光纤板调制波数据
    uint32_t  uUw1_Fib;    //写入光纤板调制波数据
    uint32_t  uUu2_Fib;    //写入光纤板调制波数据
    uint32_t  uUv2_Fib;    //写入光纤板调制波数据
    uint32_t  uUw2_Fib;    //写入光纤板调制波数据

}TypeVoltBaseCal;



//===================电压基波计算的默认初始化变量========================================
#define SysVoltBaseCal_DEFAULTS {\
							(void (*)(uint32_t))fnSysVoltBaseCalc, \
							(void (*)(uint32_t))fnSysVoltBaseReset, \
							(void (*)(uint32_t))fnSysVoltBaseInit, \
							0.0,0.0,0.0,25000,0,0,0,0,0,0.0,0.0,0.0,0.0,0.0,0.0,0,0,0,0,0,0\
	                            }

extern void fnSysVoltBaseInit(TypeVoltBaseCal *p);
extern void fnSysVoltBaseReset(TypeVoltBaseCal *p);
extern void fnSysVoltBaseCalc(TypeVoltBaseCal *p);
extern TypeVoltBaseCal  SysVoltBase;
#endif
