./objects/sysctl_globalvar.o: ..\..\Motor\SysCtl_GlobalVar.c \
  ..\..\libraries\cmsis\cm4\device_support\at32a403a.h \
  ..\..\libraries\cmsis\cm4\core_support\core_cm4.h \
  ..\..\libraries\cmsis\cm4\device_support\system_at32a403a.h \
  ..\..\libraries\drivers\inc\at32a403a_def.h \
  ..\..\User\src_inc\at32a403a_conf.h \
  ..\..\libraries\drivers\inc\at32a403a_adc.h \
  ..\..\libraries\drivers\inc\at32a403a_crm.h \
  ..\..\libraries\drivers\inc\at32a403a_debug.h \
  ..\..\libraries\drivers\inc\at32a403a_dma.h \
  ..\..\libraries\drivers\inc\at32a403a_exint.h \
  ..\..\libraries\drivers\inc\at32a403a_flash.h \
  ..\..\libraries\drivers\inc\at32a403a_gpio.h \
  ..\..\libraries\drivers\inc\at32a403a_misc.h \
  ..\..\libraries\drivers\inc\at32a403a_pwc.h \
  ..\..\libraries\drivers\inc\at32a403a_spi.h \
  ..\..\libraries\drivers\inc\at32a403a_tmr.h \
  ..\..\libraries\drivers\inc\at32a403a_usb.h \
  ..\..\libraries\drivers\inc\at32a403a_wdt.h \
  ..\..\Motor\SysCtl_AllHeaders.h ..\..\Motor\MathBasic.h \
  ..\..\Motor\SysCtl_SysMoore.h ..\..\Motor\SysCtl_AnalogProcess.h \
  ..\..\Motor\SysCtl_ConstDef.h ..\..\Motor\SysCtl_CsvParamDef.h \
  ..\..\Motor\Motor_VectorControl.h ..\..\Motor\SysCtl_RotorGet.h \
  ..\..\Motor\SysCtl_IoAd2s1210.h \
  ..\..\libraries\cmsis\cm4\core_support\arm_math.h \
  ..\..\libraries\cmsis\cm4\core_support\arm_math_types.h \
  ..\..\libraries\cmsis\cm4\core_support\cmsis_compiler.h \
  ..\..\libraries\cmsis\cm4\core_support\arm_math_memory.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\none.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\utils.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\basic_math_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\interpolation_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\bayes_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\statistics_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\fast_math_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\matrix_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\complex_math_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\controller_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\support_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\distance_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\svm_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\svm_defines.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\transform_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\filtering_functions.h \
  ..\..\libraries\cmsis\cm4\core_support\dsp\quaternion_math_functions.h \
  ..\..\Motor\SysCtl_GlobalVar.h ..\..\Motor\SysVoltBase.h \
  ..\..\AnoPTv8\HWInterface.h ..\..\AnoPTv8\MotorParams.h \
  ..\..\AnoPTv8\MotorData.h ..\..\User\sysTypeDef.h \
  ..\..\Drive\ad2s1212_spi.h ..\..\User\src_inc\at32a403a_wk_config.h
