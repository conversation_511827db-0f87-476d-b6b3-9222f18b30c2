<?xml version="1.0" encoding="UTF-8"?>
<Root>
    <WorkBenchVersion>
        <Version>V1.2.00</Version>
    </WorkBenchVersion>
    <MCUInfo>
        <MCUSerials>AT32A403A</MCUSerials>
        <MCUName>AT32A403ARGT7</MCUName>
        <MCUPackage>LQFP64</MCUPackage>
    </MCUInfo>
    <ADC1>
        <Mode>
            <ModeSub name="IN0" value="TRUE"/>
            <ModeSub name="IN1" value="TRUE"/>
            <ModeSub name="IN3" value="TRUE"/>
            <ModeSub name="IN5" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="OrdinaryTriggerSource" value="ADC12_ORDINARY_TRIG_SOFTWARE"/>
            <ParametersSub name="NbrOfOrdinarySequence" value="4"/>
            <ParametersSub name="Channel_OrdinarySequence_1" value="ADC_CHANNEL_0"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_1" value="ADC_SAMPLETIME_13_5"/>
            <ParametersSub name="SamplingRate_OrdinarySequence_1" value="0.615"/>
            <ParametersSub name="Channel_OrdinarySequence_2" value="ADC_CHANNEL_1"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_2" value="ADC_SAMPLETIME_13_5"/>
            <ParametersSub name="SamplingRate_OrdinarySequence_2" value="0.615"/>
            <ParametersSub name="Channel_OrdinarySequence_3" value="ADC_CHANNEL_3"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_3" value="ADC_SAMPLETIME_13_5"/>
            <ParametersSub name="SamplingRate_OrdinarySequence_3" value="0.615"/>
            <ParametersSub name="Channel_OrdinarySequence_4" value="ADC_CHANNEL_5"/>
            <ParametersSub name="SamplingTime_OrdinarySequence_4" value="ADC_SAMPLETIME_13_5"/>
            <ParametersSub name="SamplingRate_OrdinarySequence_4" value="0.615"/>
        </Parameters>
    </ADC1>
    <ADC2>
        <Mode>
            <ModeSub name="IN6" value="TRUE"/>
            <ModeSub name="IN7" value="TRUE"/>
            <ModeSub name="IN8" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="EnableOrdinaryConversion" value="DISABLE"/>
            <ParametersSub name="EnablePreemptedConversion" value="ENABLE"/>
            <ParametersSub name="PreemptedTriggerSource" value="ADC12_PREEMPT_TRIG_TMR1CH4"/>
            <ParametersSub name="PreemptedInterrupt" value="ENABLE"/>
            <ParametersSub name="NbrOfPreemptedSequence" value="3"/>
            <ParametersSub name="Channel_PreemptedSequence_1" value="ADC_CHANNEL_6"/>
            <ParametersSub name="SamplingTime_PreemptedSequence_1" value="ADC_SAMPLETIME_7_5"/>
            <ParametersSub name="SamplingRate_PreemptedSequence_1" value="0.800"/>
            <ParametersSub name="Channel_PreemptedSequence_2" value="ADC_CHANNEL_7"/>
            <ParametersSub name="SamplingTime_PreemptedSequence_2" value="ADC_SAMPLETIME_7_5"/>
            <ParametersSub name="SamplingRate_PreemptedSequence_2" value="0.800"/>
            <ParametersSub name="Channel_PreemptedSequence_3" value="ADC_CHANNEL_8"/>
            <ParametersSub name="SamplingTime_PreemptedSequence_3" value="ADC_SAMPLETIME_7_5"/>
            <ParametersSub name="SamplingRate_PreemptedSequence_3" value="0.800"/>
            <ParametersSub name="PreemptedOffset_PreemptedSequence_3" value="0x000"/>
        </Parameters>
    </ADC2>
    <CRM>
        <Mode>
            <ModeSub name="HEXT" value="HEXT-External-Oscillator"/>
        </Mode>
        <Parameters>
            <ParametersSub name="HEXT" value="HEXT_CRYSTAL"/>
        </Parameters>
    </CRM>
    <DEBUG>
        <Mode>
            <ModeSub name="Debug interface" value="SWD"/>
        </Mode>
    </DEBUG>
    <SPI3>
        <Mode>
            <ModeSub name="Mode" value="Full_Duplex_Master"/>
        </Mode>
        <Parameters>
            <ParametersSub name="TransmissionMode" value="SPI_TRANSMIT_FULL_DUPLEX"/>
            <ParametersSub name="Mode" value="SPI_MODE_MASTER"/>
            <ParametersSub name="FirstBit" value="SPI_FIRST_BIT_MSB"/>
            <ParametersSub name="ClkDivision" value="SPI_MCLK_DIV_8"/>
            <ParametersSub name="ClockFrequency" value="12000000"/>
            <ParametersSub name="CLKPhase" value="SPI_CLOCK_PHASE_2EDGE"/>
        </Parameters>
    </SPI3>
    <SYSTEM>
        <Mode>
            <ModeSub name="Time_Base" value="SysTick"/>
        </Mode>
    </SYSTEM>
    <TMR1>
        <Mode>
            <ModeSub name="Channel1 mode" value="Output_CH1_CH1C"/>
            <ModeSub name="Channel2 mode" value="Output_CH2_CH2C"/>
            <ModeSub name="Channel3 mode" value="Output_CH3_CH3C"/>
            <ModeSub name="Channel4 mode" value="Output_without_pin"/>
            <ModeSub name="Clock selection" value="Internal"/>
            <ModeSub name="Activated" value="TRUE"/>
            <ModeSub name="Enable BRK pin" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="CounterDirection" value="TMR_COUNT_TWO_WAY_2"/>
            <ParametersSub name="Period" value="4799"/>
            <ParametersSub name="RepetitionCounter" value="39"/>
            <ParametersSub name="Primary_TMR_output" value="TMR_PRIMARY_SEL_C4ORAW"/>
            <ParametersSub name="BreakState" value="TRUE"/>
            <ParametersSub name="BrakeInputValidity" value="TMR_BRK_INPUT_ACTIVE_HIGH"/>
            <ParametersSub name="WriteProtected" value="TMR_WP_LEVEL_1"/>
            <ParametersSub name="DeadTime" value="208"/>
            <ParametersSub name="DeadTimeValue" value="2000"/>
            <ParametersSub name="OCMode_1" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="OC1Buffer" value="TRUE"/>
            <ParametersSub name="OCMode_2" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="OC2Buffer" value="TRUE"/>
            <ParametersSub name="OCMode_3" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="OC3Buffer" value="TRUE"/>
            <ParametersSub name="OCMode_4" value="TMR_OUTPUT_CONTROL_PWM_MODE_B"/>
            <ParametersSub name="ChannelData_4" value="4600"/>
        </Parameters>
    </TMR1>
    <TMR3>
        <Mode>
            <ModeSub name="Multi-Channels" value="Encoder_Mode"/>
            <ModeSub name="Activated" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="Period" value="16383"/>
            <ParametersSub name="EncoderMode" value="TMR_ENCODER_MODE_C"/>
            <ParametersSub name="IC1Filter" value="11"/>
            <ParametersSub name="IC1Filter_Frequency" value="1000000"/>
            <ParametersSub name="IC2Filter" value="11"/>
            <ParametersSub name="IC2Filter_Frequency" value="1000000"/>
        </Parameters>
    </TMR3>
    <TMR6>
        <Mode>
            <ModeSub name="Activated" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="DividerValue" value="15"/>
            <ParametersSub name="Period" value="5999"/>
        </Parameters>
    </TMR6>
    <USBFS>
        <Mode>
            <ModeSub name="Device" value="TRUE"/>
        </Mode>
    </USBFS>
    <WDT>
        <Mode>
            <ModeSub name="WDT_State" value="TRUE"/>
        </Mode>
        <Parameters>
            <ParametersSub name="Reload" value="0x1FF"/>
        </Parameters>
    </WDT>
    <USB_DEVICE>
        <Mode>
            <ModeSub name="FS_Device" value="CDC"/>
        </Mode>
    </USB_DEVICE>
    <DMA>
        <ADC1>
            <ParametersSub name="Instance" value="DMA1_Channel1"/>
            <ParametersSub name="Priority" value="DMA_PRIORITY_MEDIUM"/>
        </ADC1>
    </DMA>
    <NVIC>
        <PriorityGroup>NVIC_PRIORITY_GROUP_2</PriorityGroup>
        <SysTick_Handler>1;0;0;0;0</SysTick_Handler>
        <FLASH_IRQHandler>0;0;0;0;0</FLASH_IRQHandler>
        <CRM_IRQHandler>0;0;0;0;0</CRM_IRQHandler>
        <DMA1_Channel1_IRQHandler>1;2;1;0;0</DMA1_Channel1_IRQHandler>
        <ADC1_2_IRQHandler>1;0;1;0;0</ADC1_2_IRQHandler>
        <TMR1_BRK_TMR9_IRQHandler>1;0;0;0;0</TMR1_BRK_TMR9_IRQHandler>
        <TMR1_OVF_TMR10_IRQHandler>1;0;2;0;0</TMR1_OVF_TMR10_IRQHandler>
        <TMR1_TRG_HALL_TMR11_IRQHandler>0;0;0;0;0</TMR1_TRG_HALL_TMR11_IRQHandler>
        <TMR1_CH_IRQHandler>0;0;0;0;0</TMR1_CH_IRQHandler>
        <TMR3_GLOBAL_IRQHandler>0;0;0;0;0</TMR3_GLOBAL_IRQHandler>
        <USBFSWakeUp_IRQHandler>0;0;0;0;0</USBFSWakeUp_IRQHandler>
        <SPI3_I2S3EXT_IRQHandler>0;0;0;0;0</SPI3_I2S3EXT_IRQHandler>
        <TMR6_GLOBAL_IRQHandler>1;1;3;0;0</TMR6_GLOBAL_IRQHandler>
        <USBFS_MAPH_IRQHandler>0;0;0;0;0</USBFS_MAPH_IRQHandler>
        <USBFS_MAPL_IRQHandler>1;2;2;0;0</USBFS_MAPL_IRQHandler>
    </NVIC>
    <GPIO>
        <Signal SignalName="GPIO_Output" PinName="PC14">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="Label" value="TEMP_Fail"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PC15">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="Label" value="LS_Fail"/>
        </Signal>
        <Signal SignalName="ADC1_IN0" PinName="PA0">
            <Parameters name="Label" value="M_PT0"/>
        </Signal>
        <Signal SignalName="ADC1_IN1" PinName="PA1">
            <Parameters name="Label" value="M_PT1"/>
        </Signal>
        <Signal SignalName="ADC1_IN3" PinName="PA3">
            <Parameters name="Label" value="B_PT0"/>
        </Signal>
        <Signal SignalName="ADC1_IN5" PinName="PA5">
            <Parameters name="Label" value="U_DC"/>
        </Signal>
        <Signal SignalName="ADC2_IN6" PinName="PA6">
            <Parameters name="Label" value="ADC2_IDC"/>
        </Signal>
        <Signal SignalName="ADC2_IN7" PinName="PA7">
            <Parameters name="Label" value="ADC2_IU"/>
        </Signal>
        <Signal SignalName="ADC2_IN8" PinName="PB0">
            <Parameters name="Label" value="ADC2_IV"/>
        </Signal>
        <Signal SignalName="TMR3_CH1" PinName="PC6">
            <Parameters name="Label" value="ENC_A"/>
        </Signal>
        <Signal SignalName="TMR3_CH2" PinName="PC7">
            <Parameters name="Label" value="ENA_B"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PC8">
            <Parameters name="Label" value="ENC_Z"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PC9">
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="RDC_Reset"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PA15">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="Label" value="RDC_CS"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PC10">
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="RDC_WR"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PC11">
            <Parameters name="Label" value="RDC_IN_DOS"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PC12">
            <Parameters name="Label" value="RDC_IN_LOT"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PD2">
            <Parameters name="GPIO_Outputlevel" value="GPIO_OUTPUTLEVEL_HIGH"/>
            <Parameters name="GPIO_DriverCapability" value="GPIO_DRIVE_STRENGTH_STRONGER"/>
            <Parameters name="Label" value="RDC_SAMPLE"/>
        </Signal>
        <Signal SignalName="SPI3_SCK" PinName="PB3">
            <Parameters name="Label" value="RDC_SCK"/>
        </Signal>
        <Signal SignalName="SPI3_MISO" PinName="PB4">
            <Parameters name="Label" value="RDC_MISO"/>
        </Signal>
        <Signal SignalName="SPI3_MOSI" PinName="PB5">
            <Parameters name="Label" value="RDC_MOSI"/>
        </Signal>
        <Signal SignalName="GPIO_Input" PinName="PB7">
            <Parameters name="Label" value="RDC_IN_DIR"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PB8">
            <Parameters name="Label" value="RDC_A1"/>
        </Signal>
        <Signal SignalName="GPIO_Output" PinName="PB9">
            <Parameters name="Label" value="RDC_A0"/>
        </Signal>
    </GPIO>
    <ClockConfiguration>
        <rtcsel>0</rtcsel>
        <hext>12.000000</hext>
        <hextdiv>2</hextdiv>
        <pllhextdiv>0</pllhextdiv>
        <pllrcs>0</pllrcs>
        <pllmult>16</pllmult>
        <sclkselect>1</sclkselect>
        <ahbdiv>1</ahbdiv>
        <apb1div>2</apb1div>
        <apb2div>2</apb2div>
        <usbdiv>4.0</usbdiv>
        <hicktousb>0</hicktousb>
        <hicktosclk>0</hicktosclk>
        <clkout>0</clkout>
        <clkoutdiv>1</clkoutdiv>
        <adcdiv>6</adcdiv>
        <systicsel>8</systicsel>
    </ClockConfiguration>
    <PINInfo>
        <PinSub pinname="PC14" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PC15" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PD0/HEXT_IN" signalname="CRM_HEXT_IN" signaltype="2"/>
        <PinSub pinname="PD1/HEXT_OUT" signalname="CRM_HEXT_OUT" signaltype="2"/>
        <PinSub pinname="PA0" signalname="ADC1_IN0" signaltype="3"/>
        <PinSub pinname="PA1" signalname="ADC1_IN1" signaltype="3"/>
        <PinSub pinname="PA3" signalname="ADC1_IN3" signaltype="3"/>
        <PinSub pinname="PA5" signalname="ADC1_IN5" signaltype="3"/>
        <PinSub pinname="PA6" signalname="ADC2_IN6" signaltype="3"/>
        <PinSub pinname="PA7" signalname="ADC2_IN7" signaltype="3"/>
        <PinSub pinname="PB0" signalname="ADC2_IN8" signaltype="3"/>
        <PinSub pinname="PB12" signalname="TMR1_BRK" signaltype="3"/>
        <PinSub pinname="PB13" signalname="TMR1_CH1C" signaltype="2"/>
        <PinSub pinname="PB14" signalname="TMR1_CH2C" signaltype="2"/>
        <PinSub pinname="PB15" signalname="TMR1_CH3C" signaltype="2"/>
        <PinSub pinname="PC6" signalname="TMR3_CH1" signaltype="3"/>
        <PinSub pinname="PC7" signalname="TMR3_CH2" signaltype="3"/>
        <PinSub pinname="PC8" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PC9" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PA8" signalname="TMR1_CH1" signaltype="2"/>
        <PinSub pinname="PA9" signalname="TMR1_CH2" signaltype="2"/>
        <PinSub pinname="PA10" signalname="TMR1_CH3" signaltype="2"/>
        <PinSub pinname="PA11" signalname="USBFS_D-" signaltype="2"/>
        <PinSub pinname="PA12" signalname="USBFS_D+" signaltype="2"/>
        <PinSub pinname="PA13" signalname="DEBUG_JTMS_SWDIO" signaltype="2"/>
        <PinSub pinname="PA14" signalname="DEBUG_JTCK_SWCLK" signaltype="2"/>
        <PinSub pinname="PA15" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PC10" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PC11" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PC12" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PD2" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PB3" signalname="SPI3_SCK" signaltype="3"/>
        <PinSub pinname="PB4" signalname="SPI3_MISO" signaltype="2"/>
        <PinSub pinname="PB5" signalname="SPI3_MOSI" signaltype="2"/>
        <PinSub pinname="PB7" signalname="GPIO_Input" signaltype="3"/>
        <PinSub pinname="PB8" signalname="GPIO_Output" signaltype="3"/>
        <PinSub pinname="PB9" signalname="GPIO_Output" signaltype="3"/>
    </PINInfo>
    <ProjectInfomation>
        <ProjectName>AT32A403ARGT7_154_33</ProjectName>
        <ProjectLocation>E:/AT32_Work/AT32A403ARG_154_33_Work</ProjectLocation>
        <ToolchainIDE>MDK_V5</ToolchainIDE>
        <ARMCompiler>0</ARMCompiler>
        <KeepUserCode>true</KeepUserCode>
        <NotUsedPinAnalog>true</NotUsedPinAnalog>
        <CodeSplitIP>false</CodeSplitIP>
        <AddNecessaryFileFlag>true</AddNecessaryFileFlag>
        <MinHeapSize>0x400</MinHeapSize>
        <MinStackSize>0x400</MinStackSize>
        <UseFirmware>true</UseFirmware>
        <PackageVersion>V2.0.6</PackageVersion>
    </ProjectInfomation>
</Root>
