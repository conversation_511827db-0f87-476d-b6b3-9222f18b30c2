/**
 * @file sensor_proc.c
 * @brief 通用ADC传感器处理模块实现
 * @version 1.0
 * @date 2025-07-22
 * 
 * @description
 * 本模块实现了一个通用的ADC传感器处理系统，支持：
 * - 多种单位转换方法（线性转换、查表法）
 * - 多种数字滤波算法（IIR、滑动平均）
 * - 传感器故障诊断（开路、短路检测）
 * - 配置驱动的架构设计
 */

#include "sensor_proc.h"
#include "PT1000_table.h"
#include "system_status.h"
#include <math.h>
#include <string.h>
#include "MotorData.h"
/* ======================== 内部数据结构定义 ======================== */

/**
 * @brief 线性转换参数结构
 */
typedef struct {
    float coeff_a;      // 线性系数a (斜率)
    float coeff_b;      // 线性系数b (偏移)
} linear_convert_t;

/**
 * @brief 转换参数联合体
 */
typedef union {
    linear_convert_t    linear;     // 线性转换参数
    lookup_table_t      table;      // 查表法参数
} convert_param_t;

/**
 * @brief 传感器静态配置结构（存储在ROM中）
 */
typedef struct {
    sensor_id_t         id;             // 传感器ID
    uint8_t             adc_channel;    // ADC通道索引（在扫描序列中的位置）
    convert_method_t    convert_method; // 转换方法
    convert_param_t     convert_param;  // 转换参数
    filter_method_t     filter_method;  // 滤波方法
    float               filter_param;   // 滤波参数（IIR的alpha或移动平均窗口大小）
    float               min_value;      // 最小有效物理值
    float               max_value;      // 最大有效物理值
    uint16_t            open_threshold; // 开路检测阈值
    uint16_t            short_threshold;// 短路检测阈值
} sensor_config_t;

/**
 * @brief 滑动平均滤波器状态结构
 */
typedef struct {
    float   buffer[MOVING_AVG_WINDOW_SIZE]; // 数据缓冲区
    uint8_t index;                          // 当前索引
    uint8_t count;                          // 有效数据计数
    float   sum;                            // 数据总和
} moving_avg_state_t;

/**
 * @brief 滤波器状态联合体
 */
typedef union {
    float               iir_prev;       // IIR滤波器上一次输出值
    moving_avg_state_t  moving_avg;     // 滑动平均滤波器状态
} filter_state_t;

/**
 * @brief 传感器动态状态结构（存储在RAM中）
 */
typedef struct {
    sensor_status_t     status;         // 当前状态
    uint16_t            raw_adc;        // 原始ADC值
    float               converted_value;// 转换后的物理值
    float               filtered_value; // 滤波后的物理值
    filter_state_t      filter_state;   // 滤波器内部状态
    uint32_t            error_count;    // 错误计数
    uint8_t             init_flag;      // 初始化标志
} sensor_state_t;

/* ======================== PT1000查表数据 ======================== */
// 使用PT1000_table.c中的查表数据和函数

/* ======================== 静态配置表 ======================== */
/**
 * @brief 传感器静态配置表（存储在ROM中）
 * @note 可根据实际需求修改此配置表
 */
static const sensor_config_t sensor_configs[SENSOR_COUNT] = {
    // 电机温度传感器0 - PT1000（使用查表法）
    {
        .id = SENSOR_M_PT0,
        .adc_channel = 0,
        .convert_method = CONVERT_METHOD_TABLE,
        .convert_param.table = {
            .points = NULL,  // 使用PT1000_ADC_to_Temperature函数
            .point_count = 0
        },
        .filter_method = FILTER_METHOD_MOVING_AVG,
        .filter_param = MOVING_AVG_WINDOW_SIZE,
        .min_value = PT1000_MIN_TEMP,
        .max_value = PT1000_MAX_TEMP,
        .open_threshold = ADC_OPEN_THRESHOLD,
        .short_threshold = ADC_SHORT_THRESHOLD
    },
    
    // 电机温度传感器1 - PT1000（使用查表法）
    {
        .id = SENSOR_M_PT1,
        .adc_channel = 1,
        .convert_method = CONVERT_METHOD_TABLE,
        .convert_param.table = {
            .points = NULL,  // 将使用PT1000_ADC_to_Temperature函数
            .point_count = 0
        },
        .filter_method = FILTER_METHOD_MOVING_AVG,
        .filter_param = MOVING_AVG_WINDOW_SIZE,
        .min_value = PT1000_MIN_TEMP,
        .max_value = PT1000_MAX_TEMP,
        .open_threshold = ADC_OPEN_THRESHOLD,
        .short_threshold = ADC_SHORT_THRESHOLD
    },
    
    // 板上温度传感器0 - PT1000（使用查表法）
    {
        .id = SENSOR_B_PT0,
        .adc_channel = 2,
        .convert_method = CONVERT_METHOD_TABLE,
        .convert_param.table = {
            .points = NULL,  // 将使用PT1000_ADC_to_Temperature函数
            .point_count = 0
        },
        .filter_method = FILTER_METHOD_MOVING_AVG,
        .filter_param = MOVING_AVG_WINDOW_SIZE,
        .min_value = PT1000_MIN_TEMP,
        .max_value = PT1000_MAX_TEMP,
        .open_threshold = ADC_OPEN_THRESHOLD,
        .short_threshold = ADC_SHORT_THRESHOLD
    },
    
    // 母线电压传感器
    {
        .id = SENSOR_U_DC,
        .adc_channel = 3,
        .convert_method = CONVERT_METHOD_LINEAR,
        .convert_param.linear = {
            .coeff_a = U_DC_LINEAR_COEFF_A,
            .coeff_b = U_DC_LINEAR_COEFF_B
        },
        .filter_method = FILTER_METHOD_IIR,
        .filter_param = 0.15f,  // 一阶低通滤波器系数
        .min_value = U_DC_MIN_VOLTAGE,
        .max_value = U_DC_MAX_VOLTAGE,
        .open_threshold = ADC_OPEN_THRESHOLD,
        .short_threshold = ADC_SHORT_THRESHOLD
    }
};

/* ======================== 模块内部变量 ======================== */
sensor_state_t sensor_states[SENSOR_COUNT];     // 传感器动态状态表
static uint16_t *adc_dma_buffer = NULL;                // ADC DMA缓冲区指针
static uint8_t module_initialized = 0;                 // 模块初始化标志
static uint32_t total_error_count = 0;                 // 总错误计数
static uint8_t dma_data_ready = 0;                     // DMA数据就绪标志

/* ======================== 内部函数声明 ======================== */
static sensor_status_t diagnose_sensor(uint16_t adc_value, const sensor_config_t *config);
static float convert_adc_to_physical(uint16_t adc_value, const sensor_config_t *config);
static float apply_filter(float input_value, sensor_state_t *state, const sensor_config_t *config);
static void reset_filter_state(sensor_state_t *state, filter_method_t method);

/* ======================== 公开API函数实现 ======================== */

/**
 * @brief 初始化传感器处理模块
 */
int sensor_proc_init(uint16_t *adc_buffer)
{
    if (adc_buffer == NULL) {
        return -1;
    }

    // 保存ADC缓冲区指针
    adc_dma_buffer = adc_buffer;

    // 初始化所有传感器状态
    for (int i = 0; i < SENSOR_COUNT; i++) {
        sensor_states[i].status = SENSOR_STATUS_INIT;
        sensor_states[i].raw_adc = 0;
        sensor_states[i].converted_value = 0.0f;
        sensor_states[i].filtered_value = 0.0f;
        sensor_states[i].error_count = 0;
        sensor_states[i].init_flag = 0;

        // 重置滤波器状态
        reset_filter_state(&sensor_states[i], sensor_configs[i].filter_method);
    }

    // 重置模块统计信息
    total_error_count = 0;
    dma_data_ready = 0;

    module_initialized = 1;
    return 0;
}

/**
 * @brief 传感器处理任务
 */
void sensor_proc_task(void)
{
    if (!module_initialized || adc_dma_buffer == NULL) {
        return;
    }

    // 检查DMA数据是否就绪
    if (!dma_data_ready) {
        return;
    }

    // 清除DMA数据就绪标志
    dma_data_ready = 0;

    // 处理每个传感器
    for (int i = 0; i < SENSOR_COUNT; i++) {
        const sensor_config_t *config = &sensor_configs[i];
        sensor_state_t *state = &sensor_states[i];

        // 从DMA缓冲区读取ADC原始值
        uint16_t adc_value = adc_dma_buffer[config->adc_channel];
        state->raw_adc = adc_value;

        // 步骤1: 故障诊断
        sensor_status_t diag_status = diagnose_sensor(adc_value, config);

        if (diag_status != SENSOR_STATUS_OK) {
            // 传感器故障，更新状态并增加错误计数
            state->status = diag_status;
            state->error_count++;
            total_error_count++;
            continue; // 跳过后续处理
        }

        // 步骤2: 单位转换
        float converted_value = convert_adc_to_physical(adc_value, config);
        state->converted_value = converted_value;

        // 检查转换后的值是否在合理范围内
        if (converted_value < config->min_value || converted_value > config->max_value) {
            state->status = SENSOR_STATUS_OUT_RANGE;
            state->error_count++;
            total_error_count++;
            continue;
        }

        // 步骤3: 数字滤波
        float filtered_value = apply_filter(converted_value, state, config);
        state->filtered_value = filtered_value;

        // 更新传感器状态为正常
        state->status = SENSOR_STATUS_OK;
        state->init_flag = 1;
    }
    UpdateF2SensorData();
}

/**
 * @brief 获取指定传感器的处理结果
 */
int sensor_get_result(sensor_id_t sensor_id, sensor_result_t *result)
{
    if (sensor_id >= SENSOR_COUNT || result == NULL ) {
        return -1;
    }

    const sensor_state_t *state = &sensor_states[sensor_id];

    result->value = state->filtered_value;
    result->status = state->status;
    result->raw_adc = state->raw_adc;
    result->error_count = state->error_count;

    return 0;
}

/**
 * @brief 获取指定传感器的物理值
 */
sys_status_t sensor_get_value(sensor_id_t sensor_id, float *value)
{
    if (sensor_id >= SENSOR_COUNT || value == NULL) {
        return SYS_STATUS_INVALID_PARAM;
    }

    const sensor_state_t *state = &sensor_states[sensor_id];

    if (!state->init_flag) {
        return SYS_STATUS_SENSOR_NOT_READY;
    }

    if (state->status != SENSOR_STATUS_OK) {
        switch (state->status) {
            case SENSOR_STATUS_OPEN:
                return SYS_STATUS_SENSOR_OPEN_CIRCUIT;
            case SENSOR_STATUS_SHORT:
                return SYS_STATUS_SENSOR_SHORT_CIRCUIT;
            case SENSOR_STATUS_OUT_RANGE:
                return SYS_STATUS_SENSOR_OUT_OF_RANGE;
            default:
                return SYS_STATUS_SENSOR_FAULT;
        }
    }

    *value = state->filtered_value;
    return SYS_STATUS_OK;
}

/**
 * @brief 获取指定传感器的状态
 */
sensor_status_t sensor_get_status(sensor_id_t sensor_id)
{
    if (sensor_id >= SENSOR_COUNT ) {
        return SENSOR_STATUS_INIT;
    }

    return sensor_states[sensor_id].status;
}

/**
 * @brief 获取指定传感器的原始ADC值
 */
uint16_t sensor_get_raw_adc(sensor_id_t sensor_id)
{
    if (sensor_id >= SENSOR_COUNT ) {
        return 0;
    }

    return sensor_states[sensor_id].raw_adc;
}

/**
 * @brief 重置指定传感器的错误计数
 */
void sensor_reset_error_count(sensor_id_t sensor_id)
{
    if (sensor_id >= SENSOR_COUNT ) {
        return;
    }

    sensor_states[sensor_id].error_count = 0;
}

/**
 * @brief 获取模块运行状态信息
 */
void sensor_get_debug_info(uint32_t *total_errors)
{
    if (total_errors != NULL) {
        *total_errors = total_error_count;
    }
}

/**
 * @brief 设置DMA数据就绪标志（由DMA中断调用）
 */
void sensor_set_dma_ready(void)
{
    dma_data_ready = 1;
}

/* ======================== 内部函数实现 ======================== */

/**
 * @brief 传感器故障诊断
 */
static sensor_status_t diagnose_sensor(uint16_t adc_value, const sensor_config_t *config)
{
    // 检查开路故障（ADC值过低）
    if (adc_value < config->open_threshold) {
        return SENSOR_STATUS_OPEN;
    }

    // 检查短路故障（ADC值过高）
    if (adc_value > config->short_threshold) {
        return SENSOR_STATUS_SHORT;
    }

    return SENSOR_STATUS_OK;
}

/**
 * @brief ADC值转换为物理值
 */
static float convert_adc_to_physical(uint16_t adc_value, const sensor_config_t *config)
{
    switch (config->convert_method) {
        case CONVERT_METHOD_LINEAR:
            // 线性转换: y = ax + b
            return config->convert_param.linear.coeff_a * adc_value +
                   config->convert_param.linear.coeff_b;

        case CONVERT_METHOD_TABLE:
            // 对于PT1000传感器，使用专用的查表函数
            if (config->id == SENSOR_M_PT0 || config->id == SENSOR_M_PT1 || config->id == SENSOR_B_PT0) {
                return PT1000_ADC_to_Temperature(adc_value);
            } else {
                // 其他传感器使用通用线性插值
                return 0.0f;
            }

        default:
            return 0.0f;
    }
}

/**
 * @brief 应用数字滤波
 */
static float apply_filter(float input_value, sensor_state_t *state, const sensor_config_t *config)
{
    switch (config->filter_method) {
        case FILTER_METHOD_NONE:
            // 无滤波，直接返回输入值
            return input_value;

        case FILTER_METHOD_IIR:
            // 一阶低通滤波器: y[n] = α*x[n] + (1-α)*y[n-1]
            if (!state->init_flag) {
                // 首次运行，直接使用输入值初始化
                state->filter_state.iir_prev = input_value;
                return input_value;
            } else {
                float alpha = config->filter_param;
                float output = alpha * input_value + (1.0f - alpha) * state->filter_state.iir_prev;
                state->filter_state.iir_prev = output;
                return output;
            }

        case FILTER_METHOD_MOVING_AVG:
            // 滑动平均滤波器
            {
                moving_avg_state_t *avg_state = &state->filter_state.moving_avg;

                // 更新总和
                if (avg_state->count < MOVING_AVG_WINDOW_SIZE) {
                    // 缓冲区未满，直接累加
                    avg_state->sum += input_value;
                    avg_state->count++;
                } else {
                    // 缓冲区已满，减去即将被覆盖的旧值，加上新值
                    avg_state->sum = avg_state->sum - avg_state->buffer[avg_state->index] + input_value;
                }

                // 添加新数据到缓冲区（覆盖旧值）
                avg_state->buffer[avg_state->index] = input_value;

                // 更新索引
                avg_state->index = (avg_state->index + 1) % MOVING_AVG_WINDOW_SIZE;

                // 返回平均值
                return avg_state->sum / avg_state->count;
            }

        default:
            return input_value;
    }
}

/**
 * @brief 重置滤波器状态
 */
static void reset_filter_state(sensor_state_t *state, filter_method_t method)
{
    switch (method) {
        case FILTER_METHOD_IIR:
            state->filter_state.iir_prev = 0.0f;
            break;

        case FILTER_METHOD_MOVING_AVG:
            {
                moving_avg_state_t *avg_state = &state->filter_state.moving_avg;
                memset(avg_state->buffer, 0, sizeof(avg_state->buffer));
                avg_state->index = 0;
                avg_state->count = 0;
                avg_state->sum = 0.0f;
            }
            break;

        default:
            break;
    }
}
