#ifndef __ENC_SPEED_H
#define __ENC_SPEED_H

#include "at32a403a_wk_config.h"
#include "ad2s1212_spi.h"
#include "arm_math.h"

/*============================ 系统参数 ============================*/
/* 采样周期配置 */
#define SAMPLE_TIME           0.00005f  // 采样时间 50us (20kHz)
#define SAMPLE_TIME_US        50        // 采样时间(微秒)

/* 速度环配置 */
#define SPEED_LOOP_TIME       0.00025f  // 速度环周期 250us (4kHz)
#define SPEED_LOOP_TIME_US    250       // 速度环周期(微秒)
#define SPEED_LOOP_DIVIDER    5         // 速度环分频比 (250us/50us)

/*============================ 电机参数 ============================*/
#define MOTOR_POLE_PAIRS      4         // 电机极对数

/*============================ 编码器/旋变参数 ====================*/
#define RESOLVER_POLE_PAIRS   4         // 旋变极对数
#define RESOLVER_BITS         12        // 旋变分辨率(位)
#define RESOLVER_MAX_COUNT    4096      // 旋变最大计数值(2^12)
#define ALIGN_POSITION        2441      // 磁链对齐零位置
#define TIM3_COUNTER_PERIOD   16383     // TIM3计数器周期值(4096*4-1)
#define MECH_CYCLE_COUNT      16384     // 一个机械周期的计数值(4096*4)
#define COUNTS_PER_ELEC_REV   4096      // 电角度每圈计数，等于RESOLVER_MAX_COUNT
#define COUNTS_PER_MECH_REV   16384     // 机械角度每圈计数
#define COUNTS_PER_MECH_REV_HALF 8192   // 机械角度每圈计数的一半

/*============================ Z相参数 ============================*/
#define Z_PHASE_AS_ABS_ZERO   0         // Z相绝对零位使能
#define Z_PHASE_COUNT_PER_REV 1         // 每圈Z相脉冲数
#define Z_PHASE_EXPECTED_POS  13991     // Z相绝对零位(0-2393+周期)的TIM3计数值

/*============================ 转换系数 ===========================*/
/* 角度转换系数 */
#define ANGLE_CONVERSION_FACTOR      0.001534f       // 电角度转换因子(2π/4096)
#define MECHANICAL_ANGLE_FACTOR      0.0003835f      // 机械角度转换因子(2π/16384)

/*============================ 速度计算参数 =======================*/
/* 基础转换常数 */
#define RAD_TO_RPS                   0.159154943f    // 1/(2*PI) - 转换rad/s到RPS
#define RPS_TO_RPM                   60.0f           // 60 - 转换RPS到RPM
#define RAD_TO_RPM                   9.549296586f    // 60/(2*PI) - 转换rad/s到RPM
#define INV_RAD_TO_RPM               0.10471975512f  // 1/RAD_TO_RPM - 转换RPM到rad/s

/*============================ 滤波器参数 =========================*/
#define DEFAULT_FILTER_ALPHA   0.3f                  // 低通滤波器默认系数
#define DEFAULT_FILTER_SIZE    5                     // 滑动平均滤波器默认大小

/* 卡尔曼滤波器参数初始化 */
#define DEFAULT_KALMAN_Q_VEL       0.005f    // 速度过程噪声
#define DEFAULT_KALMAN_Q_ACC       0.002f    // 加速度过程噪声
#define DEFAULT_KALMAN_R_MEASURE   0.2f      // 测量噪声
#define DEFAULT_KALMAN_P0_INIT     0.5f      // 初始协方差

/* 简化自适应滤波参数（两档切换）*/
#define SPEED_DEADBAND_RPS         0.05f     // 速度死区阈值 (3RPM，原来0.5f太大)
#define SPEED_ACTIVE_THRESHOLD     0.5f      // 运行状态阈值（30RPM，原来1.0f太大）

/* 准静止状态参数（高稳定性）*/
#define KALMAN_Q_VEL_STABLE        0.001f    // 准静止时速度过程噪声
#define KALMAN_Q_ACC_STABLE        0.0005f   // 准静止时加速度过程噪声  
#define KALMAN_R_STABLE            0.05f     // 准静止时测量噪声
#define KALMAN_P0_STABLE           0.1f      // 准静止时初始协方差

/* 运行状态参数（快速响应）*/
#define KALMAN_Q_VEL_ACTIVE        0.005f    // 运行时速度过程噪声
#define KALMAN_Q_ACC_ACTIVE        0.002f    // 运行时加速度过程噪声
#define KALMAN_R_ACTIVE            0.2f      // 运行时测量噪声  
#define KALMAN_P0_ACTIVE           0.5f      // 运行时初始协方差

/* 兼容性定义 */
#define DEFAULT_KALMAN_R          DEFAULT_KALMAN_R_MEASURE
#define DEFAULT_KALMAN_P0         DEFAULT_KALMAN_P0_INIT

/* 编码器默认参数 */
#define DEFAULT_PULSE_PER_REV     16384               // 默认每圈脉冲数
#define DEFAULT_ANGLE_DIFF_THRESHOLD  0.01f          // 默认角度差判断阈值

/* FHAN参数兼容性定义 */
#define DEFAULT_FHAN_R0           FHAN_DEFAULT_R0
#define DEFAULT_FHAN_MAX_ACCEL    FHAN_MAX_ACCEL_DEFAULT

/* PI控制器参数兼容性定义 */
#define DEFAULT_SPEED_PI_KP           PI_DEFAULT_KP
#define DEFAULT_SPEED_PI_KI           PI_DEFAULT_KI
#define DEFAULT_SPEED_PI_MAX_INTEGRAL PI_DEFAULT_MAX_INTEGRAL
#define DEFAULT_SPEED_PI_MAX_OUTPUT   PI_DEFAULT_MAX_OUTPUT

/* 滤波器类型 */
typedef enum {
    FILTER_NONE,              // 无滤波
    FILTER_LOWPASS,           // 一阶低通滤波
    FILTER_MOVING_AVG,        // 滑动平均滤波
    FILTER_KALMAN             // 卡尔曼滤波器(速度+加速度)
} FilterType_t;

/* 低通滤波器 */
typedef struct {
    float output;             // 滤波输出
    float last_output;        // 上次输出
    float alpha;              // 滤波系数(0-1)
    float one_minus_alpha;    // 预计算的(1-alpha)值
} LowPassFilter_t;

/* 滑动平均滤波器 */
typedef struct {
    float *buffer;            // 数据缓冲区
    uint16_t size;            // 缓冲区大小
    uint16_t index;           // 当前索引
    float sum;                // 数据和
    float inv_size;           // 预计算的1/size值
} MovingAvgFilter_t;

/* 卡尔曼滤波器结构体 */
typedef struct {
    float x[2];               // 状态变量[速度, 加速度]
    float p[2][2];            // 状态协方差矩阵
    float q[2];               // 过程噪声协方差
    float r;                  // 测量噪声协方差
    float k[2];               // 卡尔曼增益
    
    /* 预计算常量 */
    float dt;                 // 固定时间间隔
    float dt_sq;              // dt的平方
    float dt_half;            // dt/2
    float dt_sq_half;         // dt²/2
    float inv_dt;             // 1/dt
    float inv_r;              // 1/r
    
    /* ARM DSP矩阵实例 */
    arm_matrix_instance_f32 F_matrix;     // 状态转移矩阵 2x2
    arm_matrix_instance_f32 P_matrix;     // 协方差矩阵 2x2
    arm_matrix_instance_f32 Q_matrix;     // 过程噪声矩阵 2x2
    arm_matrix_instance_f32 H_matrix;     // 观测矩阵 1x2
    arm_matrix_instance_f32 R_matrix;     // 测量噪声矩阵 1x1
    arm_matrix_instance_f32 K_matrix;     // 卡尔曼增益矩阵 2x1
    
    /* 矩阵数据存储 */
    float F_data[4];          // F矩阵数据 [1, dt; 0, 1]
    float Q_data[4];          // Q矩阵数据 [q_vel, 0; 0, q_acc]
    float H_data[2];          // H矩阵数据 [1, 0]
    float R_data[1];          // R矩阵数据 [r]
    
    /* 临时矩阵存储 */
    float temp1_data[4];      // 临时矩阵1 2x2
    float temp2_data[4];      // 临时矩阵2 2x2
    float temp3_data[2];      // 临时矩阵3 2x1
    arm_matrix_instance_f32 temp1_matrix;
    arm_matrix_instance_f32 temp2_matrix;
    arm_matrix_instance_f32 temp3_matrix;
} KalmanFilter_t;

/* 角度计算结构体 */
typedef struct {
    float elec_angle;           // 电角度 (0-2π)
    float mech_angle;           // 机械角度 (0-2π)
    float total_mech_angle;     // 累积机械角度 (无限制)
    
    // 传统溢出检测系统
    int32_t overflow_count;     // 溢出计数器
    uint16_t last_timer_count;  // 上次定时器计数值
    
    // 性能优化预计算常量
    float mech_angle_factor_fast;      // 机械角度转换因子
} AngleData_t;

/* 角度数据快照结构体 - 用于多中断环境数据同步 */
typedef struct {
    float total_mech_angle;     // 累积机械角度快照
    float mech_angle;           // 机械角度快照
    float elec_angle;           // 电角度快照
} AngleSnapshot_t;

/* 速度计算结构体 */
typedef struct {
    float signed_speed_rps;    // 带符号转速(rps)
    float signed_speed_rpm;    // 带符号转速(rpm)
    float last_mech_angle;     // 上次机械角度
    float last_total_angle;    // 上次总角度
} SpeedData_t;

/* 卡尔曼滤波器函数 */
void InitKalmanFilter(float q_vel, float q_acc, float r, float p0);
void SetKalmanFilterParams(float q_vel, float q_acc, float r, float p0);



/*============================ FHAN路径规划参数 ======================*/
/* FHAN算法参数 */
#define FHAN_DEFAULT_R0          100.0f    // 速度因子
#define FHAN_DEFAULT_H0          0.00025f  // 积分步长
#define FHAN_MAX_ACCEL_DEFAULT   400.0f    // 最大加速度限制

/* FHAN滤波器结构体 */
typedef struct {
    float x1;            // 位置状态(当前输出速度 RPS)
    float x2;            // 速度状态(当前加速度 RPS/s) 
    float r0;            // 速度因子参数
    float h0;            // 步长参数
    float max_accel;     // 最大加速度限制
    uint8_t enabled;     // 使能标志：1=启用fhan，0=直通
    
    /* 预计算常量 */
    float h0_sq;         // h0的平方
    float r0_h0;         // r0*h0
} FhanFilter_t;

/*============================ PI控制器参数 ===========================*/
/* PI控制器设计参数 */
#define PI_BANDWIDTH_TARGET      20.0f     // PI控制器目标带宽(Hz)
#define PI_INTEGRAL_SEPARATION   5.0f      // 积分分离系数
#define SYSTEM_INERTIA_KGM2      8.113f    // 系统总转动惯量
#define MOTOR_KT_AVERAGE         0.38f     // 电机平均转矩常数

/* PI控制器增益配置 */
#define PI_DEFAULT_KP            3.5f      // 比例增益
#define PI_DEFAULT_KI            45.0f     // 积分增益(连续域)
#define PI_DEFAULT_KI_TS         (PI_DEFAULT_KI * SPEED_LOOP_TIME)  // 离散积分增益
#define PI_DEFAULT_MAX_INTEGRAL  30.0f     // 积分限幅
#define PI_DEFAULT_MAX_OUTPUT    180.0f    // 输出限幅
#define PI_INTEGRAL_LEAK_RATE    0.9999f   // 积分泄漏系数

/* 前馈补偿系数 */
#define FEEDFORWARD_COEFF        15.0f     // 前馈系数

/* PI控制器结构体 */
typedef struct {
    float kp;                // 比例增益
    float ki;                // 积分增益(连续域)
    float ki_ts;             // 离散积分增益
    float integral_sum;      // 积分累加和
    float max_integral_sum;  // 积分累加和限幅
    float max_output;        // 输出限幅
    float leak_rate;         // 积分泄漏系数
    float output;            // PI输出
    float feedforward;       // 前馈补偿
    float total_output;      // 总输出
    uint8_t enabled;         // 使能标志
    uint8_t reset_integral;  // 积分复位标志
    uint8_t ff_enabled;      // 前馈使能标志
    
    /* 电流环安全读取保护 */
    volatile float current_command_safe;  // 电流指令安全副本
    volatile uint8_t update_flag;         // 更新标志位
} PIController_t;

/* 编码器处理结构体 */
typedef struct {
    uint16_t resolver_pos;    // 旋变绝对位置
    
    AngleData_t angle;        // 角度数据
    SpeedData_t speed;        // 速度数据
    
    FilterType_t filter_type; // 滤波器类型
    LowPassFilter_t lp_filter;   // 低通滤波器
    MovingAvgFilter_t ma_filter; // 滑动平均滤波器
    KalmanFilter_t kalman;       // 卡尔曼滤波器
    
    FhanFilter_t fhan;        // FHAN路径规划滤波器
    PIController_t speed_pi;  // 速度PI控制器
    
    /* 简化的Z相计数器 */
    uint32_t z_pulse_counter;  // Z相脉冲计数器：每次Z相触发+1，不参与角度计算
} EncoderHandler_t;

/* 外部变量声明 */
extern EncoderHandler_t g_encoder;

/* 接口函数声明 */
void EncoderSpeed_Init(void);
void EncoderSpeed_Reset(void);

/**
 * @brief 完整的速度环系统初始化
 * @param filter_type 速度滤波器类型选择
 * @param enable_fhan 是否启用FHAN路径规划 (1=启用, 0=禁用)
 * @param enable_feedforward 是否启用前馈补偿 (1=启用, 0=禁用)
 * @note 一次性完成编码器、滤波器、FHAN、PI控制器的初始化和配置
 *       避免在主函数中重复调用多个配置函数
 */
void SpeedLoopSystem_Init(FilterType_t filter_type, uint8_t enable_fhan, uint8_t enable_feedforward);

/**
 * @brief 获取电角度（主接口函数）
 * @return 电角度(0-2π)
 * @note 在20kHz电流环中断中调用，只负责角度计算和更新
 *       不再包含速度计算逻辑
 */
float GetElectricalAngle_ENC(void);

/**
 * @brief 速度计算函数（新增）
 * @note 在4kHz速度环定时器中调用，专门负责速度计算
 *       卡尔曼滤波->FHAN路径规划->PI控制器(500Hz带宽)
 */
void CalculateSpeed_4KHz(void);

/**
 * @brief 获取带方向的转速(RPS)
 * @return 带方向的转速(rps)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPS(void);

/**
 * @brief 获取带方向的转速(RPM)
 * @return 带方向的转速(rpm)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPM(void);

/**
 * @brief 获取FHAN滤波后的速度指令(RPS)
 * @return FHAN输出的平滑速度指令(rps)，用于PI控制器
 */
float GetFhanSpeed_RPS(void);

/**
 * @brief 获取FHAN输出的加速度(RPS/s)
 * @return FHAN输出的加速度，可用于前馈补偿
 */
float GetFhanAcceleration_RPS(void);

/**
 * @brief Z脉冲检测回调函数
 * @note 外部Z相IO中断中调用
 *      用于独立计数统计
 */
void ZPulseDetectedCallback(void);

/* Z相计数器接口函数 */
uint32_t GetZPulseCounter(void);
void ResetZPulseCounter(void);

/* 配置函数 */
void SetSpeedFilterType(FilterType_t type);
void SetLowPassFilterCoeff(float alpha);
void SetMovingAvgFilterSize(uint16_t size);

/* FHAN路径规划函数 */
void InitFhanFilter(float r0, float max_accel);
void SetFhanParams(float r0, float max_accel);
void EnableFhanFilter(uint8_t enable);
uint8_t IsFhanEnabled(void);
void ProcessFhan(float target_speed);

/* PI控制器函数 */
void InitSpeedPI(float kp, float ki, float max_integral, float max_output);
void SetSpeedPIParams(float kp, float ki, float max_integral, float max_output);
void EnableSpeedPI(uint8_t enable);
void EnablePIFeedforward(uint8_t enable);
void ResetSpeedPIIntegral(void);
uint8_t IsSpeedPIEnabled(void);

/**
 * @brief 获取速度PI控制器输出(电流指令A)
 * @param speed_target 目标速度(RPS) - 来自FHAN输出或直接给定
 * @return PI控制器输出的电流指令(A)
 */
float GetSpeedPIOutput(float speed_target);

/**
 * @brief 获取速度PI总输出(包含前馈)
 * @return 总电流指令(A) = PI输出 + 前馈补偿
 */
float GetSpeedPITotalOutput(void);


/* TMR3溢出处理函数 */
void HandleTimerOverflow(uint16_t current_count, uint16_t last_count, int32_t *overflow_count);
void ResetOverflowCounter(void);

/* 溢出检测阈值 */
#define OVERFLOW_DETECT_HIGH_THRESHOLD  12288  // (COUNTS_PER_MECH_REV * 3 / 4)
#define OVERFLOW_DETECT_LOW_THRESHOLD   4096   // (COUNTS_PER_MECH_REV / 4)

/* Z相简化配置 */
#define Z_PULSE_COUNTER_ENABLE          1       // 启用Z相计数功能

/* Z相优化配置 */
#define Z_POSITION_FILTER_SIZE         4        // Z相位置滑动滤波点数
#define Z_DEADBAND_THRESHOLD           50       // Z相死区阈值(±50计数)
#define Z_FILTER_INDEX_MASK            0x03     // 滤波索引掩码(4点循环)

/**
 * @brief 获取安全的电流指令（供电流环读取）
 * @return 电流指令(A)，保证数据一致性
 * @note 在20KHz电流环中调用，自动清除更新标志
 */
float GetCurrentCommandSafe(void);

/**
 * @brief 仅获取电流指令，不影响更新标志
 * @return 电流指令(A)，保证数据一致性
 * @note 用于需要保持更新标志状态的场合
 */
float GetCurrentCommandValue(void);

/**
 * @brief 检查电流指令是否有更新
 * @return 1=有新更新，0=无更新
 * @note 检查更新状态，不清除标志
 */
uint8_t IsCurrentCommandUpdated(void);

/**
 * @brief 清除电流指令更新标志
 * @note 手动清除更新标志，用于自定义同步策略
 */
void ClearCurrentCommandFlag(void);

/**
 * @brief 更新F4自定义数据用于调试观察
 * @param target_speed_rps 目标速度(RPS)，用于计算速度误差
 * @note 将速度环关键调试波形数据赋值给F4帧的custom字段，并发送数据帧
 *       包括：原始速度、滤波速度、FHAN输出、PI输出等关键信号
 */
void UpdateF4CustomDataWithTarget(float target_speed_rps);

/**
 * @brief 模拟ABZ编码器函数 - 用于无旋变测试
 * @param target_speed_rps 目标速度(RPS)
 * @note 在20K中断中调用，模拟真实编码器行为
 *       包含固定加速度5RPS/s和±2RPS震荡效果
 *       直接更新TIM3计数器值，替代真实编码器输入
 */
void SimulateABZEncoder(float target_speed_rps);

#endif /* __ENC_SPEED_H */
