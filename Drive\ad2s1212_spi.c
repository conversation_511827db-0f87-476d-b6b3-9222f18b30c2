#include "ad2s1212_spi.h"

/**
 * @brief 配置AD2S1210的工作模式
 * @param ModeConfig 模式配置参数
 *        - Mode_POSIT: 位置读取模式 (A0=0, A1=0)
 *        - Mode_SPEED: 速度读取模式 (A0=0, A1=1)
 *        - Mode_COFIG: 寄存器配置模式 (A0=1, A1=1)
 * @note 通过设置A0和A1引脚电平来配置工作模式，并执行采样操作
 */
void AD2S2S1210_ModeCfg(uint8_t ModeConfig)
{
  //A0,A1配置
  switch(ModeConfig)
  {
    case Mode_POSIT:
            A0_L;A1_L;    // 位置读取模式 (A0=0, A1=0)
            break;
    case Mode_SPEED:
            A0_L;A1_H;    // 速度读取模式 (A0=0, A1=1)
            break;
    case Mode_COFIG:
            A0_H;A1_H;    // 寄存器配置模式 (A0=1, A1=1)
            break;		
    default:
            A0_L;A1_L;    // 默认为配置模式
            break;	
  }
  AD2S1210_Delay(4);
  SMAPLE_H;
  AD2S1210_Delay(1);
  SMAPLE_L;
  AD2S1210_Delay(1);
}

/**
 * @brief 初始化AD2S1210旋变数字转换器
 * @note 配置分辨率为12位，设置各种阈值参数，并初始化为位置读取模式
 *       包括：控制寄存器、激励频率、DOS阈值、LOS阈值、LOT阈值等配置
 */
void AD2S1210_Init()
{
  uint8_t readValue = 0;
  //电阻配置
  RES0_L;RES1_H;  //配置为12位
  A0_H;A1_H;      //配置模式
  
  PCS_L;
  SMAPLE_L;
  NWR_H;
  AD2S1210_Delay(0x0F);
  
  // 根据寄存器映射表更新寄存器配置
  AD2S1210_WRITE(CONTROL, 0x7A);         // 控制寄存器配置，设置分辨率为12位
  readValue = AD2S1210_READ(CONTROL);    // 读取控制寄存器验证写入

  AD2S1210_WRITE(EXFREQUENCY, 0x28);     // 设置激励频率 40*250Hz = 10KHz
  readValue = AD2S1210_READ(EXFREQUENCY); // 读取激励频率寄存器验证写入

  AD2S1210_WRITE(DOSRSTMXTHRES, 0x7E);   // DOS复位最大阈值
  readValue = AD2S1210_READ(DOSRSTMXTHRES); // 读取验证写入

  AD2S1210_WRITE(DOSRSTMITHRES, 0x02);   // DOS复位最小阈值
  readValue = AD2S1210_READ(DOSRSTMITHRES); // 读取验证写入

  AD2S1210_WRITE(LOSTHRES, 0x01);        // LOS门限值
  readValue = AD2S1210_READ(LOSTHRES);   // 读取验证写入

  AD2S1210_WRITE(DOSORTHRES, 0x7F);      // DOS超量程阈值
  readValue = AD2S1210_READ(DOSORTHRES); // 读取验证写入

  AD2S1210_WRITE(DOSMISTHRES, 0x7F);     // DOS失配阈值
  readValue = AD2S1210_READ(DOSMISTHRES); // 读取验证写入

  AD2S1210_WRITE(LOTHITHRES, 0x7F);      // LOT上限
  readValue = AD2S1210_READ(LOTHITHRES); // 读取验证写入

  AD2S1210_WRITE(LOTLOTHRES, 0x01);      // LOT下限
  readValue = AD2S1210_READ(LOTLOTHRES); // 读取验证写入

  AD2S1210_Delay(0xFFF);
  
  SMAPLE_H;
  SMAPLE_L;
  AD2S1210_Delay(0x1FF);
  AD2S1210_REFAULT();                    // 复位错误标志
  AD2S1210_Delay(0xFF);
  AD2S2S1210_ModeCfg(Mode_POSIT);        // 切换到普通位置读取模式  
  PCS_H;
}
 
/**
 * @brief AD2S1210硬件复位函数
 * @note 通过SAMPLE信号的高低电平切换实现硬件复位
 *       也可以使用RESET引脚进行硬件复位
 */
void AD2S1210_RESET(void)
{
  SMAPLE_H;
  SMAPLE_L;
  AD2S_RESET_L;
  AD2S1210_Delay(0x5FF);
  AD2S_RESET_H;
  AD2S1210_Delay(0xFF);
}
 
/**
 * @brief AD2S1210错误标志复位函数
 * @note 通过读取故障寄存器清除故障标志
 *       执行时间约为3us
 *       切换到配置模式，执行SAMPLE信号采样，读取FAULT寄存器
 */
void AD2S1210_REFAULT()
{
  AD2S2S1210_ModeCfg(Mode_COFIG);        // 切换到配置模式
  SMAPLE_H;
  AD2S1210_Delay(0x1);
  SMAPLE_L;
  AD2S1210_Delay(0x1);
  AD2S1210_READ(FAULT);                  // 读取故障寄存器清除故障
  SMAPLE_H;
  AD2S1210_Delay(0x1);
  SMAPLE_L;
  AD2S1210_Delay(6);
}
 
/**
 * @brief 延时函数
 * @param nCount 延时计数值
 * @note nCount=1时执行时间为183ns，步长22ns
 *       通过简单循环实现延时
 */
void AD2S1210_Delay(uint32_t nCount)     // nCount=1时执行时间为183ns步长22ns
{ 
  for(; nCount != 0; nCount--); 
} 
 
/**
 * @brief 向AD2S1210寄存器写入数据
 * @param addr 寄存器地址
 * @param data 要写入的数据
 * @note 通过SPI接口向指定寄存器写入数据
 *       先发送寄存器地址，再发送数据
 */
void AD2S1210_WRITE(uint8_t addr, uint8_t data)
{
  uint8_t temp = addr;
  uint8_t rData = 0;
  PCS_L;
  NWR_L;
  NOP1;
  spi_i2s_data_transmit(SPI2, temp);     // 发送寄存器地址
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);
  NWR_H;
  NOP38;
  NWR_L;
  NOP1;
  temp = data;
  spi_i2s_data_transmit(SPI2, temp);     // 发送寄存器数据
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);
  NOP1;
  NWR_H;
  PCS_H;
  NOP38;
  
}
 
/**
 * @brief 读取AD2S1210寄存器数据
 * @param addr 要读取的寄存器地址
 * @return 读取到的寄存器数据
 * @note 先切换到配置模式，通过SPI接口读取指定寄存器的数据
 *       读取完成后切换回位置读取模式
 */
uint8_t AD2S1210_READ(uint8_t addr)
{
  AD2S2S1210_ModeCfg(Mode_COFIG);
  uint8_t temp = addr;
  uint8_t buff = 0;
  uint8_t rData = 0;
  PCS_L;
  NWR_L;
  AD2S1210_Delay(0x01);
  spi_i2s_data_transmit(SPI2, temp);     // 发送寄存器地址
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);
  NWR_H;
  AD2S1210_Delay(0x02);
  
  buff = 0xFF;                          // 发送空数据读取寄存器内容
  NWR_L;
  spi_i2s_data_transmit(SPI2, buff);
  while(spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET);
  rData = spi_i2s_data_receive(SPI2);   // 读取寄存器内容
  NWR_H;
  AD2S1210_Delay(0x2);
  PCS_H;

  return rData;
}
 
/**
 * @brief 连续读取AD2S1210位置
 * @return 读取到的12位数据结果
 * @note 与普通读取不同，此函数在读取MSB和LSB之间不拉高NWR信号
 *       实现连续读取两个字节，提高读取效率
 */
uint16_t AD2S1210_CommRead(void)
{
    uint16_t result = 0;
    uint8_t msb = 0, lsb = 0;
    uint32_t timeout = 0;

    SMAPLE_L;
    INSERT_NOP(4);  // T16_NOP值

    PCS_L;
    NWR_L;
    // T31_NOP值为0，不需要延时 (<5ns)
    INSERT_NOP(1);   // T23_NOP值

    // 连续读取两个字节，中间不拉高NWR
    SPI2->dt = 0xFF;
    timeout = 0;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG)) {
        timeout++;
        if (timeout >= 1000) {
            // 超时处理：复位控制线并返回错误值
            SMAPLE_H;
            NWR_H;
            PCS_H;
            return 0xFFFF; // 错误值
        }
    }
    msb = SPI2->dt;

    // 直接继续保持NWR_L
    // INSERT_NOP(1);  // 额外延迟

    SPI2->dt = 0xFF;
    timeout = 0;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG)) {
        timeout++;
        if (timeout >= 1000) {
            // 超时处理：复位控制线并返回错误值
            SMAPLE_H;
            NWR_H;
            PCS_H;
            return 0xFFFF; // 错误值
        }
    }
    lsb = SPI2->dt;

    SMAPLE_H;
    NWR_H;
    PCS_H;

    result = ((uint16_t)msb << 4) | (lsb >> 4);
    return result;
}



/**
 * @brief 读取AD2S1210角速度数据
 * @return 读取到的12位角速度数据(二进制补码格式)
 * @note 该函数会临时切换到速度读取模式，读取完成后恢复到位置读取模式
 *       速度数据格式为二进制补码，MSB代表旋转方向
 */
uint16_t AD2S1210_ReadVelocity(void)
{
    uint16_t result = 0;
    uint8_t msb = 0, lsb = 0;
    
    // 使用SAMPLE更新寄存器数据
    INSERT_NOP(1);  // 短暂延迟
    SMAPLE_L;
    INSERT_NOP(4); // T16_NOP值
    // 切换到速度读取模式 (A0=0, A1=1)
    A0_L;
    A1_H;
    // 片选
    PCS_L;
    INSERT_NOP(1);  // T6_NOP值
    
    // 读取MSB
    NWR_L;
    // T31_NOP值为0，不需要延时 (<5ns)
    INSERT_NOP(1);  // T23_NOP值
    
    SPI2->dt = 0xFF;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG));
    msb = SPI2->dt;
    
    SPI2->dt = 0xFF;
    while (!(SPI2->sts & SPI_I2S_RDBF_FLAG));
    lsb = SPI2->dt;
    
    NWR_H;
    SMAPLE_H;
    PCS_H;
    
    // 恢复到位置读取模式 (A0=0, A1=0)
    A0_L;
    A1_L;
    
    // 合成12位结果
    result = ((uint16_t)msb << 8) | lsb;
    return result;
}

/**
 * @brief 获取转子旋转速度，支持转/分(RPM)或转/秒(RPS)
 * @param unit 速度单位：0=转/秒(RPS), 1=转/分(RPM)
 * @return 转换后的旋转速度，带符号值(正值表示正向旋转，负值表示反向旋转)
 * @note 对于12位分辨率、8.192MHz时钟:
 *       - 最大跟踪速率: ±1000 rps
 *       - 有效数据位: D15至D4 (忽略D3至D0)
 *       - 当速度为+1000 rps时，原始值为0x7FF0
 *       - 当速度为-1000 rps时，原始值为0x8000
 */
float AD2S1210_GetRotationSpeed(uint8_t unit)
{
    int16_t raw_velocity;
    int16_t velocity_12bit;
    float rotation_speed;
    
    // 读取原始速度数据，转换为有符号整型保留方向信息
    raw_velocity = (int16_t)AD2S1210_ReadVelocity();
    
    // 右移4位忽略低4位无效数据，得到12位有效值
    velocity_12bit = raw_velocity >> 4;
    
    // 对于12位二进制补码:
    // - 最大正值为0x7FF (+2047)，表示+1000 rps
    // - 最小负值为0x800 (-2048)，表示-1000 rps
    const float LSB_VALUE = 1000.0f / 2047.0f; // 约0.488 rps
    
    // 计算旋转速度(rps)
    rotation_speed = (float)velocity_12bit * LSB_VALUE;
    
    // 如果需要转/分(RPM)，乘以60
    if(unit == 1) {
        rotation_speed *= 60.0f;
    }
    
    return rotation_speed;
}

/********************************* 拓展功能模块 **************************************/

// 拓展功能模块通用常量定义
#define COUNTS_PER_ELEC_REV         4096.0f
#define HER_HER_TWO_PI              6.283185307179586f
#define HER_HER_PI                  3.141592653589793f
#define COUNTS_TO_RAD_FACTOR        (HER_HER_TWO_PI / COUNTS_PER_ELEC_REV) // 预计算转换系数

/****************************** 位置初始化功能 ******************************/
/**
 * @brief 从SPI读取绝对位置并计算定时器初始值
 * @note  这是一个自包含的函数
 * @param resolver_pos_out [out] 指向uint16_t的指针，用于存储从SPI读取到的原始位置值。
 * @param timer_count_out [out] 指向uint16_t的指针，用于存储计算出的、需要写入定时器的计数值。
 * @return true: SPI通信和计算成功, false: SPI通信失败。
 */
void CalculateInitialPositionFromSPI(uint16_t* resolver_pos_out, uint16_t* timer_count_out)
{
    // 定义旋变编码器的最大计数值 (16位)
    const uint16_t RESOLVER_MAX_COUNT = 4096;
    const uint16_t align_position = 2393;  //磁链对齐零位置

    uint16_t current_resolver_pos;
    uint16_t calculated_count;

    /* 1. 从旋变读取绝对位置 */
    current_resolver_pos = AD2S1210_CommRead();

    // 检查SPI通信是否成功
    if (current_resolver_pos == 0xFFFF) {
        *resolver_pos_out = 0xFFFF;
        *timer_count_out = 0;
    }

    /* 2. 返回从SPI成功读取到的原始位置值 */
    *resolver_pos_out = current_resolver_pos;

    /* 3. 计算相对于零位的电角度偏移 */
    if (current_resolver_pos >= align_position) {
        calculated_count = current_resolver_pos - align_position;
    } else {
        calculated_count = current_resolver_pos + (RESOLVER_MAX_COUNT - align_position);
    }

    TMRx_CVAL = calculated_count;

    /* 4. 返回计算出的定时器计数值 */
    *timer_count_out = calculated_count;
}

/**
 * @brief 从SPI读取绝对位置并初始化硬件编码器定时器
 * @param timer_instance 指定的定时器实例 (如TMR3)
 * @param align_offset 角度偏移值，默认为0
 * @return true: 初始化成功, false: 初始化失败
 */
uint8_t AD2S1210_InitHardwareFromSPI(tmr_type* timer_instance, uint16_t align_offset)
{
    const uint16_t RESOLVER_MAX_COUNT = 4096;
    uint16_t resolver_pos;
    uint16_t calculated_count;

    /* 从旋变读取绝对位置 */
    resolver_pos = AD2S1210_CommRead();
    if (resolver_pos == 0xFFFF) {
        return 0;  // SPI读取失败
    }

    /* 计算相对于零位的电角度偏移 */
    if (resolver_pos >= align_offset) {
        calculated_count = resolver_pos - align_offset;
    } else {
        calculated_count = resolver_pos + (RESOLVER_MAX_COUNT - align_offset);
    }

    /* 同步指定定时器计数器 */
    timer_instance->cval = calculated_count;

    return 1;  // 初始化成功
}

/****************************** 角度偏移校准功能 ******************************/
/**
 * @brief  通过SPI读取绝对位置来校准和设定软件角度偏移量
 * @brief  此函数应在上电初始化序列中，电机使能之前调用一次。
 * @param  offset_rad_out:    用于返回计算出的弧度制偏移量。
 * @return bool_t:            如果成功读取并计算了偏移，则返回TRUE；否则返回FALSE。
 */
uint8_t Sensor_InitAndCalibrateOffset(float *offset_rad_out)
{
    // 输入参数检查
    if (offset_rad_out == NULL) {
        return FALSE;
    }

    // 静态常量定义
    // 传感器一圈的总计数值 (对于12位的AD2S1210是4096)
    const uint16_t SENSOR_MAX_COUNT = 4096; 
    // 通过手动磁链对齐，预先测得的，当电机在电气角度0度时，传感器输出的绝对位置计数值
    const uint16_t ELECTRICAL_ZERO_COUNT = 2393; 

    // 步骤 1: 清零定时器
    TMRx_CVAL = 0;

    // 步骤 2: 读取当前绝对角度
    uint16_t current_pos_counts = AD2S1210_CommRead();

    // 检查SPI通信是否失败
    if (current_pos_counts == 0xFFFF) {
        *offset_rad_out = 0.0f;
        return FALSE; 
    }

    // 步骤 3: 计算软件偏移量
    int32_t offset_counts = -(int32_t)ELECTRICAL_ZERO_COUNT;

    // 步骤 4: 将偏移量转化为弧度并传出
    float offset_rad = (float)offset_counts * (HER_HER_TWO_PI / (float)SENSOR_MAX_COUNT);
    
    // 返回计算结果
    *offset_rad_out = offset_rad;
    
    return TRUE;
}





/**
 * @brief 在电流环中断中更新并返回归一化的电角度
 * @details 使用预计算的转换系数
 * @return -π 到 +π 之间的浮点数电角度 (单位：弧度)
 */
float PositionSensorENC_Update_20KHz_Optimized(void)
{
    /* 1. 读取定时器计数值 */
    uint16_t elec_count = TMRx_CVAL & 4095;
    
    /* 2. 将计数值直接转换为 [0, 2π] 范围的弧度 */
    float angle_rad = (float)elec_count * COUNTS_TO_RAD_FACTOR;
    
    /* 3. 范围归一化到 [-π, +π] 范围 */
	while (angle_rad < -HER_HER_PI) { angle_rad += HER_HER_TWO_PI; }
	while (angle_rad >=  HER_HER_PI) { angle_rad -= HER_HER_TWO_PI; }
    
    return angle_rad;
}

/****************************** 自检功能 ******************************/
/**
 * @brief AD2S1210自检功能 - 读取LOT和DOS状态
 * @param lot_status [out] LOT引脚状态，0=故障(低电平)，1=正常(高电平)
 * @param dos_status [out] DOS引脚状态，0=故障(低电平)，1=正常(高电平)
 * @note LOT和DOS为低电平时表示故障触发
 */
void AD2S1210_ReadStatusPins(uint8_t* lot_status, uint8_t* dos_status)
{
    if (lot_status != NULL) {
        *lot_status = LOT;  // 读取LOT引脚状态
    }

    if (dos_status != NULL) {
        *dos_status = DOS;  // 读取DOS引脚状态
    }
}

/**
 * @brief AD2S1210自检功能 - 读取故障寄存器
 * @return 故障寄存器值(8位)，各位含义：
 *         D7: 正弦/余弦输入削波
 *         D6: 正弦/余弦输入低于LOS阈值
 *         D5: 正弦/余弦输入超过DOS超量程阈值
 *         D4: 正弦/余弦输入超过DOS失配阈值
 *         D3: 跟踪误差超过LOT阈值
 *         D2: 速度超过最大跟踪速率
 *         D1: 相位误差超过锁相范围
 *         D0: 配置奇偶校验错误
 * @note 故障寄存器位为高有效，即故障位为高时表示故障已发生
 */
uint8_t AD2S1210_ReadFaultRegister(void)
{
    uint8_t fault_value;

    // 切换到配置模式读取故障寄存器
    AD2S2S1210_ModeCfg(Mode_COFIG);

    // 读取故障寄存器
    fault_value = AD2S1210_READ(FAULT);

    // 切换回位置读取模式
    AD2S2S1210_ModeCfg(Mode_POSIT);

    return fault_value;
}

/**
 * @brief AD2S1210自检功能 - 清除故障寄存器
 * @note 通过读取故障寄存器来清除故障标志
 */
void AD2S1210_ClearFaultRegister(void)
{
    // 使用现有的故障复位函数
    AD2S1210_REFAULT();
}

/**
 * @brief AD2S1210自检功能 - 分析故障寄存器
 * @param fault_reg 故障寄存器值
 * @return 检测到的故障数量
 */
uint8_t AD2S1210_AnalyzeFaultRegister(uint8_t fault_reg)
{
    uint8_t fault_count = 0;

    // 统计故障位数量
    if (fault_reg & 0x80) fault_count++;  // D7: 正弦/余弦输入削波
    if (fault_reg & 0x40) fault_count++;  // D6: 正弦/余弦输入低于LOS阈值
    if (fault_reg & 0x20) fault_count++;  // D5: 正弦/余弦输入超过DOS超量程阈值
    if (fault_reg & 0x10) fault_count++;  // D4: 正弦/余弦输入超过DOS失配阈值
    if (fault_reg & 0x08) fault_count++;  // D3: 跟踪误差超过LOT阈值
    if (fault_reg & 0x04) fault_count++;  // D2: 速度超过最大跟踪速率
    if (fault_reg & 0x02) fault_count++;  // D1: 相位误差超过锁相范围
    if (fault_reg & 0x01) fault_count++;  // D0: 配置奇偶校验错误

    return fault_count;
}

/**
 * @brief AD2S1210完整自检API
 * @param self_test_result [out] 自检结果结构体指针
 * @return 自检状态：0=正常，1=有故障
 * @note 执行完整的自检流程：
 *       1. 读取LOT、DOS引脚状态
 *       2. 读取并分析故障寄存器
 *       3. 清除故障寄存器
 */
uint8_t AD2S1210_SelfTest(AD2S1210_SelfTestResult_t* self_test_result)
{
    uint8_t has_fault = 0;

    if (self_test_result == NULL) {
        return 1;  // 参数错误
    }

    // 初始化结果结构体
    self_test_result->lot_status = 0;
    self_test_result->dos_status = 0;
    self_test_result->fault_register = 0;
    self_test_result->fault_count = 0;

    // 步骤1: 读取LOT和DOS引脚状态
    AD2S1210_ReadStatusPins(&self_test_result->lot_status, &self_test_result->dos_status);

    // 检查引脚状态（低电平表示故障）
    if (self_test_result->lot_status == 0) {
        has_fault = 1;
    }
    if (self_test_result->dos_status == 0) {
        has_fault = 1;
    }

    // 步骤2: 读取故障寄存器
    self_test_result->fault_register = AD2S1210_ReadFaultRegister();

    // 步骤3: 分析故障寄存器（统计故障数量）
    self_test_result->fault_count = AD2S1210_AnalyzeFaultRegister(self_test_result->fault_register);

    // 如果故障寄存器有任何位被设置，表示有故障
    if (self_test_result->fault_register != 0) {
        has_fault = 1;
    }

    // 步骤4: 清除故障寄存器
    AD2S1210_ClearFaultRegister();

    return has_fault;
}