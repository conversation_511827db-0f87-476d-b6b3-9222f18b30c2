#include "HWInterface.h"
#include "AnoPTv8Run.h"
#include "AnoPTv8Par.h"
#include "MotorParams.h"
#include "MotorCmd.h"
#include "usb_app.h"
#include "Sys_TimerEvent.h"  // 添加此头文件以使用 Get_Runtime_Ms
#include <stdarg.h>
#include <stdio.h>

/**
 * @brief  协议硬件初始化函数
 * @param  None
 * @retval None
 * @note   初始化电机参数和命令
 */
void AnoPTv8HwInit(void)
{
    MotorParamsInit();
    MotorCmdInit();
}

/**
 * @brief  协议帧发送函数
 * @param  frame: 要发送的帧索引
 * @retval None
 * @note   调用SendFrame函数完成实际发送
 */
void AnoPTv8HwSendFrame(uint8_t frame)
{
    
}

/**
 * @brief  协议数据测试函数
 * @param  None
 * @retval None
 * @note   用于测试协议发送功能
 */
void AnoPTv8HwDataTest(void)
{
    static uint32_t test_count = 0;
    static uint32_t last_test_time = 0;

    // 每5秒发送一次测试数据
    if(Get_Runtime_Ms() - last_test_time > 5000) {
        last_test_time = Get_Runtime_Ms();
        test_count++;

        // 发送测试字符串
        char test_str[50];
        sprintf(test_str, "Test Message #%u", (unsigned int)test_count);
        AnoPTv8SendStr(ANOPTV8DEVID_SWJ, LOG_COLOR_GREEN, test_str);

        // 发送设备信息（模拟上位机请求）
        if(test_count % 3 == 0) {
            AnoPTv8SendDevInfo(ANOPTV8DEVID_SWJ);
        }
    }
}

/**
 * @brief  协议字节发送函数
 * @param  buf: 要发送的数据缓冲区指针
 * @param  len: 要发送的数据长度
 * @retval None
 * @note   通过USB接口发送数据
 */
void AnoPTv8HwSendBytes(uint8_t *buf, uint16_t len)
{
    if(buf == NULL || len == 0) {
        return;
    }
    error_status result = usb_send_data(buf, len);
}

/**
 * @brief  AnoPTv8协议字节接收函数
 * @param  dat: 接收到的单字节数据
 * @retval None
 * @note   此函数已在hardwareInterface.h中声明，用户需要在对应串口的接收事件中调用此函数
 *         如果接收事件接收到的数据大于1字节，需多次调用此函数
 */
void AnoPTv8HwRecvByte(uint8_t dat)
{
    AnoPTv8RecvOneByte(dat);
}

/**
 * @brief  AnoPTv8协议批量字节接收函数
 * @param  buf: 接收到的数据缓冲区指针
 * @param  len: 接收到的数据长度
 * @retval None
 * @note   批量处理接收到的数据，比逐字节处理更高效
 */
void AnoPTv8HwRecvBytes(uint8_t *buf, uint16_t len)
{
    AnoPTv8RecvBytes(buf, len);
}

/**
 * @brief  协议定时触发函数
 * @param  None
 * @retval None
 * @note   每调用一次进行一次缓冲检查并发送，按频率需求
 *         如果数据较多调用频率太慢会导致堵塞
 */
void AnoPTv8HwTrigger1ms(void)
{
    // 发送大缓冲区数据
    AnoPTv8TxLargeBufSend();
}   

/**
 * @brief  协议参数值接收回调函数
 * @param  parid: 参数ID
 * @param  parval: 参数值
 * @retval None
 * @note   当接收到参数值时被调用
 */
void AnoPTv8HwParValRecvCallback(uint16_t parid, int32_t parval)
{
    // 参数值接收回调
}

/**
 * @brief  协议参数命令接收回调函数
 * @param  id: 命令ID
 * @param  val: 命令值
 * @retval None
 * @note   当接收到参数命令时被调用
 */
void AnoPTv8HwParCmdRecvCallback(uint8_t id, uint16_t val)
{
    // 参数命令接收回调
}

/**
 * @brief  协议参数复位命令回调函数
 * @param  None
 * @retval None
 * @note   当接收到参数复位命令时被调用
 */
void AnoPTv8HwParCmdResetParameter(void)
{
    // 参数复位命令回调
}

/**
 * @brief 发送格式化字符串到上位机V2
 * @details 类似printf的功能，内部调用AnoPTv8SendStr完成发送到上位机
 * @param[in] string_color 字符串颜色
 * @param[in] format 格式化字符串
 * @param[in] ... 可变参数列表
 */
void Usb_printf(uint8_t string_color, const char *format, ...)
{
    char str_buf[100];
    va_list args;
    
    if (format == NULL) return;
    
    // 初始化可变参数列表,将args指向第一个可变参数
    va_start(args, format);
    
    // 使用vsnprintf将格式化字符串及参数写入缓冲区
    // len返回实际写入的字符数(不包含结尾的\0)
    int len = vsnprintf(str_buf, sizeof(str_buf), format, args);
    
    // 清理可变参数列表
    va_end(args);
    
    // 处理格式化错误或缓冲区溢出的情况
    // len < 0 表示格式化错误
    // len >= sizeof(str_buf) 表示缓冲区溢出
    if (len < 0 || len >= sizeof(str_buf)) {
        // 确保字符串以\0结尾
        str_buf[sizeof(str_buf) - 1] = '\0';
    }
    
    // 如果格式化后的字符串为空,则直接返回
    if (str_buf[0] == '\0') return;
    
    // 调用AnoPTv8SendStr发送格式化后的字符串到上位机
    // ANOPTV8DEVID_SWJ为目标设备地址
    // string_color为字符串颜色
    AnoPTv8SendStr(ANOPTV8DEVID_SWJ, string_color, str_buf);
}

/**
 * @brief  协议帧校验计算函数
 * @param[in] p 指向帧结构体的指针
 * @retval None
 * @note   计算帧的校验和01，这个是专门给外部接口调用的，内部还有一个AnoPTv8CalFrameCheck
 */
void AnoPTv8CalFrameCheck01(_un_frame_v8 *p)
{
    uint8_t sumcheck = 0, addcheck = 0;
    for (uint16_t i = 0; i < (ANOPTV8_FRAME_HEADLEN + p->frame.datalen); i++)
    {
        sumcheck += p->rawBytes[i];
        addcheck += sumcheck;
    }
    p->rawBytes[ANOPTV8_FRAME_HEADLEN + p->frame.datalen] = sumcheck;
    p->rawBytes[ANOPTV8_FRAME_HEADLEN + p->frame.datalen + 1] = addcheck;
    p->frame.sumcheck = sumcheck;
    p->frame.addcheck = addcheck;
}

/**
 * @brief 直接通过USB发送格式化字符串，绕过定时发送系统V2.1版本，使用memcpy优化
 * @details 用于实时调试输出，实现在程序阻塞时也能输出信息，最多99字节
 * @param[in] string_color 字符串颜色
 * @param[in] format 格式化字符串
 * @param[in] ... 可变参数列表
 */
void Usb_printf_direct(uint8_t string_color, const char *format, ...)
{
    char str_buf[100];
    va_list args;
    _un_frame_v8 directFrame; // 使用相同的帧结构
    uint8_t _cnt = 0;
    
    if (format == NULL) return;
    
    // 初始化可变参数列表,将args指向第一个可变参数
    va_start(args, format);
    
    // 使用vsnprintf将格式化字符串及参数写入缓冲区
    int len = vsnprintf(str_buf, sizeof(str_buf), format, args);
    
    // 清理可变参数列表
    va_end(args);
    
    // 处理格式化错误或缓冲区溢出的情况
    if (len < 0 || len >= sizeof(str_buf)) {
        str_buf[sizeof(str_buf) - 1] = '\0';
        len = sizeof(str_buf) - 1;
    }
    
    // 如果格式化后的字符串为空,则直接返回
    if (str_buf[0] == '\0') return;
    
    // 使用与Usb_printf相同的方式构建帧
    directFrame.frame.head = ANOPTV8_FRAME_HEAD;
    directFrame.frame.sdevid = ANOPTV8_MYDEVID;
    directFrame.frame.ddevid = ANOPTV8DEVID_SWJ;
    directFrame.frame.frameid = 0xA0;
    
    // 字符串颜色
    directFrame.frame.data[_cnt++] = string_color;
    
    // 字符串内容 - memcpy优化
    if (len > 99) len = 99; // 限制最大长度
    memcpy(&directFrame.frame.data[_cnt], str_buf, len);
    _cnt += len;
    
    // 设置数据长度
    directFrame.frame.datalen = _cnt;
    
    // 使用AnoPTv8CalFrameCheck01计算校验和
    AnoPTv8CalFrameCheck01(&directFrame);
    
    // 计算总帧长度
    uint16_t frameLen = directFrame.frame.datalen + ANOPTV8_FRAME_HEADLEN + 2;
    
    // 直接通过USB发送
    //usb_send_data(directFrame.rawBytes, frameLen);
}

