#include "low_speed_alarm.h"
#include "Sys_TimerEvent.h"
#include "sensor_proc.h"
#include "protection_monitor.h"
#include "system_status.h"
#include <string.h>

// 模块内部数据
static low_speed_alarm_data_t g_alarm_data = {0};

// 内部函数声明
static void update_system_state(void);
static void check_temperature_alarms(void);
static void check_low_speed_alarm(void);
static void update_alarm_outputs(void);
static uint8_t check_270v_status(void);
static fault_reason_t detect_fault_reason(void);

/**
 * @brief 低速告警模块初始化
 */
void Low_Speed_Alarm_Init(void)
{
    // 初始化数据结构
    memset(&g_alarm_data, 0, sizeof(low_speed_alarm_data_t));

    // 设置初始状态为正常(高电平)
    SET_TEMP_NoFail;
    SET_LowS_NoFail;

    // 初始化状态
    g_alarm_data.current_state = SYSTEM_STATE_POWER_OFF;
    g_alarm_data.state_enter_time = Get_Runtime_Ms();
}

/**
 * @brief 低速告警主任务(100ms调用一次)
 */
void Low_Speed_Alarm_Task(void)
{
    // 更新系统状态
    update_system_state();
    
    // 检查温度告警
    check_temperature_alarms();
    
    // 检查低速告警
    check_low_speed_alarm();
    
    // 更新告警输出
    update_alarm_outputs();
}

/**
 * @brief 重置告警模块
 */
void Low_Speed_Alarm_Reset(void)
{
    g_alarm_data.active_alarms = ALARM_TYPE_NONE;
    g_alarm_data.fault_reason = FAULT_REASON_NONE;
    g_alarm_data.state_enter_time = Get_Runtime_Ms();

    // 恢复正常输出(高电平=正常)
    SET_TEMP_NoFail;
    SET_LowS_NoFail;
}

/**
 * @brief 更新系统状态
 */
static void update_system_state(void)
{
    uint32_t current_time = Get_Runtime_Ms();
    system_state_t new_state = g_alarm_data.current_state;
    uint8_t voltage_270v_ok = check_270v_status();
    
    switch (g_alarm_data.current_state) {
        case SYSTEM_STATE_POWER_OFF:
            if (voltage_270v_ok) {
                new_state = SYSTEM_STATE_STARTUP;
                g_alarm_data.voltage_recovery_time = current_time;
            } else {
                // 270V断电超过30秒后进入告警状态
                if (current_time - g_alarm_data.state_enter_time > ALARM_DELAY_TIME_MS) {
                    new_state = SYSTEM_STATE_ALARM;
                    g_alarm_data.fault_reason = FAULT_REASON_270V_LOST;
                }
            }
            break;
            
        case SYSTEM_STATE_STARTUP:
            if (!voltage_270v_ok) {
                new_state = SYSTEM_STATE_POWER_OFF;
            } else if (current_time - g_alarm_data.voltage_recovery_time > STARTUP_RECOVERY_TIME_MS) {
                if (Low_Speed_Alarm_IsMotorRunning() && 
                    Low_Speed_Alarm_GetMotorSpeed() >= LOW_SPEED_THRESHOLD_RPM) {
                    new_state = SYSTEM_STATE_NORMAL_RUN;
                } else {
                    new_state = SYSTEM_STATE_FAULT_DELAY;
                    g_alarm_data.fault_start_time = current_time;
                    g_alarm_data.fault_reason = detect_fault_reason();
                }
            }
            break;
            
        case SYSTEM_STATE_NORMAL_RUN:
            if (!voltage_270v_ok) {
                new_state = SYSTEM_STATE_FAULT_DELAY;
                g_alarm_data.fault_start_time = current_time;
                g_alarm_data.fault_reason = FAULT_REASON_270V_LOST;
            } else if (Low_Speed_Alarm_GetMotorSpeed() < LOW_SPEED_THRESHOLD_RPM ||
                       !Low_Speed_Alarm_IsMotorRunning() ||
                       Low_Speed_Alarm_HasProtectionFault()) {
                new_state = SYSTEM_STATE_FAULT_DELAY;
                g_alarm_data.fault_start_time = current_time;
                g_alarm_data.fault_reason = detect_fault_reason();
            }
            break;
            
        case SYSTEM_STATE_FAULT_DELAY:
            if (voltage_270v_ok && 
                Low_Speed_Alarm_IsMotorRunning() && 
                Low_Speed_Alarm_GetMotorSpeed() >= LOW_SPEED_THRESHOLD_RPM &&
                !Low_Speed_Alarm_HasProtectionFault()) {
                new_state = SYSTEM_STATE_NORMAL_RUN;
            } else if (current_time - g_alarm_data.fault_start_time > ALARM_DELAY_TIME_MS) {
                new_state = SYSTEM_STATE_ALARM;
            }
            break;
            
        case SYSTEM_STATE_ALARM:
            if (voltage_270v_ok) {
                new_state = SYSTEM_STATE_STARTUP;
                g_alarm_data.voltage_recovery_time = current_time;
            }
            break;
    }
    
    // 状态切换处理
    if (new_state != g_alarm_data.current_state) {
        g_alarm_data.previous_state = g_alarm_data.current_state;
        g_alarm_data.current_state = new_state;
        g_alarm_data.state_enter_time = current_time;
        
        // 状态切换时的特殊处理
        if (new_state == SYSTEM_STATE_STARTUP || new_state == SYSTEM_STATE_NORMAL_RUN) {
            g_alarm_data.active_alarms &= ~ALARM_TYPE_LOW_SPEED; // 清除低速告警
        }
    }
    
    g_alarm_data.voltage_270v_ok = voltage_270v_ok;
    g_alarm_data.is_startup_process = (g_alarm_data.current_state == SYSTEM_STATE_STARTUP);
}

/**
 * @brief 检查温度告警
 */
static void check_temperature_alarms(void)
{
    float motor_temp = Low_Speed_Alarm_GetMotorTemp();
    float board_temp = Low_Speed_Alarm_GetBoardTemp();
    
    // 电机温度告警
    if (motor_temp > MOTOR_TEMP_THRESHOLD) {
        if (!(g_alarm_data.active_alarms & ALARM_TYPE_MOTOR_TEMP)) {
            g_alarm_data.active_alarms |= ALARM_TYPE_MOTOR_TEMP;
            g_alarm_data.temp_alarm_count++;
        }
    } else {
        g_alarm_data.active_alarms &= ~ALARM_TYPE_MOTOR_TEMP;
    }
    
    // 板上温度告警
    if (board_temp > BOARD_TEMP_THRESHOLD) {
        if (!(g_alarm_data.active_alarms & ALARM_TYPE_BOARD_TEMP)) {
            g_alarm_data.active_alarms |= ALARM_TYPE_BOARD_TEMP;
            g_alarm_data.temp_alarm_count++;
        }
    } else {
        g_alarm_data.active_alarms &= ~ALARM_TYPE_BOARD_TEMP;
    }
}

/**
 * @brief 检查低速告警
 */
static void check_low_speed_alarm(void)
{
    // 在告警状态下激活低速告警
    if (g_alarm_data.current_state == SYSTEM_STATE_ALARM) {
        if (!(g_alarm_data.active_alarms & ALARM_TYPE_LOW_SPEED)) {
            g_alarm_data.active_alarms |= ALARM_TYPE_LOW_SPEED;
            g_alarm_data.low_speed_alarm_count++;
        }
    }
}

/**
 * @brief 更新告警输出
 */
static void update_alarm_outputs(void)
{
    // 温度告警输出(任一温度超限则告警)
    // 高电平=正常，低电平=告警
    if (g_alarm_data.active_alarms & (ALARM_TYPE_MOTOR_TEMP | ALARM_TYPE_BOARD_TEMP)) {
        SET_TEMP_IsFail;  // 低电平=告警
    } else {
        SET_TEMP_NoFail;    // 高电平=正常
    }

    // 低速告警输出
    // 高电平=正常，低电平=告警
    if (g_alarm_data.active_alarms & ALARM_TYPE_LOW_SPEED) {
        SET_LowS_IsFail;  // 低电平=告警
    } else {
        SET_LowS_NoFail;    // 高电平=正常
    }
}

/**
 * @brief 检查270V电压状态
 */
static uint8_t check_270v_status(void)
{
    float voltage_270v = Low_Speed_Alarm_Get270VVoltage();
    return (voltage_270v >= VOLTAGE_270V_MIN) ? 1 : 0;
}

/**
 * @brief 检测故障原因
 */
static fault_reason_t detect_fault_reason(void)
{
    if (!g_alarm_data.voltage_270v_ok) {
        return FAULT_REASON_270V_LOST;
    }
    
    if (Low_Speed_Alarm_HasProtectionFault()) {
        // 这里可以进一步细分故障类型
        return FAULT_REASON_OVERCURRENT; // 简化处理
    }
    
    if (!Low_Speed_Alarm_IsMotorRunning()) {
        return FAULT_REASON_STALL;
    }
    
    if (Low_Speed_Alarm_GetMotorSpeed() < LOW_SPEED_THRESHOLD_RPM) {
        return FAULT_REASON_LOW_SPEED;
    }
    
    return FAULT_REASON_NONE;
}

// 状态查询函数实现
uint8_t Low_Speed_Alarm_IsActive(alarm_type_t alarm_type)
{
    return (g_alarm_data.active_alarms & alarm_type) ? 1 : 0;
}

system_state_t Low_Speed_Alarm_GetSystemState(void)
{
    return g_alarm_data.current_state;
}

fault_reason_t Low_Speed_Alarm_GetFaultReason(void)
{
    return g_alarm_data.fault_reason;
}

// 外部接口函数实现(需要根据实际系统调整)
float Low_Speed_Alarm_GetMotorSpeed(void)
{
    // TODO: 从电机控制模块获取转速

    return 0.0f; // 占位符
}

float Low_Speed_Alarm_GetMotorTemp(void)
{
    float temp;
    if (sensor_get_value(SENSOR_M_PT0, &temp) == SYS_STATUS_OK) {
        return temp;
    }
    return 25.0f; // 默认室温
}

float Low_Speed_Alarm_GetBoardTemp(void)
{
    float temp;
    if (sensor_get_value(SENSOR_B_PT0, &temp) == SYS_STATUS_OK) {
        return temp;
    }
    return 25.0f; // 默认室温
}

float Low_Speed_Alarm_Get270VVoltage(void)
{
    float voltage;
    if (sensor_get_value(SENSOR_U_DC, &voltage) == SYS_STATUS_OK) {
        return voltage;
    }
    return 0.0f; // 默认值
}

uint8_t Low_Speed_Alarm_IsMotorRunning(void)
{
    // TODO: 从电机控制模块获取运行状态
    // 可以通过电流、转速等参数判断
    float speed = Low_Speed_Alarm_GetMotorSpeed();
    return (speed > 100.0f) ? 1 : 0; // 简单判断，转速>100rpm认为在运行
}

uint8_t Low_Speed_Alarm_HasProtectionFault(void)
{
    // 从protection_monitor模块获取保护故障状态
    // 检查是否有任何保护参数处于触发状态
    for (int i = 0; i < PROTECT_PARAM_COUNT; i++) {
        if (Protection_Monitor_GetStatus((protect_param_id_t)i) == PROTECT_STATUS_TRIGGERED) {
            return 1;
        }
    }
    return 0;
}
