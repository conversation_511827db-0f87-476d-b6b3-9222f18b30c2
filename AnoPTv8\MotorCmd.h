#ifndef __MOTOR_CMD_H
#define __MOTOR_CMD_H

#include "AnoPTv8Cmd.h"
#include "AnoPTv8Par.h"

// 帧发送使能标志位定义
typedef union {
    uint8_t all;      // 所有标志位
    struct {
        uint8_t f1_en : 1;    // F1帧发送使能位 (bit0)
        uint8_t f2_en : 1;    // F2帧发送使能位 (bit1)
        uint8_t f3_en : 1;    // F3帧发送使能位 (bit2)
        uint8_t f4_en : 1;    // F4帧发送使能位 (bit3)
        uint8_t f5_en : 1;    // F5帧发送使能位 (bit4)
        uint8_t f6_en : 1;    // F6帧发送使能位 (bit5)
        uint8_t reserved : 2;  // 保留位 (bit6-7)
    } bits;
} frame_enable_flags_t;

extern frame_enable_flags_t gFrameFlags;

// 命令函数声明
void MotorCmdInit(void);
void SetFrameSendEnable(uint8_t frame_id, uint8_t enable);
uint8_t GetFrameSendEnable(void);

// 命令ID定义
#define CMD_MOTOR_START      0x01    // 电机启动命令
#define CMD_MOTOR_STOP       0x02    // 电机正常停止命令
#define CMD_MOTOR_RESET      0x03    // 电机复位命令
#define CMD_MOTOR_SELFTEST   0x04    // 电机自检命令
#define CMD_MOTOR_GET_FAULT  0x05    // 获取电机故障状态命令
#define CMD_MOTOR_CLR_FAULT  0x06    // 清除电机故障状态命令
#define CMD_WAVE_CTRL        0x07    // 波形控制命令(F1/F2/F3/F4帧控制)
#define CMD_WAVE_STOP_ALL    0x08    // 停止所有波形发送命令

#endif 