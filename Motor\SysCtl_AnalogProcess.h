/*
// Version: V1.0 文件创建
//
// Date: 	Dec 18, 2017
//
// Author:	chenchangjiang @YD
//===================================================================
//
// 文件名:	SysCtl_AnalogProcess.h
//
// 用途:    模拟量控制结构体

//===================================================================
*/
#ifndef SYSCTRL_ANALOGPROCESS_H
#define SYSCTRL_ANALOGPROCESS_H
#include "at32a403a.h"
//============= 模拟输入控制结构体 ================================
typedef struct {
	            //输入
	      		int    iSysADResult[16];
                //参数
				float fAnaInTs;        //模拟采样周期
				float fLVoltAB;
				float fLVoltBC;
				//输出
				float fFreGivenAna;    //模拟给定频率
				float fADResult_Offset[16];//系统模拟量（输入输出电流、电压）零漂前采样值
				float fADResult[16];		  //采样实际值
				float fADValue[9];
        int uADResultAnaIn[16];  //片上AD结果寄存器
				//计算函数
				void (*pfnAISample)();//模拟采样结果函数
              //  void (*pfnInit)();//初始化函数

                }TypeAnalogInput;

//============= 模拟输入默认变量初始化 =============================
#define AnalogInput_DEFAULTS {{0},0.0,0.0,0.0,0.0,{0},{0},{0},{0}, \
							  (void (*)(uint32_t))fnAISample\
                              }
//============= 模拟输入处理函数声明 ==============================
void fnAISample(TypeAnalogInput *p);

typedef struct {
				//函数声明
				void (*pfnSysOffsetInit)();//零漂参数初始化函数
				void (*pfnSysOffsetParameterCal)();//零漂参数计算函数
                //状态量
	            uint16_t  uSampleOffsetEn;//零漂使能
	            uint16_t  SampOffsetOnce;//读取零漂结果单次触发
				uint16_t  uOffsetCount;//零漂计数
                uint16_t  uOffsetCounter;//零漂计数
                float fOffsetValue[16];//DSP运算零漂值
				float fOffsetAdd[16];
 
				float fK[16];
				float fB[16];
				
               }TypeSysOffsetParameter;

#define SysOffsetParameter_DEFAULTS { (void (*)(uint32_t))fnSysOffsetInit,(void (*)(uint32_t))fnSysOffsetParameterCal}

//=================采样整定参数=========================================
typedef struct {
                float fSysSamValue[12];
				float fSysSamOutCRatio;
				float fSysSamOutCSamR;
				float fSysSamOutVDividR;
				float fSysSamOutVDCR;
				float fSysSamDCVoltage;
				float fSysSamOutVoltage;
				float fSysSamOutCurrent;
				float fSysSamNTC1;
				float fSysSamNTC2;
                } TypeSysSamScaParameter;
//================采样整定参数默认变量初始化===============================
#define SysSamScaParameter_DEFAULTS {{1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0,1.0},0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0}

//=================DSP采样数据传递至ARM=========================================
typedef struct {
				float fSamDSPtoARMIu1;
				float fSamDSPtoARMIv1;
				float fSamDSPtoARMIw1;

                float fSamDSPtoARMIu2;
                float fSamDSPtoARMIv2;
                float fSamDSPtoARMIw2;

				float fSamDSPtoARMUdc;
				float fSamDSPtoARMIdc;
				} TypeSamDSPtoARM;
//================采样数据变量传递至ARM===============================
#define TypeSamDSPtoARM_DEFAULTS {0.0,0.0,0.0,\
				                  0.0,0.0,0.0,\
				                  0.0,0.0}
//=================DA数据选择=========================================
typedef struct {
				uint16_t uDA_NumCh1;//通道1编号
				uint16_t uDA_NumCh2;//通道2编号

				float fDA_DataCh1;//通道1数据
				float fDA_DataCh2;//通道2数据
				} TypeDAChoose;
//================DA数据传递至ARM===============================
#define TypeDAChoose_DEFAULTS {0,0,0.0,0.0}



//============ 全局变量与全局函数声明 =============================

extern TypeAnalogInput AnalogInput;

extern TypeSysOffsetParameter SysSampOffset;

extern TypeSysSamScaParameter SysSamScaParameter;

extern TypeSamDSPtoARM  SysSamDSPtoARM;

extern TypeDAChoose DAOut;

void fnSysOffsetInit(TypeSysOffsetParameter *p);

void fnSysOffsetParameterCal(TypeSysOffsetParameter *p);

extern void fnParaUpdateSysSamScaParameter(void); 

//extern void fnWhileAISample(void);

#endif
//========================================================================
// No more.
//========================================================================


