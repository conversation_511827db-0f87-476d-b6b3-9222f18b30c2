Removed Pin From Net: NetName=XTALOUT Pin=U5-8
Change Component Comment : Designator=R94 Old Comment=1K/0.1% New Comment=5.1K/0.1%
Change Component Comment : Designator=R92 Old Comment=2K/0.1% New Comment=6.2K/0.1%
Change Component Comment : Designator=R95 Old Comment=3.9K/0.1% New Comment=1.3K/0.1%
Change Component Comment : Designator=R100 Old Comment=3K/0.1% New Comment=1.3K/0.1%
Change Component Comment : Designator=R109 Old Comment=3K/0.1% New Comment=1.3K/0.1%
Change Component Comment : Designator=R104 Old Comment=5K/0.1% New Comment=2K/0.1%
Change Component Comment : Designator=R111 Old Comment=5K/0.1% New Comment=2K/0.1%
Change Component Comment : Designator=R101 Old Comment=6.8K/0.1% New Comment=1.8K/0.1%
Change Component Comment : Designator=R108 Old Comment=6.8K/0.1% New Comment=1.8K/0.1%
Change Component Comment : Designator=R93 Old Comment=6.8K/0.1% New Comment=2K/0.1%
Change Component Comment : Designator=R99 Old Comment=7.8K/0.1% New Comment=1.5K/0.1%
Change Component Comment : Designator=R107 Old Comment=7.8K/0.1% New Comment=1.5K/0.1%
Change Component Comment : Designator=R36 Old Comment=10K New Comment=15K
Change Component Comment : Designator=C27 Old Comment=12pF/50V New Comment=22pF/50V
Change Component Comment : Designator=C29 Old Comment=12pF/50V New Comment=22pF/50V
Change Component Designator: OldDesignator=J1 NewDesignator=J8
Change component parameters: Designator = "C27" ("C27"); Footprint = "C0603"; UniqueID = "\ICFASBGK" ("\ICFASBGK")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQECA0010"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "CC0603JRNPO9BN200"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "22pF/50V"; VariantName = "[No Variations]"
Change component parameters: Designator = "C29" ("C29"); Footprint = "C0603"; UniqueID = "\JYVZNTQI" ("\JYVZNTQI")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQECA0010"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "CC0603JRNPO9BN200"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "22pF/50V"; VariantName = "[No Variations]"
Change component parameters: Designator = "R36" ("R36"); Footprint = "R0603"; UniqueID = "\SDOVRPFP" ("\SDOVRPFP")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0012"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RC0603FR-0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "15K"; VariantName = "[No Variations]"
Change component parameters: Designator = "R92" ("R92"); Footprint = "R0603"; UniqueID = "\BEOTKSBU" ("\BEOTKSBU")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "6.2K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R93" ("R93"); Footprint = "R0603"; UniqueID = "\SMUYREIX" ("\SMUYREIX")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "2K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R94" ("R94"); Footprint = "R0603"; UniqueID = "\OMLFMJOA" ("\OMLFMJOA")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "5.1K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R95" ("R95"); Footprint = "R0603"; UniqueID = "\NCAOYWGI" ("\NCAOYWGI")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "1.3K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R99" ("R99"); Footprint = "R0603"; UniqueID = "\FYFKKHKL" ("\FYFKKHKL")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "1.5K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R100" ("R100"); Footprint = "R0603"; UniqueID = "\ZZCDJROO" ("\ZZCDJROO")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "1.3K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R101" ("R101"); Footprint = "R0603"; UniqueID = "\LSSMGCLF" ("\LSSMGCLF")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "1.8K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R104" ("R104"); Footprint = "R0603"; UniqueID = "\IGVCLUWF" ("\IGVCLUWF")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "2K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R107" ("R107"); Footprint = "R0603"; UniqueID = "\COTBGUEM" ("\COTBGUEM")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "1.5K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R108" ("R108"); Footprint = "R0603"; UniqueID = "\YUWQJMCO" ("\YUWQJMCO")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "1.8K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R109" ("R109"); Footprint = "R0603"; UniqueID = "\WKDIPBHG" ("\WKDIPBHG")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "1.3K/0.1%"; VariantName = "[No Variations]"
Change component parameters: Designator = "R111" ("R111"); Footprint = "R0603"; UniqueID = "\HANQJHNY" ("\HANQJHNY")
Change component parameters (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "ERP code"; Value = "ADQELR0046"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Specification"; Value = "RT0603BRD0710KL"; VariantName = "[No Variations]"
Change component parameters (AddParameter): Name = "Value"; Value = "2K/0.1%"; VariantName = "[No Variations]"
Added Component: Designator=R3(R0603)
Add component (AddParameter): Name = "Brand"; Value = "YAGEO"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "ERP code"; Value = "ADQELR0012"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Soldering Type"; Value = "SMT"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Specification"; Value = "RC0603FR-0710KL"; VariantName = "[No Variations]"
Add component (AddParameter): Name = "Value"; Value = "500R"; VariantName = "[No Variations]"
Added Pin To Net: NetName=XTALOUT Pin=R3-2
Change Net Name : Old Net Name=NetJ1_A5 New Net Name=NetJ8_A5
Change Net Name : Old Net Name=NetJ1_B5 New Net Name=NetJ8_B5
Change Net Name : Old Net Name=XTALOUT New Net Name=NetC29_2
Added Pin To Net: NetName=XTALOUT Pin=R3-1
Added Pin To Net: NetName=XTALOUT Pin=U5-8
Added Net: Name=XTALOUT
Add Differential Pair: Number of nets in differential pair USB_D+ is 1 instead of 2
Added Member To Class: ClassName=CONNECT Member=Component J8 TYPE-C-31-M-12
Added Member To Class: ClassName=RDC Member=Component R3 500R
