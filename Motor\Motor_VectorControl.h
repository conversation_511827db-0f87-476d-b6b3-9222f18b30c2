/*
 * Motor_VectorControl.h
 *
 *  Created on: 2018年9月17日
 *      Author: Hiter
 */

#ifndef INCLUDE_MOTOR_VECTORCONTROL_H_
#define INCLUDE_MOTOR_VECTORCONTROL_H_
#include "at32a403a.h"
//#define PI  3.14159265359
#define DPI 6.2831853072


//======================================================================================================================================//
// main program code of the vector control of induction motor
typedef struct {
//-------------------------------------------------//
    unsigned int control_method;                    // 1:starter     2:Ideling     3:Generator
    unsigned int start;                             //启动VC控制，0禁止1使能

    unsigned int StartReady_Flag;                   //起动阶段接触器准备标志位，0为未准备好，1位准备好
    unsigned int IdelReady_Flag;                    //怠速阶段接触器准备标志位，0为未准备好，1位准备好
    unsigned int GenerateReady_Flag;                //怠速阶段接触器准备标志位，0为未准备好，1位准备好

    unsigned int PrePosition_Flag;                  //初始预定位标志位
    unsigned int SensorlessCtl_Flag;                //无速度传感器切换标志位
    unsigned int StopFlag;                          //停止标志位
    unsigned int Sensorlessmode;                   //无速度传感器切换标志位

    unsigned int Over_modu;                         //过调制 not used
    unsigned int CT_Flag;                           //恒转矩阶段标志位
    unsigned int CP_Flag;                           //恒功率阶段标志位
    unsigned int SensorlessCtl;                     //无速度传感器控制标志位，0表示不使用无速度传感器，1表示使用无速度传感器
    unsigned int controlLoopMode;                   //控制环路模式，0-速度电流闭环，1-电流环；2-开环频率控制
    float Ts;                                       //控制周期
    int WaiteTime;                                  //接触器触点反馈延迟
//-------------------------------------------------//
    float PreId;                                    //测试信号1
    float GenLowVolt;                                    //测试信号2
    float debugstep;                                    //测试信号3
    float CurrentLimit;                                    //测试信号3
    float IFiq;
//-------------------------------------------------//
//input
    float Isa1_SI;                                   //A相电流实际值
    float Isb1_SI;                                   //B相电流实际值
    float Isc1_SI;                                   //C相电流实际值
    float Isa2_SI;                                   //A相电流实际值
    float Isb2_SI;                                   //B相电流实际值
    float Isc2_SI;                                   //C相电流实际值
    float Isa_PU;                                   //输入A相电流标幺值
    float Isb_PU;                                   //输入B相电流标幺值
    float Isc_PU;                                   //输入C相电流标幺值
    float Isa_flt;                                  //滤波后三相电流
    float Isb_flt;
    float Isc_flt;
    float Isa1_flt;                                  //滤波后三相电流(第一套绕组)
    float Isb1_flt;
    float Isc1_flt;
    float Isa2_flt;                                  //滤波后三相电流(第二套绕组)
    float Isb2_flt;
    float Isc2_flt;
    float Vdc_SI;                                   //直流母线电压实际值
    float Idc_SI;
    float Vdc_PU;                                   //标幺值
    float Vdc_up_SI;                                //上电容电压实际值
    float Vdc_down_SI;                              //下电容电压实际值
    float Usa_SI;                                   //A相电压实际值
    float Usb_SI;                                   //B相电压实际值
    float Usc_SI;                                   //C相电压实际值
    float Usa_PU;                                   //标幺值
    float Usb_PU;
    float Usc_PU;

    float ISA_SI;                                 //两相静止坐标系下alpha的电流
    float ISB_SI;                                 //两相静止坐标系下beta的电流
    float ISA1_SI;                                 //两相静止坐标系下alpha的电流
    float ISB1_SI;                                 //两相静止坐标系下beta的电流
    float ISA2_SI;                                 //两相静止坐标系下alpha的电流
    float ISB2_SI;                                 //两相静止坐标系下beta的电流

    float wr_meca_SI;               //机械角速度


    float posi_meca;                /* Speed reference given by PC scope */
    float posi_elec;                //
    float posi_ab;                  //转子的绝对位置
    float speed_ref_Hz;             //
    float Vol_ref;
    float speed_ref;                /* Speed reference  */
    float speed_ref_flt;            /* Speed reference after filter (pu) */
    float Udqmax;                   //
    float flux_theta;                             //角度
    float flux_theta_cal;                             //角度
    float theta_preposition;                              //初始位置误差
    float cos_theta;                              //余弦
    float sin_theta;                              //正弦
    float cos_theta2;                              //余弦2倍角度值
    float sin_theta2;                              //正弦2倍角度值


    float P_ref;                                    //恒功率运行
    float Te_ref;             /* reference electromagnetic torque  */
//-------------------------------------------------//
//output
    float Ua_ref;                   //A相输出参考值,实际值
    float Ub_ref;                   //B相输出参考值,实际值
    float Uc_ref;                   //C相输出参考值,实际值
    float Ua1_ref;                   //A相输出参考值,实际值
    float Ub1_ref;                   //B相输出参考值,实际值
    float Uc1_ref;                   //C相输出参考值,实际值
    float Ua2_ref;                   //A相输出参考值,实际值
    float Ub2_ref;                   //B相输出参考值,实际值
    float Uc2_ref;                   //C相输出参考值,实际值

    float Freq_Uref;                //输出频率参考值
//-------------------------------------------------//
    float wr_elec;                  //电角速度
    float wr_meca;                  //机械角速度
    float wr_elec_flt;              //电角速度滤波
    float wr_elec_flt_cal;
//-------------------------------------------------//
//input

    float speedback;                //反馈速度
    float speedback_flt;            //反馈速度滤波后

    float SpeedLimit;

 //---------------------------电压环控制---------------------//
    float voltage_ref;              //电压环参考值
//-------------------------------------------------//
    int voltage_pi_count;               //电压环比例增益
    float p_voltage_coe;                //电压环比例增益
    float i_voltage_coe;                //电压环积分增益

    int speed_pi_count;                 //转速环计数
    float p_speed_coe;                  //转速环比例增益
    float i_speed_coe;                  //转速环积分增益

    float p_isd_coe;                    //电流环比例增益
    float i_isd_coe;                    //电流环积分增益

    float p_isq_coe;                    //电流环比例增益
    float i_isq_coe;                    //电流环积分增益

    int fluxweaken_pi_count;
    float p_flux_coe;                   //磁链环比例增益
    float i_flux_coe;                   //磁链环积分增益
//--------------------------------------------------//
    /* reference stator d-axis current  */
    float   isd_ref;                    //d轴电流给定值
    float   isd_ref_flt;                //d轴电流给定滤波值
    /* reference stator q-axis current  */
    float   isq_ref;                    //q轴电流给定值
    float   isq_ref_flt;                //q轴电流给定滤波值
    float   Is_ref;

    /*实际电流*/
    float   isd;                        //d轴电流实际值
    float   isd_flt;                    //d轴电流实际滤波值
    float   isq;                        //q轴电流实际值
    float   isq_flt;                    //q轴电流实际值滤波
    float   isd1;                        //d轴电流实际值
    float   isd2;                        //d轴电流实际值
    float   isq1;                        //d轴电流实际值
    float   isq2;                        //d轴电流实际值

    float   Vsd_ref;            /* reference stator d-axis voltage  */
    float   Vsq_ref;            /* reference stator q-axis voltage  */
    float   Us_ref;
    float   Vsd1_ref;            /* reference stator d-axis voltage  */
    float   Vsq1_ref;            /* reference stator q-axis voltage  */
    float   Vsd2_ref;            /* reference stator d-axis voltage  */
    float   Vsq2_ref;            /* reference stator q-axis voltage  */

    float   Vsd_ref_new;            /* reference stator d-axis voltage  */
    float   Vsq_ref_new;            /* reference stator q-axis voltage  */

    float   Vsd_comp;                   //d轴交叉解耦项，补偿项
    float   Vsq_comp;                   //d轴交叉解耦项，补偿项
    float   Vsd1_comp;                   //d轴交叉解耦项，补偿项
    float   Vsq1_comp;                   //d轴交叉解耦项，补偿项
    float   Vsd2_comp;                   //d轴交叉解耦项，补偿项
    float   Vsq2_comp;                   //d轴交叉解耦项，补偿项


    float   k_comp;                     //1.0;
    float   Tem;                        // the calculated torque

    float  Tem_max;                     //转矩最大
    float  isdref_max;                  //dq轴电流最大值
    float  isqref_max;                  //dq轴电流最大值
    float  Vsdref_max;                  //dq轴电压最大值
    float  Vsqref_max;                  //dq轴电压最大值
    float VSdq_ref;                     // 给定输出电压幅值
    //-------------------------------------------------//
    // for the flux weakening
    float flux_level;
    float flux_theta_est;
    float wr_elec_flt_est;
    float we_est_old;


    float Vdc_inv;                          //电压的倒数

//============================死区设置=================================
    float DB_com_enable;                    //死区补偿使能
    float DB_com;                           //死区补偿值
    float Ts_dead_us;                       //死区时间

    unsigned int counter;                               //初始预定位计数器
    unsigned int counter2;                              //初始预定位计数器2

    float wr_elec_flt_ekf_est;
    float YUDINGtime;                       //死区时间
    float ZhengBuChang;                       //
    float speedcount;
    float RUOCIxishu;
    float FILTERUDC;
    float GiveUdc;
    float PrePositionEN;

    float kq;
    float kd;

    float filteria;
    float filterib;
    float filteric;
    //------------------------------定义断z相故障后的变量------------//
    float isdz;
    float isqz;
    float isxz;
    float ixz_ref;
    float Vmz_comp;
    float Vnz_comp;
    float Vnz_ref;
    float Vmz_ref;
    float Vsdz_ref;
    float Vsqz_ref;
    float Vsxz_ref;
//-------------------------------------------------//
    void  (*reset_Ts)();
    void  (*init)();
    void  (*reset)();
    void  (*calc)();    /* Pointer to calculation function */
    void  (*voltagebalance)();    /* Pointer to calculation function */
    void  (*marsest)();
     } Vector_ctrl;

typedef Vector_ctrl *Vector_ctrl_handle;
//----------------------------------------------------------------//
#define Vector_ctrl_DEFAULTS {0,0,0,0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1.0,0,400,0,0,0,\
                              0,0,0,0,0,0,0,0,0,0,0,0,0,0,\
                              (void (*)(long))Vector_ctrl_ResetTs, \
                              (void (*)(long))Vector_ctrl_init, \
                              (void (*)(long))Vector_ctrl_reset, \
                              (void (*)(long))Vector_ctrl_calc,\
							  (void (*)(long))VoltageBalance_calc,\
							  (void (*)(long))marsest_calc,\
    }
//----------------------------------------------------------------//
void Vector_ctrl_ResetTs(Vector_ctrl_handle);
void Vector_ctrl_init(Vector_ctrl_handle);
void Vector_ctrl_reset(Vector_ctrl_handle);
void Vector_ctrl_calc(Vector_ctrl_handle);
void VoltageBalance_calc(Vector_ctrl_handle);
void marsest_calc(Vector_ctrl_handle);
//======================================================================================================================================//

//======================================================================================================================================//
typedef struct
{
    //-------------------------//
    float Ts;                               //控制周期
    float Fs_cpu_M;                         //150M Hz
    //-------------------------//
    float Rs;                               //定子电阻
    float Lsd;                              //定子电感
    float Lsq;                              //定子电感
    float Ld;                               //d轴电感
    float Lq;                               //q轴电感
    float Laaq;                             //q轴主自感
    float Laad;                             //d轴主自感
    float Laal;                             //漏感值
    unsigned int p;                         //极对数  poles paires
    float flux_r;                           //转子磁链
    float Inertia;                          //转动惯量
    float friction;                         //摩擦系数
    float flux_r_N;
    //-------------------------//
    float P_N;                              //额定功率
    float UL_N;                             //额定线电压
    float UP_N;                             //额定相电压
    float I_N;                              //额定电流
    float F_N;                              //额定频率
    float rpm_N;                            //额定转速（rpm）
    float wmeca_N;                          //额定机械角速度（rad/s）
    float ws_N;                             //同步角速度
    float T_N;                              //额定转矩
    //---------PI参数----------------//
    float Wc_wr;                            //转速环截止频率
    float Wc_cur;                           //电流环截止频率

    unsigned int H;
    //-------------------------//
    float Imax;                             //最大电流
    float Umax;                             //最大电压
    float Udq_max;                          //最大dq轴电压
    float Idq_max;                          //最大dq轴电流
    float Torque_max;                       //最大转矩
    float flux_max;                         //最大磁链
    float Freq_max;                         //最大频率
    //------------------------------------//
    //base values
    float BASE_U;                           //电压基准值
    float BASE_I;                           //电流基准值
    float BASE_R;                           //电阻基准值
    float BASE_L;                           //电感基准值
    float BASE_Power;                       //功率基准值
    float BASE_w;                           //角速度基准值
    float BASE_time;                        //时间基准值
    float BASE_FREQ;                        //频率基准值
    float BASE_Torque;                      //转矩基准值
    float BASE_Flux;                        //磁链基准值
    float BASE_Interia;                     //转动惯量基准值
    float BASE_U_inv;                       //电压倒数基准值
    float BASE_I_inv;                       //电流倒数基准值
    //------------------------------------//
    //p.u. values
    float rs;                               //定子电阻标幺值
    float ls;                               //定子电感标幺值
    float ld;                               //ld标幺值
    float lq;                               //lq标幺值

    float flux_r_pu;                        //转子磁链标幺值
    float inertia_pu;                       //转动惯量标幺值
    float friction_pu;                      //摩擦系数标幺值
    float wc_wr_pu;                         //转速环截止频率标幺值
    float wc_cur_pu;                        //电流环截止频率标幺值

    float Imax_pu;                          //最大电流标幺值
    float Umax_pu;                          //最大电压标幺值
    float Udq_max_pu;                       //dq轴电压标幺值
    float Idq_max_pu;                       //dq轴电流标幺值
    float Torque_max_pu;                    //最大转矩标幺值
    float flux_max_pu;                      //最大磁链标幺值
    float freq_max_pu;                      //最大频率标幺值
    float ts;                               //时间基准标幺值

    //------------------------------------//
    void  (*derive)();
} Parameter_set;


typedef Parameter_set *Parameter_set_handle;
//-------------------------------------------------------------------------------
#define Parameter_set_DEFAULTS {0,0,0,0,0,0,0,0,0,0,0,0,0,0,\
                                0,0,0,0,0,0,0,0,0,0,\
                                0,0,0,0,0,0,0,0,0,0,\
                                0,0,0,0,0,0,0,0,0,0,\
                                0,0,0,0,0,0,0,0,0,0,\
                                0,0,0,0,0,0,0,0,0,0,\
                          (void (*)(long))Para_derive }

void Para_derive(Parameter_set_handle);

//============================================================================================//


typedef struct
{
    float Rs;               //定子电阻
    float Ls;               //定子电感
    float Ld;               //定子d轴电感
    float Lq;               //定子q轴电感

    unsigned int p;         // 极对数poles paires
    float Ts;               //控制周期
    float flux_rN;          //磁链的额定值
    float Inertia;          //转动惯量
    float friction;         //摩擦系数
    float Wc_wr;            //转速环截止频率
    float Wc_cur;           //电流环截止频率
    float Imax;             //最大电流
    float Umax;             //最大电压
    float Udq_max;          //dq轴最大电压
    float Idq_max;          //dq轴最大电流
    float Torque_max;       //最大转矩
    float flux_max;         //最大磁链
    float Freq_max;         //最大频率
    //------------------------//
} Parameter_used;
//============================================================================================//
//==================================================================================//
// coordinate tranformation for the vector control
//==================================================================================//
typedef struct {  float  ds;        /* Input: stationary d-axis stator variable */
                  float  qs;            /* Input: stationary q-axis stator variable */
                  float  ang;           /* Input: rotating angle (pu) */
                  float  de;            /* Output: rotating d-axis stator variable */
                  float  qe;            /* Output: rotating q-axis stator variable */
                  float  sin_ang;
                  float  cos_ang;
                  void  (*calc)();  /* Pointer to calculation function */
                 } PARK;

typedef PARK *PARK_handle;

#define PARK_DEFAULTS {  0,0,0,0,0,0,0, \
                          (void (*)(long))park_calc }

//-------------------------------------------------------------//
void park_calc(PARK_handle);
//===================================================================================================//

typedef struct {  float  ds;    /* Output: stationary d-axis stator variable */
                  float  qs;        /* Output: stationary q-axis stator variable */
                  float  ang;       /* Input: rotating angle (pu) */
                  float  de;        /* Input: rotating d-axis stator variable */
                  float  qe;        /* Input: rotating q-axis stator variable */
                  float  sin_ang;
                  float  cos_ang;
                  void  (*calc)();  /* Pointer to calculation function */
                 } IPARK;

typedef IPARK *IPARK_handle;


//-------------------------------------------------------------//
#define IPARK_DEFAULTS {  0,0,0,0,0,0,0, \
                          (void (*)(long))ipark_calc }

//-------------------------------------------------------------//
void ipark_calc(IPARK_handle);

//===================================================================================================//
typedef struct {  float  as;        /* Output: phase-a stator variable  */
                  float  bs;            /* Output: phase-b stator variable  */
                  float  cs;            /* Output: phase-c stator variable  */
                  float  ds;            /* Input: stationary d-axis stator variable  */
                  float  qs;            /* Input: stationary q-axis stator variable  */
                  float  x0;
                  void  (*calc)();  /* Pointer to calculation function */
                 } CLARKE;

typedef CLARKE *CLARKE_handle;
//===================================================================================================//

#define CLARKE_DEFAULTS { 0,0,0,0,0,0, \
                          (void (*)(long))clarke_calc }
void clarke_calc(CLARKE_handle);

//==============定义VSD控制
typedef struct {    float  ias;        /* Output: phase-a stator variable  */
                    float  ibs;            /* Output: phase-b stator variable  */
                    float  ics;            /* Output: phase-c stator variable  */
                    float  ixs;        /* Output: phase-a stator variable  */
                    float  iys;            /* Output: phase-b stator variable  */
                    float  izs;            /* Output: phase-c stator variable  */
                    float  alp;            /* Output: phase-b stator variable  */
                    float  bet;            /* Output: phase-c stator variable  */
                    float  xs;
                    float  ys;
                    void  (*calc)();  /* Pointer to calculation function */
                 } VSDCLARKE;

typedef VSDCLARKE *VSDCLARKE_handle;
//===================================================================================================//

#define VSDCLARKE_DEFAULTS { 0,0,0,0,0,0,0,0,0,0, \
                          (void (*)(long))VSDclarke_calc }
void VSDclarke_calc(VSDCLARKE_handle);

//==========定义iVSD控制

typedef struct {    float  ias;        /* Output: phase-a stator variable  */
                    float  ibs;            /* Output: phase-b stator variable  */
                    float  ics;            /* Output: phase-c stator variable  */
                    float  ixs;        /* Output: phase-a stator variable  */
                    float  iys;            /* Output: phase-b stator variable  */
                    float  izs;            /* Output: phase-c stator variable  */
                    float  alp;            /* Output: phase-b stator variable  */
                    float  bet;            /* Output: phase-c stator variable  */
                    float  xs;
                    float  ys;
                    void  (*calc)();  /* Pointer to calculation function */
                 } IVSDCLARKE;

typedef IVSDCLARKE *IVSDCLARKE_handle;
//===================================================================================================//

#define IVSDCLARKE_DEFAULTS { 0,0,0,0,0,0,0,0,0,0,\
                          (void (*)(long))IVSDclarke_calc }
void IVSDclarke_calc(IVSDCLARKE_handle);
//=====================================故障下的VSD坐标变换
typedef struct {    float  ias;        /* Output: phase-a stator variable  */
                    float  ibs;            /* Output: phase-b stator variable  */
                    float  ics;            /* Output: phase-c stator variable  */
                    float  ixs;        /* Output: phase-a stator variable  */
                    float  iys;            /* Output: phase-b stator variable  */
                    float  alp;            /* Output: phase-b stator variable  */
                    float  bet;            /* Output: phase-c stator variable  */
                    float  xs;
                    void  (*calc)();  /* Pointer to calculation function */
                 } VSDCLARKEfault;

typedef VSDCLARKEfault *VSDCLARKEfault_handle;
//===================================================================================================//

#define VSDCLARKEfault_DEFAULTS { 0,0,0,0,0,0,0,0,\
                          (void (*)(long))VSDclarkefault_calc }
void VSDclarkefault_calc(VSDCLARKEfault_handle);
//===================故障VSD反变换

typedef struct {    float  ias;        /* Output: phase-a stator variable  */
                    float  ibs;            /* Output: phase-b stator variable  */
                    float  ics;            /* Output: phase-c stator variable  */
                    float  ixs;        /* Output: phase-a stator variable  */
                    float  iys;            /* Output: phase-b stator variable  */
                    float  alp;            /* Output: phase-b stator variable  */
                    float  bet;            /* Output: phase-c stator variable  */
                    float  xs;
                    float  z2s;
                    float  z3s;
                    void  (*calc)();  /* Pointer to calculation function */
                 } IVSDCLARKEfault;

typedef IVSDCLARKEfault *IVSDCLARKEfault_handle;
//===================================================================================================//

#define IVSDCLARKEfault_DEFAULTS { 0,0,0,0,0,0,0,0,0,0,\
                          (void (*)(long))IVSDclarkefault_calc }
void IVSDclarkefault_calc(IVSDCLARKEfault_handle);

//===========================================定义故障检测变换矩阵=========================

typedef struct {    float  ias;        /* Output: phase-a stator variable  */
                    float  ibs;            /* Output: phase-b stator variable  */
                    float  ics;            /* Output: phase-c stator variable  */
                    float  ixs;        /* Output: phase-a stator variable  */
                    float  iys;            /* Output: phase-b stator variable  */
                    float  izs;            /* Output: phase-b stator variable  */
                    float  uab;            /* Output: phase-b stator variable  */
                    float  ubc;            /* Output: phase-b stator variable  */
                    float  uxy;            /* Output: phase-b stator variable  */
                    float  uyz;            /* Output: phase-b stator variable  */
                    float  ix1;            /* Output: phase-b stator variable  */
                    float  iy1;            /* Output: phase-c stator variable  */
                    float  ix2;            /* Output: phase-b stator variable  */
                    float  iy2;            /* Output: phase-c stator variable  */
                    float  ix3;            /* Output: phase-b stator variable  */
                    float  iy3;            /* Output: phase-c stator variable  */
                    float  ux1;            /* Output: phase-b stator variable  */
                    float  uy1;            /* Output: phase-c stator variable  */
                    float  ux2;            /* Output: phase-b stator variable  */
                    float  uy2;            /* Output: phase-c stator variable  */
                    float  ux3;            /* Output: phase-b stator variable  */
                    float  uy3;            /* Output: phase-c stator variable  */
                    void  (*calc)();  /* Pointer to calculation function */
                 } Fault_detection;

typedef Fault_detection *Fault_detection_handle;
//===================================================================================================//

#define Fault_detection_DEFAULTS { 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,\
                          (void (*)(long))Fault_detection_calc }
void Fault_detection_calc(Fault_detection_handle);

//-------------------------------------------------------------//

typedef struct {  float  as;        /* Output: phase-a stator variable  */
                  float  bs;            /* Output: phase-b stator variable  */
                  float  cs;            /* Output: phase-c stator variable  */
                  float  ds;            /* Input: stationary d-axis stator variable  */
                  float  qs;            /* Input: stationary q-axis stator variable  */
                  float x0;
                  void  (*calc)();  /* Pointer to calculation function */
                 } ICLARKE;

typedef ICLARKE *ICLARKE_handle;
//-------------------------------------------------------------//
#define ICLARKE_DEFAULTS { 0,0,0,0,0,0, \
                          (void (*)(long))iclarke_calc }
//-------------------------------------------------------------//

void iclarke_calc(ICLARKE_handle);

//===========================容错算法时坐标二次变化============================//
typedef struct {    float  cos;        /* Output: phase-a stator variable  */
                    float  sin;            /* Output: phase-b stator variable  */
                    float  um;            /* Output: phase-c stator variable  */
                    float  un;        /* Output: phase-a stator variable  */
                    float  ud;            /* Output: phase-b stator variable  */
                    float  uq;            /* Output: phase-b stator variable  */
                    void  (*calc)();  /* Pointer to calculation function */
                 } Quadratic_coordinate;

typedef Quadratic_coordinate *Quadratic_coordinate_handle;

#define Quadratic_coordinate_DEFAULTS { 0,0,0,0,0,0,\
                          (void (*)(long))Quadratic_coordinate_calc }
void Quadratic_coordinate_calc(Quadratic_coordinate_handle);








//======================================================================================================================================//
typedef struct {    float   x;              /* Input:Input of the low pass filter (PU) */
                    float   Tc;             /* Parameter:Sampling period (PU) */
                    float   wc;             /* Parameter:Cut off frequency for low pass filter (PU) */
                    float   y_old;          /* Variable:Output of the last cycle (PU) */
                    float   y;              /* Output:Output of the low pass filter (PU) */
                    void  (*calc)();    /* Po_iqer to calculation function */
                 } LPFL;

typedef LPFL *LPFL_handle;

//-------------------------------------------------------------//
#define LPFL_DEFAULTS {0,0,0,0,0,  \
                          (void (*)(long))lpfl_calc }
//-------------------------------------------------------------//
void lpfl_calc(LPFL_handle);
//=======================================================================//

typedef struct {    float   x;              /* Input:Input of the low pass filter (PU) */
                    float   Tc;             /* Parameter:Sampling period (PU) */
                    float wc;               /* Parameter:Cut off frequency for low pass filter (PU) */
                    float   y_old;          /* Variable:Output of the last cycle (PU) */
                    float   y;              /* Output:Output of the low pass filter (PU) */
                    void  (*calc)();    /* Po_iqer to calculation function */
                 } LPFI;

typedef LPFI *LPFI_handle;
#define LPFI_DEFAULTS {0,0,0,0,0,   \
                          (void (*)(long))lpfi_calc }
//-------------------------------------------------------------//
void lpfi_calc(LPFI_handle);

//======================================================================================================================================//

typedef struct {    float   wc_wr;          /* Input:Cut-off frequency for speed loop filter (PU) */
                    float   inertia;    /* Input:Inertia (PU) */
                    float   h;              /* Input:Bandwidth of the intermediate frequency */
                    float   Ki;             /* Output:Integral gain */
                    float   Kp;             /* Output:Proportional gain */
                    void  (*calc)();    /* Pofloater to calculation function */
                 } PI_SPEED_CONST;

typedef PI_SPEED_CONST *PI_SPEED_CONST_handle;
//-------------------------------------------------------------//
#define PI_SPEED_CONST_DEFAULTS {0,0,0,0,0, \
                          (void (*)(long))pi_speed_const_calc }
//-------------------------------------------------------------//
void pi_speed_const_calc(PI_SPEED_CONST_handle);

/*******************************************************************************/

typedef struct {    float   wc_current;     /* Input:Cut-off frequency for current loop filter (PU) */
                    float   rr;             /* Input:Rotor resistance (PU) */
                    float   rs;             /* Input:Stator resistance (PU) */
                    float   lm;             /* Input:Flux inductance (PU) */
                    float   lr;             /* Input:Rotor inductance (PU) */
                    float   ls;             /* Input:Stator inductance (PU) */
                    float   Ki;             /* Output:Integral gain */
                    float   Kp;             /* Output:Proportional gain */
                    void  (*calc)();    /* Pofloater to calculation function */
                 } PI_CURRENT_CONST;

typedef PI_CURRENT_CONST *PI_CURRENT_CONST_handle;
//-------------------------------------------------------------//
#define PI_CURRENT_CONST_DEFAULTS {0,0,0,0,0,0,0,0, \
                          (void (*)(long))pi_current_const_calc }
//-------------------------------------------------------------//
void pi_current_const_calc(PI_CURRENT_CONST_handle);


/*******************************************************************************/
typedef struct {    float   pi_fdb;         /* Input:Feedback value */
                    float   pi_ref;         /* Input:Reference value */
                    float   Tc;             /* Parameter:sampleing period */
                    float   Ki;             /* Parameter:Integral gain */
                    float   Kp;             /* Parameter:Proportional gain */
                    float   pi_out_max;     /* Parameter:Maximum output */
                    float   err;            /* Variable:Input error */
                    float   up;             /* Variable:Proportional output */
                    float   ui;             /* Variable:Integral output */
                    float   ui_delta;       /* Variable:Integtal increment */
                    float   pi_out;         /* Output:PI output */
                    float   err1;
                    int pi_gain;        /* pi_gain determin the scale of pi */
                    void  (*calc)();    /* Pofloater to calculation function */
                 } PI_SPEED;

typedef PI_SPEED *PI_SPEED_handle;
//-------------------------------------------------------------//
#define PI_SPEED_DEFAULTS {0,0,0,0,0,0,0,0,0,0,0,  \
                           0,0,  \
                      (void (*)(long))pi_speed_calc }
//-------------------------------------------------------------//
void pi_speed_calc(PI_SPEED_handle);

/*******************************************************************************/
/*******************************************************************************/
typedef struct {    float   pi_fdb;         /* Input:Feedback value */
                    float   pi_ref;         /* Input:Reference value */
                    float   Tc;             /* Parameter:sampleing period */
                    float   Ki;             /* Parameter:Integral gain */
                    float   Kp;             /* Parameter:Proportional gain */
                    float   pi_out_max;     /* Parameter:Maximum output */
                    float   err;            /* Variable:Input error */
                    float   up;             /* Variable:Proportional output */
                    float   ui;             /* Variable:Integral output */
                    float   ui_delta;       /* Variable:Integtal increment */
                    float   pi_out;         /* Output:PI output */
                    int pi_gain;        /* pi_gain determin the scale of pi */
                    void  (*calc)();    /* Pofloater to calculation function */
                 } PI_VOLTAGE;

typedef PI_VOLTAGE *PI_VOLTAGE_handle;
//-------------------------------------------------------------//
#define PI_VOLTAGE_DEFAULTS {0,0,0,0,0,0,0,0,0,0,  \
                           0,0,  \
                      (void (*)(long))pi_voltage_calc }
//-------------------------------------------------------------//
void pi_voltage_calc(PI_VOLTAGE_handle);

/*******************************************************************************/

typedef struct {    float   pi_fdb;         /* Input:Feedback value */
                    float   pi_ref;         /* Input:Reference value */
                    float   Tc;             /* Parameter:sampleing period */
                    float   Ki;             /* Parameter:Integral gain */
                    float   Kp;             /* Parameter:Proportional gain */
                    float   pi_out_max;     /* Parameter:Maximum output */
                    float   pi_var_max;     /* Parameter:Maximum variable */
                    float   err;            /* Variable:Input error */
                    float   up;             /* Variable:Proportional output */
                    float   ui;             /* Variable:Integral output */
                    float   ui_delta;       /* Variable:Integtal increment */
                    float   pi_out;         /* Output:PI output */
                    float   err1;
                    void  (*calc)();    /* Pofloater to calculation function */
                 } PI_fun;

typedef PI_fun *PI_fun_handle;
//-------------------------------------------------------------//
#define PI_DEFAULTS {0,0,0,0,0,0,0,0,0,0,  \
                     0,0,0,  \
                          (void (*)(long))pi_fun_calc }
//-------------------------------------------------------------//
void pi_fun_calc(PI_fun_handle);

/*******************************************************************************/

typedef struct {    float   pi_fdb;         /* Input:Feedback value */
                    float   pi_ref;         /* Input:Reference value */
                    float   Tc;             /* Parameter:sampleing period */
                    float   Ki;             /* Parameter:Integral gain */
                    float   Kp;             /* Parameter:Proportional gain */
                    float   pi_out_max;     /* Parameter:Maximum output */
                    float   pi_out_min;     /* Parameter:Maximum variable */
                    float   err;            /* Variable:Input error */
                    float   up;             /* Variable:Proportional output */
                    float   ui;             /* Variable:Integral output */
                    float   ui_delta;       /* Variable:Integtal increment */
                    float   pi_out;         /* Output:PI output */
                    void  (*calc)();    /* Pofloater to calculation function */
                 } PI_flux;

typedef PI_flux *PI_flux_handle;
//-------------------------------------------------------------//
#define PI_flux_DEFAULTS {0,0,0,0,0,0,0,0,0,0,  \
                           0,0,  \
                          (void (*)(long))pi_flux_calc }
//-------------------------------------------------------------//
typedef struct LPF
{
    float input;
    float last_value;
    float a;
    float filter_vlaue;
} LPF_TypeDef;												


typedef struct CurvePara {
  float start_speed;    //开始调速时的初始速度
  float current_speed;   //当前速度
  float target_speed;    //目标速度
  float step_speed;     //加速度
  float speed_max;     //最大速度
  float speed_min;     //最小速度
  uint32_t aTimes;     //调速时间
  uint32_t max_times;    //调速跨度
  float out_speed;
  uint16_t index;
  uint16_t step_time;
}CurvePara_TypeDef;

void pi_flux_calc(PI_flux_handle);

/********************************** 开环频率控制 Start **********************************/
// 开环频率控制结构体
typedef struct {
    float target_freq;       // 目标频率(Hz)
    float current_freq;      // 当前频率(Hz)
    float freq_step;         // 频率步长(Hz/控制周期)
    float max_freq;          // 最大频率限制(Hz)
    float angle;             // 当前角度(rad)
    float angle_step;        // 角度步长(rad/控制周期)
    float angle_coef;        // 角度步长系数
    float Ts;                // 控制周期(s)
} OpenLoopFreqCtrl_TypeDef;
// 函数声明
void OpenLoopFreqCtrl_Init(OpenLoopFreqCtrl_TypeDef *ctrl);
void OpenLoopFreqCtrl_Update(OpenLoopFreqCtrl_TypeDef *ctrl);
/********************************** 开环频率控制 End **********************************/
extern int8_t faultId;

//======================================================================================================================================//
extern Vector_ctrl SynMotorVc;

//============================================================================================//

extern PI_SPEED    pi_speed;
extern PI_VOLTAGE  pi_voltage;
extern PI_fun      pi_isd;
extern PI_fun      pi_isq;
extern PI_fun      pi_isd1;
extern PI_fun      pi_isq1;
extern PI_fun      pi_isd2;
extern PI_fun      pi_isq2;
extern PI_fun      pi_isdz;
extern PI_fun      pi_isxz;
extern PI_fun      pi_isqz;
extern PI_flux     pi_flux_w;                           // PI for the adaptive flux weakening

extern Parameter_set para;
extern CurvePara_TypeDef trapeLine;
void CalcTRAP(CurvePara_TypeDef *trap, float target, float time);
void CalcTRAP(CurvePara_TypeDef *trap, float target, float time);
float TrapeAccelerationCurve(CurvePara_TypeDef *trap);
void update_wave(); //更新波形
#endif /* INCLUDE_MOTOR_VECTORCONTROL_H_ */
