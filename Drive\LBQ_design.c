/**
  * @file     LBQ_design.c
  * @brief    滤波器设计实现文件
  * <AUTHOR>
  * @date     2025-01-22
  * @version  V1.0.0
  */

#include "LBQ_design.h"
#include <string.h>
#include "arm_math.h"

/**
  * @brief  滑动平均滤波器初始化
  * @param  filter: 滤波器指针
  * @param  buffer: 外部提供的缓冲区
  * @param  length: 滤波器长度（会被调整为2的幂）
  * @retval 无
  */
void MA_Filter_Init(MA_Filter_t *filter, uint16_t *buffer, uint16_t length)
{
    filter->buf = buffer;
    filter->len = FILTER_LEN;
    filter->sum = 0;
    filter->head = 0;
    filter->count = 0;
    switch(FILTER_LEN)
    {
        case 32:
            filter->shift = 5;
            break;
        case 16:
            filter->shift = 4;
            break;
        case 8:
            filter->shift = 3;
            break;
        case 4:
            filter->shift = 2;
            break;
        case 2:
            filter->shift = 1;
            break;
        default:
            filter->shift = 0;
            break;
    }

    memset(buffer, 0, sizeof(uint16_t) * FILTER_LEN);
}

/**
 * @brief  温度传感器有效性简易判断
 * @param  adc_value: ADC滤波后原始值
 * @param  invalid_flag_index: 无效标志位索引(0-9对应不同传感器)
 * @retval uint16_t: 有效的ADC值(无效时返回固定值1300，约25°C)
 */
#define TEMP_SENSOR_MIN_VALID  100   // 短路阈值
#define TEMP_SENSOR_MAX_VALID  3950  // 断路阈值
#define TEMP_SENSOR_DEFAULT    1300  // 默认值(25°C)
uint16_t Check_Temp_Sensor_Valid(uint16_t adc_value, uint8_t invalid_flag_index)
{
    // 检查传感器断路/短路
    uint8_t is_invalid = (adc_value <= TEMP_SENSOR_MIN_VALID || adc_value >= TEMP_SENSOR_MAX_VALID);
    
    // 索引左移生成掩码 (1 << index)
    uint16_t mask = 1U << invalid_flag_index;
    
    // 使用位操作设置或清除标志位
    if(is_invalid) {
        gADC_Result.temp_invalid_flags |= mask;  // 设置位
    } else {
        gADC_Result.temp_invalid_flags &= ~mask; // 清除位
    }
    
    return is_invalid ? TEMP_SENSOR_DEFAULT : adc_value;
}


/**
  * @brief  优化滑动平均滤波器更新
  * @param  filter: 滤波器指针
  * @param  new_value: 新数据
  * @retval 滤波后的值
  */
uint16_t MA_Filter_Update(MA_Filter_t *filter, uint16_t new_value)
{
    // 参数检查
    if(filter == NULL || filter->buf == NULL ) {
        return new_value;
    }
    
    // 当前位置和旧值
    uint16_t current_pos = filter->head;
    uint16_t old_value = filter->buf[current_pos];
    
    // 存储新值
    filter->buf[current_pos] = new_value;
    
    // 预计算是否缓冲区已满
    uint8_t buffer_full = (filter->count >= filter->len);
    
    // 单次操作更新总和
    filter->sum = filter->sum - (buffer_full ? old_value : 0) + new_value;
    
    // 更新头指针
    filter->head = (current_pos + 1) & (filter->len - 1);
    
    // 更新计数
    filter->count += (filter->count < filter->len);
    
    // 位移计算平均值
    return (uint16_t)(filter->sum >> filter->shift);
}

// /**
//  * @brief  带去极值的滑动平均滤波器更新（优化版）
//  * @param  filter: 滤波器指针
//  * @param  new_value: 新数据
//  * @retval 滤波后的值
//  */
// uint16_t MA_Filter_Update(MA_Filter_t *filter, uint16_t new_value)
// {
//     // 极简参数检查
//     if(filter == NULL || filter->buf == NULL) {
//         return new_value;
//     }
    
//     // 获取当前位置和旧值
//     uint16_t current_pos = filter->head;
//     uint16_t old_value = filter->buf[current_pos];
    
//     // 检查旧值是否为极值
//     uint8_t old_was_max = (current_pos == filter->max_idx);
//     uint8_t old_was_min = (current_pos == filter->min_idx);
//     uint8_t need_scan = old_was_max | old_was_min;
    
//     // 更新缓冲区
//     filter->buf[current_pos] = new_value;
    
//     // 预计算buffer是否已满
//     uint8_t buffer_full = (filter->count >= filter->len);
    
//     // 使用条件移动更新总和，减少分支
//     filter->sum = filter->sum - (buffer_full ? old_value : 0) + new_value;
    
//     // 更新头指针
//     filter->head = (current_pos + 1) & (filter->len - 1);
    
//     // 更新计数，使用条件移动避免分支
//     filter->count += (filter->count < filter->len);
    
//     // 快速极值更新
//     if(new_value > filter->max_val) {
//         // 新值成为最大值
//         filter->max_val = new_value;
//         filter->max_idx = current_pos;
//         need_scan = 0;  // 不需要扫描
//     } 
//     else if(new_value < filter->min_val) {
//         // 新值成为最小值
//         filter->min_val = new_value;
//         filter->min_idx = current_pos;
//         need_scan = 0;  // 不需要扫描
//     }
    
//     // 仅当旧的极值被覆盖且新值不是新极值时扫描
//     if(need_scan && buffer_full) {
//         // 重置极值
//         filter->max_val = 0;
//         filter->min_val = 0xFFFF;
        
//         // 单次扫描查找新的极值
//         for(uint16_t i = 0; i < filter->count; i++) {
//             uint16_t idx = (filter->head + i) & (filter->len - 1);
//             uint16_t val = filter->buf[idx];
            
//             // 使用条件赋值替代分支
//             uint8_t is_new_max = (val > filter->max_val);
//             filter->max_val = is_new_max ? val : filter->max_val;
//             filter->max_idx = is_new_max ? idx : filter->max_idx;
            
//             uint8_t is_new_min = (val < filter->min_val);
//             filter->min_val = is_new_min ? val : filter->min_val;
//             filter->min_idx = is_new_min ? idx : filter->min_idx;
//         }
//     }
//     // 数据点太少，不去极值
//     if(filter->count <= 2) {
//         return (uint16_t)(filter->sum / filter->count);
//     }
//     // 从总和中减去最大值和最小值
//     uint32_t filtered_sum = filter->sum - filter->max_val - filter->min_val;
//     // 有效数据点数 = 总数 - 2（去掉最大最小值）
//     return (uint16_t)(filtered_sum >> filter->shift);  // 使用预计算的移位值代替除法
// }

/**
 * @brief  IIR滤波器实现
 * @param  input: 输入值
 * @param  prev_output: 上一次输出值
 * @retval 滤波后的值
 */
#define IIR_Filter(input, prev_output) \
    ((IIR_ALPHA) * (prev_output) + (1.0f - (IIR_ALPHA)) * (input))

/**
  * @brief  PT100温度查表计算
  * @param  adc_value: ADC原始值
  * @retval 温度值(摄氏度)
  * @note   使用二分查找快速定位区间
  */
float ADC_Get_PT100_Temp(uint16_t adc_value)
{
    // 边界检查
    if(adc_value <= PT100_ADC_Table[0]) {
        return PT100_TEMP_MIN;
    }
    if(adc_value >= PT100_ADC_Table[PT100_TABLE_SIZE-1]) {
        return PT100_TEMP_MAX;
    }
    
    // 二分查找最接近的索引
    int16_t left = 0;
    int16_t right = PT100_TABLE_SIZE - 1;
    int16_t mid;
    
    while(left < right) {
        mid = left + ((right - left) >> 1);
        
        if(PT100_ADC_Table[mid] < adc_value) {
            left = mid + 1;
        } else {
            right = mid;
        }
    }
    
    // // 边界情况处理
    // if(PT100_ADC_Table[left] == adc_value || left == 0) {
    //     return (float)(left - 40);
    // }
    
    // // 使用四分位法进行插值（无浮点运算）
    // uint16_t range = PT100_ADC_Table[left] - PT100_ADC_Table[left-1];
    // uint16_t position = PT100_ADC_Table[left] - adc_value;
    
    // // 计算四分位点
    // uint16_t quarter = range >> 2;        // 1/4区间
    // uint16_t half = range >> 1;           // 1/2区间
    // uint16_t three_quarters = half + quarter; // 3/4区间
    
    // // 基础温度
    // float base_temp = (float)(left - 40);
    
    // // 使用嵌套三元运算符代替if-else，避免分支预测
    // return position < quarter ? base_temp : 
    //        position < half ? base_temp - 0.25f :
    //        position < three_quarters ? base_temp - 0.5f : 
    //        base_temp - 0.75f;
    return (float)(left-40);
}


/**
  * @brief  PT1000温度查表计算
  * @param  adc_value: ADC原始值
  * @retval 温度值(摄氏度)
  * @note   使用二分查找快速定位区间，性能稳定且不依赖数据分布
  */
float ADC_Get_PT1000_Temp(uint16_t adc_value)
{
    // 边界检查
    if(adc_value <= PT1000_ADC_Table[0]) {
        return PT1000_TEMP_MIN;
    }
    if(adc_value >= PT1000_ADC_Table[PT1000_TABLE_SIZE-1]) {
        return PT1000_TEMP_MAX;
    }
    
    // 二分查找最接近的索引
    int16_t left = 0;
    int16_t right = PT1000_TABLE_SIZE - 1;
    int16_t mid;
    
    // 高效二分查找 - 找到第一个大于等于adc_value的元素
    while(left < right) {
        mid = left + ((right - left)>> 1); // 避免溢出的中点计算
        
        if(PT1000_ADC_Table[mid] < adc_value) {
            left = mid + 1;
        } else {
            right = mid;
        }
    }
    // left--;
    // // 简化的整数线性插值 (0.5°C精度)
    // int16_t temp_base = (left - 40);      // 基础温度值
    // uint16_t adc_current = PT1000_ADC_Table[left];
    // uint16_t adc_next = PT1000_ADC_Table[left+1];
    // uint16_t adc_diff = adc_next - adc_current;


    // // 计算差值
    // uint16_t adc_delta = adc_value - adc_current;
    
    // // 使用四分位划分插值区间
    // uint16_t quarter = adc_diff >> 2;      // 1/4区间
    // uint16_t three_quarters = quarter * 3; // 3/4区间
    
    // // 根据位置返回不同精度的温度值
    // if(adc_delta < quarter) {
    //     return (float)temp_base;           // 0-25%区间，返回基础温度
    // } else if(adc_delta < three_quarters) {
    //     return (float)temp_base + 0.5f;    // 25-75%区间，返回基础温度+0.5
    // } else {
    //     return (float)(temp_base + 1.0f);  // 75-100%区间，返回基础温度+1
    // }
    return (float)(left-40);
}
