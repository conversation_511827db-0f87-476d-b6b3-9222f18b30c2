#include "ENC_Speed.h"
#include "math.h"
#include <stdlib.h> 
#include "MotorCmd.h"
#include "MotorData.h"
/* 数学常量 */
#ifndef PI
#define PI                    3.141592654f
#endif
#define TWO_PI                6.283185307f

/*============================ 系统限制常量 =====================*/
#define MAX_MOTOR_SPEED_RPS    200.0f    // 电机最大转速(RPS)，对应12000RPM

/* 全局变量 */
EncoderHandler_t g_encoder;
static float speed_buffer[32];  // 滑动平均滤波缓冲区
uint32_t g_last_z_detect_time = 0; // Z相检测时间记录

uint16_t CalculateTimerCountFromSPI(uint16_t resolver_pos);
static float CalculateAngleDifference(float current_angle, float last_angle);
void ProcessFhan(float target_speed);
float signed_speed_rps;
/**
 * @brief 初始化卡尔曼滤波器
 * @param q_vel 速度过程噪声
 * @param q_acc 加速度过程噪声
 * @param r 测量噪声
 * @param p0 初始协方差
 */
void InitKalmanFilter(float q_vel, float q_acc, float r, float p0) {
    /* 初始化状态向量 */
    g_encoder.kalman.x[0] = 0.0f;
    g_encoder.kalman.x[1] = 0.0f;
    
    g_encoder.kalman.p[0][0] = p0;
    g_encoder.kalman.p[0][1] = 0.0f;
    g_encoder.kalman.p[1][0] = 0.0f;
    g_encoder.kalman.p[1][1] = p0;
    
    /* 设置噪声参数 */
    g_encoder.kalman.q[0] = q_vel;
    g_encoder.kalman.q[1] = q_acc;
    g_encoder.kalman.r = r;
    
    /* 预计算常量 */
    g_encoder.kalman.inv_r = (r > 0.0001f) ? (1.0f / r) : 1000.0f;
    
    g_encoder.kalman.dt = SPEED_LOOP_TIME;
    g_encoder.kalman.dt_sq = g_encoder.kalman.dt * g_encoder.kalman.dt;
    g_encoder.kalman.dt_half = g_encoder.kalman.dt * 0.5f;
    g_encoder.kalman.dt_sq_half = g_encoder.kalman.dt_sq * 0.5f;
    g_encoder.kalman.inv_dt = (g_encoder.kalman.dt > 0.0001f) ? (1.0f / g_encoder.kalman.dt) : 4000.0f;
    
    /* 初始化ARM DSP矩阵实例 */
    /* 状态转移矩阵 F = [1, dt; 0, 1] */
    g_encoder.kalman.F_data[0] = 1.0f;
    g_encoder.kalman.F_data[1] = g_encoder.kalman.dt;
    g_encoder.kalman.F_data[2] = 0.0f;
    g_encoder.kalman.F_data[3] = 1.0f;
    arm_mat_init_f32(&g_encoder.kalman.F_matrix, 2, 2, g_encoder.kalman.F_data);
    
    g_encoder.kalman.Q_data[0] = q_vel;
    g_encoder.kalman.Q_data[1] = 0.0f;
    g_encoder.kalman.Q_data[2] = 0.0f;
    g_encoder.kalman.Q_data[3] = q_acc;
    arm_mat_init_f32(&g_encoder.kalman.Q_matrix, 2, 2, g_encoder.kalman.Q_data);
    
    g_encoder.kalman.H_data[0] = 1.0f;
    g_encoder.kalman.H_data[1] = 0.0f;
    arm_mat_init_f32(&g_encoder.kalman.H_matrix, 1, 2, g_encoder.kalman.H_data);
    
    g_encoder.kalman.R_data[0] = r;
    arm_mat_init_f32(&g_encoder.kalman.R_matrix, 1, 1, g_encoder.kalman.R_data);
    
    arm_mat_init_f32(&g_encoder.kalman.P_matrix, 2, 2, (float32_t*)g_encoder.kalman.p);
    arm_mat_init_f32(&g_encoder.kalman.K_matrix, 2, 1, g_encoder.kalman.k);
    
    arm_mat_init_f32(&g_encoder.kalman.temp1_matrix, 2, 2, g_encoder.kalman.temp1_data);
    arm_mat_init_f32(&g_encoder.kalman.temp2_matrix, 2, 2, g_encoder.kalman.temp2_data);
    arm_mat_init_f32(&g_encoder.kalman.temp3_matrix, 2, 1, g_encoder.kalman.temp3_data);
}

/**
 * @brief 设置卡尔曼滤波器参数
 * @param q_vel 速度过程噪声
 * @param q_acc 加速度过程噪声
 * @param r 测量噪声
 * @param p0 初始协方差
 */
void SetKalmanFilterParams(float q_vel, float q_acc, float r, float p0) {
    g_encoder.kalman.q[0] = q_vel;
    g_encoder.kalman.q[1] = q_acc;
    g_encoder.kalman.r = r;
    
    /* 重新计算预计算常量 */
    g_encoder.kalman.inv_r = (r > 0.0001f) ? (1.0f / r) : 1000.0f;
    
    g_encoder.kalman.p[0][0] = p0;
    g_encoder.kalman.p[0][1] = 0.0f;
    g_encoder.kalman.p[1][0] = 0.0f;
    g_encoder.kalman.p[1][1] = p0;
}

/**
 * @brief 卡尔曼滤波器更新
 * @param measurement 速度测量值
 * @return 滤波后速度值
 */
static float UpdateKalmanFilter(float measurement) {
    float y, s, inv_s;
    
    /* 步骤1: 预测状态 x_pred = F * x  */
    arm_matrix_instance_f32 x_vector, x_pred_vector;
    arm_mat_init_f32(&x_vector, 2, 1, g_encoder.kalman.x);
    arm_mat_init_f32(&x_pred_vector, 2, 1, g_encoder.kalman.temp3_data);
    
    arm_mat_mult_f32(&g_encoder.kalman.F_matrix, &x_vector, &x_pred_vector);
    
    /* 步骤2: 预测协方差 P_pred = F * P * F' + Q  */
    // temp1 = F * P
    arm_mat_mult_f32(&g_encoder.kalman.F_matrix, &g_encoder.kalman.P_matrix, &g_encoder.kalman.temp1_matrix);
    
    arm_matrix_instance_f32 Ft_matrix;
    float Ft_data[4] = {1.0f, 0.0f, g_encoder.kalman.dt, 1.0f};
    arm_mat_init_f32(&Ft_matrix, 2, 2, Ft_data);
    
    arm_mat_mult_f32(&g_encoder.kalman.temp1_matrix, &Ft_matrix, &g_encoder.kalman.temp2_matrix);
    arm_mat_add_f32(&g_encoder.kalman.temp2_matrix, &g_encoder.kalman.Q_matrix, &g_encoder.kalman.P_matrix);
    
    /* 步骤3: 计算新息 y = measurement - H * x_pred */
    y = measurement - g_encoder.kalman.temp3_data[0]; // H=[1,0]
    
    /* 步骤4: 新息协方差 S = H * P_pred * H' + R */
    s = g_encoder.kalman.p[0][0] + g_encoder.kalman.r;
    
    /* 步骤5: 卡尔曼增益 K = P_pred * H' / S */
    if(_fabsf(s) > 0.0001f) {
        inv_s = 1.0f / s;
        g_encoder.kalman.k[0] = g_encoder.kalman.p[0][0] * inv_s;
        g_encoder.kalman.k[1] = g_encoder.kalman.p[1][0] * inv_s;
    } else {
        g_encoder.kalman.k[0] = 0.0f;
        g_encoder.kalman.k[1] = 0.0f;
    }
    
    /* 步骤6: 更新状态 x = x_pred + K * y */
    g_encoder.kalman.x[0] = g_encoder.kalman.temp3_data[0] + g_encoder.kalman.k[0] * y;
    g_encoder.kalman.x[1] = g_encoder.kalman.temp3_data[1] + g_encoder.kalman.k[1] * y;
    
    /* 步骤7: 更新协方差矩阵 P = (I - K * H) * P_pred (使用ARM DSP) */
    // 构建 (I - K * H) 矩阵
    float IKH_data[4] = {
        1.0f - g_encoder.kalman.k[0],  0.0f,
        -g_encoder.kalman.k[1],        1.0f
    };
    arm_matrix_instance_f32 IKH_matrix;
    arm_mat_init_f32(&IKH_matrix, 2, 2, IKH_data);
    
    arm_copy_f32((float32_t*)g_encoder.kalman.p, g_encoder.kalman.temp1_data, 4);
    
    // P = (I - K * H) * P_pred
    arm_mat_mult_f32(&IKH_matrix, &g_encoder.kalman.temp1_matrix, &g_encoder.kalman.P_matrix);
    
    return g_encoder.kalman.x[0];
}

/**
 * @brief 初始化编码器速度模块
 */
void EncoderSpeed_Init(void)
{
    /* 初始化编码器数据结构 */
    memset(&g_encoder, 0, sizeof(g_encoder));
    
    /* 初始化默认参数 */
    g_encoder.filter_type = FILTER_NONE;
    
    /* 初始化溢出检测系统 */
    g_encoder.angle.overflow_count = 0;
    g_encoder.angle.last_timer_count = TMR3->cval;  // 初始化为当前计数器值
    
    /* 初始化滤波器参数 */
    SetLowPassFilterCoeff(DEFAULT_FILTER_ALPHA);
    SetMovingAvgFilterSize(DEFAULT_FILTER_SIZE);
    
    /* 初始化卡尔曼滤波器 */
    InitKalmanFilter(DEFAULT_KALMAN_Q_VEL, DEFAULT_KALMAN_Q_ACC, DEFAULT_KALMAN_R, DEFAULT_KALMAN_P0);
    
    /* 初始化FHAN滤波器 */
    InitFhanFilter(DEFAULT_FHAN_R0, DEFAULT_FHAN_MAX_ACCEL);
    
    /* 初始化速度PI控制器 */
    InitSpeedPI(DEFAULT_SPEED_PI_KP, DEFAULT_SPEED_PI_KI, DEFAULT_SPEED_PI_MAX_INTEGRAL, DEFAULT_SPEED_PI_MAX_OUTPUT);
    
    /* 初始化Z相计数器 */
    g_encoder.z_pulse_counter = 0;
    
    /* 初始化上次角度值 */
    g_encoder.speed.last_mech_angle = 0.0f;
    g_encoder.speed.last_total_angle = 0.0f;
    
    /* 同步旋变位置和定时器计数：上电读取一次绝对位置 */
    CalculateTimerCountFromSPI(0);
}

/**
 * @brief 完整的速度环系统初始化
 * @param filter_type 速度滤波器类型选择
 * @param enable_fhan 是否启用FHAN路径规划 (1=启用, 0=禁用)
 * @param enable_feedforward 是否启用前馈补偿 (1=启用, 0=禁用)
 * @note 一次性完成所有速度环相关模块的初始化和配置
 */
void SpeedLoopSystem_Init(FilterType_t filter_type, uint8_t enable_fhan, uint8_t enable_feedforward)
{
    /* 1. 基础编码器模块初始化 */
    EncoderSpeed_Init();
    
    /* 2. 配置指定的滤波器类型 */
    SetSpeedFilterType(filter_type);
    
    /* 3. 根据滤波器类型设置合适的默认参数 */
    switch (filter_type) {
        case FILTER_LOWPASS:
            SetLowPassFilterCoeff(DEFAULT_FILTER_ALPHA);  // 默认低通滤波系数
            break;
        case FILTER_MOVING_AVG:
            SetMovingAvgFilterSize(DEFAULT_FILTER_SIZE);    // 默认8点滑动平均
            break;
        case FILTER_KALMAN:
            /* 卡尔曼滤波器已在EncoderSpeed_Init中初始化 */
            break;
        default:
            /* FILTER_NONE无需额外配置 */
            break;
    }
    
    /* 4. 配置FHAN路径规划 */
    if (enable_fhan) {
        EnableFhanFilter(1);  // 启用FHAN
    } else {
        EnableFhanFilter(0);  // 禁用FHAN
    }
    
    /* 5. 启用PI控制器 */
    EnableSpeedPI(1);
    
    /* 6. 配置前馈补偿 */
    if (enable_feedforward) {
        EnablePIFeedforward(1);  // 启用前馈
    } else {
        EnablePIFeedforward(0);  // 禁用前馈
    }
}

/**
 * @brief 重置编码器速度模块
 */
void EncoderSpeed_Reset(void)
{
    /* 停止定时器 */
    tmr_counter_enable(TMR3, FALSE);
    
    /* 重置角度和速度数据 */
    g_encoder.angle.total_mech_angle = 0.0f;
    
    g_encoder.speed.signed_speed_rps = 0.0f;
    g_encoder.speed.signed_speed_rpm = 0.0f;
    
    /* 重新启动定时器 */
    tmr_counter_enable(TMR3, TRUE);
    
    /* 重新初始化 */
    EncoderSpeed_Init();
}

/**
 * @brief 低通滤波
 * @param filter 低通滤波器结构体
 * @param input 输入值
 * @return 滤波后输出值
 */
static float ApplyLowpassFilter(LowPassFilter_t *filter, float input)
{
    /* 一阶低通滤波: y(n) = α * x(n) + (1-α) * y(n-1) */
    filter->output = filter->alpha * input + filter->one_minus_alpha * filter->last_output;
    filter->last_output = filter->output;
    return filter->output;
}

/**
 * @brief 滑动平均滤波
 * @param filter 滑动平均滤波器结构体
 * @param input 输入值
 * @return 滤波后输出值
 */
static float ApplyMovingAvgFilter(MovingAvgFilter_t *filter, float input)
{
    filter->sum -= filter->buffer[filter->index];
    filter->buffer[filter->index] = input;
    filter->sum += input;
    filter->index = (filter->index + 1) % filter->size;
    
    return filter->sum * filter->inv_size;
}

/**
 * @brief 计算角度差值
 * @param current_angle 当前角度
 * @param last_angle 上次角度
 * @return 角度差值（-π到π）
 */
static float CalculateAngleDifference(float current_angle, float last_angle)
{
    float diff = current_angle - last_angle;
    
    /* 处理跨越2π边界的情况 */
    if (diff > PI) {
        diff -= TWO_PI;
    } else if (diff < -PI) {
        diff += TWO_PI;
    }
    
    return diff;
}

/**
 * @brief  Z相脉冲检测中断回调函数(简化版)
 * @param  None
 * @retval None
 * @note   在外部Z相IO中断中调用，仅进行计数统计，不参与角度计算
 */
void ZPulseDetectedCallback(void)
{
    /* 独立Z相计数器递增 */
    g_encoder.z_pulse_counter++;
}

/**
 * @brief 获取Z相脉冲计数器值
 * @return Z相脉冲计数器值
 */
uint32_t GetZPulseCounter(void)
{
    return g_encoder.z_pulse_counter;
}

/**
 * @brief 重置Z相脉冲计数器
 */
void ResetZPulseCounter(void)
{
    g_encoder.z_pulse_counter = 0;
}

/**
 * @brief 重置溢出计数器
 */
void ResetOverflowCounter(void)
{
    g_encoder.angle.overflow_count = 0;
    g_encoder.angle.last_timer_count = TMR3->cval;
}

/**
 * @brief  获取电角度
 * @param  None
 * @retval 电角度值(0-2π)
 * @note   在20kHz电流环中断中调用，只负责电角度计算
 */
float GetElectricalAngle_ENC()
{
    //SimulateABZEncoder(100);模拟测试

    uint16_t tim_count = TMR3->cval;
    
    /* 计算并更新电角度 */
    uint16_t elec_position = tim_count & (COUNTS_PER_ELEC_REV - 1);
    float elec_angle = (float)elec_position * ANGLE_CONVERSION_FACTOR;
    g_encoder.angle.elec_angle = elec_angle;
    
    return elec_angle;
}

/**
 * @brief 获取带方向的转速(RPS)
 * @return 带方向的转速(rps)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPS(void)
{
    // return g_encoder.speed.signed_speed_rps;
    return signed_speed_rps;
}

/**
 * @brief 获取带方向的转速(RPM)
 * @return 带方向的转速(rpm)，正值=正转，负值=反转
 */
float GetSignedSpeed_RPM(void)
{
    return g_encoder.speed.signed_speed_rpm;
}

/**
 * @brief 设置速度滤波器类型
 * @param type 滤波器类型
 */
void SetSpeedFilterType(FilterType_t type)
{
    g_encoder.filter_type = type;
}

/**
 * @brief 设置低通滤波器系数
 * @param alpha 滤波系数(0-1)，值越小滤波效果越强
 */
void SetLowPassFilterCoeff(float alpha)
{
    if (alpha < 0.0f) alpha = 0.0f;
    if (alpha > 1.0f) alpha = 1.0f;
    
    g_encoder.lp_filter.alpha = alpha;
    g_encoder.lp_filter.one_minus_alpha = 1.0f - alpha;
}

/**
 * @brief 设置滑动平均滤波器大小
 * @param size 滤波器大小
 */
void SetMovingAvgFilterSize(uint16_t size)
{
    /* 限制滤波器大小不超过缓冲区容量 */
    if (size > sizeof(speed_buffer) / sizeof(speed_buffer[0])) {
        size = sizeof(speed_buffer) / sizeof(speed_buffer[0]);
    }
    if (size < 1) size = 1;
    
    /* 重置滤波器 */
    g_encoder.ma_filter.size = size;
    g_encoder.ma_filter.index = 0;
    g_encoder.ma_filter.sum = 0.0f;
    g_encoder.ma_filter.inv_size = 1.0f / (float)size;  // 预计算1/size，避免除法
    g_encoder.ma_filter.buffer = speed_buffer;           // 设置缓冲区指针
    
    /* 清空缓冲区 */
    for (uint16_t i = 0; i < size; i++) {
        speed_buffer[i] = 0.0f;
    }
}

/**
 * @brief 根据SPI读取的旋变位置计算并更新TIM3计数器值
 * @param resolver_pos 旋变绝对位置（如果为0则自动读取）
 * @return TIM3计数器值
 * @note 仅在初始化时使用，读取绝对位置并同步定时器计数器
 */
uint16_t CalculateTimerCountFromSPI(uint16_t resolver_pos)
{
    uint16_t dir;
    
    /* 如果没有提供位置参数，则从旋变读取绝对位置 */
    if (resolver_pos == 0) {
        resolver_pos = AD2S1210_CommRead();
        if (resolver_pos == 0xFFFF) {
            /* 读取失败，使用当前定时器值 */
            return TMR3->cval;
        }
    }
    
    /* 更新旋变绝对位置到结构体 */
    g_encoder.resolver_pos = resolver_pos;
    
    /* 计算相对于零位的电角度偏移 */
    if (resolver_pos > ALIGN_POSITION) {
        dir = resolver_pos - ALIGN_POSITION;
    } else {
        dir = resolver_pos + (4096 - ALIGN_POSITION);
    }
    
    /* 写入定时器计数器 */
    TMR3->cval = dir;
    
    /* 更新编码器数据结构 */
    g_encoder.angle.last_timer_count = dir;
    g_encoder.angle.overflow_count = 0;  // 重置溢出计数器

    /* 初始化角度数据 */
    g_encoder.angle.total_mech_angle = (float)dir * MECHANICAL_ANGLE_FACTOR;
    g_encoder.angle.mech_angle = (float)dir * MECHANICAL_ANGLE_FACTOR;
    g_encoder.angle.elec_angle = (float)(dir & (COUNTS_PER_ELEC_REV - 1)) * ANGLE_CONVERSION_FACTOR;
    
    /* 初始化速度历史数据 */
    g_encoder.speed.last_mech_angle = g_encoder.angle.mech_angle;
    g_encoder.speed.last_total_angle = g_encoder.angle.total_mech_angle;
    
    return dir;
}


/**
 * @brief 自适应卡尔曼参数切换
 * @param current_speed 当前速度(RPS)
 * @note 低速时使用稳定参数，高速时使用快响应参数
 */
static void AdaptiveKalmanParams(float current_speed)
{
    static uint8_t last_state = 0;  // 0=稳定态, 1=运行态
    uint8_t current_state;
    
    /* 根据速度判断当前状态 */
    float abs_speed = _fabsf(current_speed);
    if (abs_speed > SPEED_ACTIVE_THRESHOLD) {
        current_state = 1;  // 高速运行态
    } else if (abs_speed < SPEED_DEADBAND_RPS) {
        current_state = 0;  // 低速稳定态
    } else {
        current_state = last_state;  // 死区内保持上次状态，避免抖动
    }
    
    /* 状态切换时更新参数 */
    if (current_state != last_state) {
        if (current_state == 1) {
            /* 切换到运行态：快速响应 */
            SetKalmanFilterParams(KALMAN_Q_VEL_ACTIVE, KALMAN_Q_ACC_ACTIVE, 
                                KALMAN_R_ACTIVE, KALMAN_P0_ACTIVE);
        } else {
            /* 切换到稳定态：高精度 */
            SetKalmanFilterParams(KALMAN_Q_VEL_STABLE, KALMAN_Q_ACC_STABLE, 
                                KALMAN_R_STABLE, KALMAN_P0_STABLE);
        }
        last_state = current_state;
    }
}

/**
 * @brief 4KHz速度计算函数
 */
void CalculateSpeed_4KHz(void)
{
    /* 读取当前计数器值 */
    uint16_t current_count = TMR3->cval;
    
    /* 防止异常值 */
    if(current_count > TIM3_COUNTER_PERIOD) current_count = current_count % (TIM3_COUNTER_PERIOD + 1);
    
    /* 计算计数器差值 */
    int32_t count_diff = (int32_t)current_count - (int32_t)g_encoder.angle.last_timer_count;
    
    /* 溢出处理 */
    const int32_t MAX_COUNT = TIM3_COUNTER_PERIOD;
    const int32_t COUNT_RANGE = MAX_COUNT + 1;
    const int32_t HALF_RANGE = COUNT_RANGE / 2;
    
    if (count_diff >= HALF_RANGE) {
        count_diff -= COUNT_RANGE;
    } else if (count_diff <= -HALF_RANGE) {
        count_diff += COUNT_RANGE;
    }
    
    /* 计算原始差值用于调试 */
    static int32_t raw_diff = 0;
    raw_diff = (int32_t)current_count - (int32_t)g_encoder.angle.last_timer_count;
    
    /* 速度计算 */
    const float SPEED_CALC_COEFF_M = 1.534f;
    signed_speed_rps = (float)count_diff * SPEED_CALC_COEFF_M * RAD_TO_RPS;
    
    /* M法测速：速度 = 计数差值 * 转换系数 */
    /* 转换系数 = (2π/16384) / 0.00025 = 0.0003835 / 0.00025 = 1.534 rad/s per count */
    //SPEED_CALC_COEFF_M = 1.534f;  // (2π/16384) / 0.00025s
    //float raw_speed_rps = (float)count_diff * SPEED_CALC_COEFF_M * RAD_TO_RPS;  // 转换为RPS
    //signed_speed_rps=(float)count_diff * SPEED_CALC_COEFF_M * RAD_TO_RPS;
    signed_speed_rps=(float)count_diff*0.24414368f;
    /* 速度死区处理 */
    if (_fabsf(signed_speed_rps) < 0.1f) {
        signed_speed_rps = 0.0f;
    }
    
    /* 速度异常检测保护 */
    const float MAX_SPEED_LIMIT = 500.0f;
    if (_fabsf(signed_speed_rps) > MAX_SPEED_LIMIT) {
        signed_speed_rps = (signed_speed_rps > 0) ? MAX_SPEED_LIMIT : -MAX_SPEED_LIMIT;
    }
    
    /* 应用滤波器 */
    switch (g_encoder.filter_type) {
        case FILTER_LOWPASS:
            g_encoder.speed.signed_speed_rps = g_encoder.lp_filter.alpha * signed_speed_rps + 
                                        g_encoder.lp_filter.one_minus_alpha * g_encoder.lp_filter.last_output;
            g_encoder.lp_filter.last_output = g_encoder.speed.signed_speed_rps;
            break;
                    
        case FILTER_MOVING_AVG:
            g_encoder.speed.signed_speed_rps = ApplyMovingAvgFilter(&g_encoder.ma_filter, signed_speed_rps);
            break;
            
        case FILTER_KALMAN:
            AdaptiveKalmanParams(signed_speed_rps);
            g_encoder.speed.signed_speed_rps = UpdateKalmanFilter(signed_speed_rps);
            break;
                    
        default:
            g_encoder.speed.signed_speed_rps = signed_speed_rps;
            break;
    }
    
    /* 更新RPM */
    g_encoder.speed.signed_speed_rpm = g_encoder.speed.signed_speed_rps * RPS_TO_RPM;
    
    /* 更新历史值 */
    g_encoder.angle.last_timer_count = current_count;
    
    /* 调试数据输出 */
//    gMotorData.f4.custom_1 = g_encoder.speed.signed_speed_rps;
//    gMotorData.f4.custom_2 = signed_speed_rps;
//    gMotorData.f4.custom_3 = g_encoder.kalman.x[1];
//    gMotorData.f4.custom_4 = g_encoder.kalman.p[0][0];
}

/* FHAN路径规划 */

/**
 * @brief 符号函数
 * @param x 输入值
 * @return 1.0f(x>0), -1.0f(x<0), 0.0f(x=0)
 */
static float sign_func(float x)
{
    if (x > 0.0f) return 1.0f;
    else if (x < 0.0f) return -1.0f;
    else return 0.0f;
}

/**
 * @brief 带饱和特性的FHAN函数
 * @param x1 位置状态
 * @param x2 速度状态
 * @param v 目标输入
 * @param r0 速度因子
 * @param h0 步长
 * @param max_accel 最大加速度限制
 * @return 最优控制量
 * @note 基于韩京清的改进算法，增加饱和限制
 */
static float fhan_saturated(float x1, float x2, float v, float r0, float h0, float max_accel)
{
    /* 预计算常量 */
    const float h0_sq = h0 * h0;
    const float d = r0 * h0_sq;  // 切换函数的参数
    
    /* 状态误差 */
    float e1 = x1 - v;           // 位置误差
    float e2 = x2;               // 速度误差（目标速度变化率为0）
    
    /* 快速非线性跟踪函数 */
    float y = e1 + h0 * e2;      // 预测位置误差
    float a0 = _fabsf(y);         // |y|
    
    float u;
    if (a0 <= d) {
        /* 线性区域：使用线性逼近 */
        u = -r0 * (e1 + h0 * e2) / d;
    } else {
        /* 非线性区域：使用符号函数 */
        u = -r0 * sign_func(y);
    }
    
    /* 饱和限制 */
    if (u > max_accel) {
        u = max_accel;
    } else if (u < -max_accel) {
        u = -max_accel;
    }
    
    return u;
}

/**
 * @brief 初始化FHAN滤波器
 * @param r0 速度因子，建议值：根据系统惯量调整(50-200)
 * @param max_accel 最大加速度限制(RPS/s)
 * @note r0=100, max_accel=400 RPS/s
 */
void InitFhanFilter(float r0, float max_accel)
{
    g_encoder.fhan.x1 = 0.0f;               // 初始位置状态
    g_encoder.fhan.x2 = 0.0f;               // 初始速度状态
    g_encoder.fhan.r0 = r0;                 // 速度因子
    g_encoder.fhan.h0 = FHAN_DEFAULT_H0;    // 步长(250us)
    g_encoder.fhan.max_accel = max_accel;   // 最大加速度限制
    g_encoder.fhan.enabled = 0;             // 默认禁用，需要手动启用
    
    /* 预计算常量 */
    g_encoder.fhan.h0_sq = g_encoder.fhan.h0 * g_encoder.fhan.h0;
    g_encoder.fhan.r0_h0 = g_encoder.fhan.r0 * g_encoder.fhan.h0;
}

/**
 * @brief 设置FHAN参数
 * @param r0 速度因子
 * @param max_accel 最大加速度限制
 */
void SetFhanParams(float r0, float max_accel)
{
    g_encoder.fhan.r0 = r0;
    g_encoder.fhan.max_accel = max_accel;
    
    /* 重新计算预计算常量 */
    g_encoder.fhan.r0_h0 = g_encoder.fhan.r0 * g_encoder.fhan.h0;
}

/**
 * @brief 使能/禁用FHAN滤波器
 * @param enable 1=启用FHAN路径规划，0=直通滤波后速度
 */
void EnableFhanFilter(uint8_t enable)
{
    if (enable && !g_encoder.fhan.enabled) {
        /* 启用时，用当前速度初始化FHAN状态 */
        g_encoder.fhan.x1 = g_encoder.speed.signed_speed_rps;
        g_encoder.fhan.x2 = 0.0f;  // 初始加速度为0
    }
    g_encoder.fhan.enabled = enable;
}

/**
 * @brief 检查FHAN是否启用
 * @return 1=已启用，0=未启用
 */
uint8_t IsFhanEnabled(void)
{
    return g_encoder.fhan.enabled;
}

/**
 * @brief FHAN路径规划处理函数
 * @param target_speed 目标速度(RPS)，来自速度滤波器输出
 * @note 在CalculateSpeed_4KHz函数中调用，4KHz执行
 */
void ProcessFhan(float target_speed)
{
    if (!g_encoder.fhan.enabled) {
        /* 未启用时直通 */
        g_encoder.fhan.x1 = target_speed;
        g_encoder.fhan.x2 = 0.0f;
        return;
    }
    
    /* 执行FHAN算法 */
    float u = fhan_saturated(g_encoder.fhan.x1, g_encoder.fhan.x2, target_speed,
                           g_encoder.fhan.r0, g_encoder.fhan.h0, g_encoder.fhan.max_accel);
    
    /* 更新状态 */
    g_encoder.fhan.x1 += g_encoder.fhan.h0 * g_encoder.fhan.x2;  // 位置积分
    g_encoder.fhan.x2 += g_encoder.fhan.h0 * u;                 // 速度积分
}

/**
 * @brief 获取FHAN滤波后的速度指令(RPS)
 * @return FHAN输出的平滑速度指令，用于PI控制器
 */
float GetFhanSpeed_RPS(void)
{
    return g_encoder.fhan.x1;
}

/**
 * @brief 获取FHAN输出的加速度(RPS/s)
 * @return FHAN输出的加速度，可用于前馈补偿
 * @note 加速度前馈计算：I_ff = (J * accel) / Kt
 *       其中 J=8.113kg·m², Kt≈0.38Nm/A
 *       前馈电流 ≈ accel * 21.35 (A)
 */
float GetFhanAcceleration_RPS(void)
{
    return g_encoder.fhan.x2;
}

/* PI控制器 */

/**
 * @brief 初始化速度PI控制器
 * @param kp 比例增益
 * @param ki 积分增益
 * @param max_integral 积分限幅
 * @param max_output 输出限幅
 * @note 
 */
void InitSpeedPI(float kp, float ki, float max_integral, float max_output)
{
    g_encoder.speed_pi.kp = kp;
    g_encoder.speed_pi.ki = ki;                        
    g_encoder.speed_pi.ki_ts = ki * SPEED_LOOP_TIME;   // 预计算Ki_discrete
    g_encoder.speed_pi.integral_sum = 0.0f;            // 积分累加和 Σe(i)
    g_encoder.speed_pi.max_integral_sum = (g_encoder.speed_pi.ki_ts > 0.0001f) ? 
                                         (max_integral / g_encoder.speed_pi.ki_ts) : 
                                         1000000.0f;   // 预计算积分累加和限幅
    g_encoder.speed_pi.max_output = max_output;
    g_encoder.speed_pi.leak_rate = PI_INTEGRAL_LEAK_RATE;
    g_encoder.speed_pi.output = 0.0f;
    g_encoder.speed_pi.feedforward = 0.0f;
    g_encoder.speed_pi.total_output = 0.0f;
    g_encoder.speed_pi.enabled = 0;        
    g_encoder.speed_pi.reset_integral = 0;
    g_encoder.speed_pi.ff_enabled = 1;     
}

/**
 * @brief 设置速度PI控制器参数
 * @param kp 比例增益
 * @param ki 积分增益
 * @param max_integral 积分限幅
 * @param max_output 输出限幅
 */
void SetSpeedPIParams(float kp, float ki, float max_integral, float max_output)
{
    g_encoder.speed_pi.kp = kp;
    g_encoder.speed_pi.ki = ki;
    g_encoder.speed_pi.ki_ts = ki * SPEED_LOOP_TIME;  // 重新计算Ki*Ts
    g_encoder.speed_pi.max_integral_sum = (g_encoder.speed_pi.ki_ts > 0.0001f) ? 
                                         (max_integral / g_encoder.speed_pi.ki_ts) : 
                                         1000000.0f;
    g_encoder.speed_pi.max_output = max_output;
}

/**
 * @brief 使能/禁用速度PI控制器
 * @param enable 1=启用PI控制器，0=禁用
 */
void EnableSpeedPI(uint8_t enable)
{
    if (enable && !g_encoder.speed_pi.enabled) {
        /* 启用时清零积分项 */
        g_encoder.speed_pi.integral_sum = 0.0f;
        g_encoder.speed_pi.output = 0.0f;
    }
    g_encoder.speed_pi.enabled = enable;
}

/**
 * @brief 使能/禁用前馈补偿
 * @param enable 1=启用前馈，0=禁用前馈
 */
void EnablePIFeedforward(uint8_t enable)
{
    g_encoder.speed_pi.ff_enabled = enable;
}

/**
 * @brief 重置速度PI积分项
 */
void ResetSpeedPIIntegral(void)
{
    g_encoder.speed_pi.integral_sum = 0.0f;
    g_encoder.speed_pi.reset_integral = 1;
}

/**
 * @brief 检查速度PI是否启用
 * @return 1=已启用，0=未启用
 */
uint8_t IsSpeedPIEnabled(void)
{
    return g_encoder.speed_pi.enabled;
}

/**
 * @brief PI控制器处理
 * @param speed_target 目标速度(RPS)
 * @param speed_feedback 反馈速度(RPS)
 * @note 4KHz执行，优化的离散PI：u(k) = Kp×e(k) + Ki_ts×Σe(i)
 */
static void ProcessSpeedPI(float speed_target, float speed_feedback)
{
    if (!g_encoder.speed_pi.enabled) {
        g_encoder.speed_pi.output = 0.0f;
        g_encoder.speed_pi.total_output = 0.0f;
        g_encoder.speed_pi.current_command_safe = 0.0f;
        g_encoder.speed_pi.update_flag = 1;
        return;
    }
    
    /* 速度误差 */
    const float error = speed_target - speed_feedback;
    
    /* 积分重置检查 */
    if (g_encoder.speed_pi.reset_integral) {
        g_encoder.speed_pi.integral_sum = 0.0f;
        g_encoder.speed_pi.reset_integral = 0;
    } else {
        /* 积分更新：Σe(i) = leak_rate×Σe(i-1) + e(k) */
        g_encoder.speed_pi.integral_sum = g_encoder.speed_pi.integral_sum * g_encoder.speed_pi.leak_rate + error;
        
        /* 积分限幅 */
        const float max_sum = g_encoder.speed_pi.max_integral_sum;
        if (g_encoder.speed_pi.integral_sum > max_sum) {
            g_encoder.speed_pi.integral_sum = max_sum;
        } else if (g_encoder.speed_pi.integral_sum < -max_sum) {
            g_encoder.speed_pi.integral_sum = -max_sum;
        }
    }
    
    /* PI输出: u(k) = Kp×e(k) + Ki_ts×Σe(i) */
    g_encoder.speed_pi.output = g_encoder.speed_pi.kp * error + 
                               g_encoder.speed_pi.ki_ts * g_encoder.speed_pi.integral_sum;
    
    /* 前馈补偿 */
    g_encoder.speed_pi.feedforward = (g_encoder.speed_pi.ff_enabled && g_encoder.fhan.enabled) ? 
                                    (g_encoder.fhan.x2 * FEEDFORWARD_COEFF) : 0.0f;
    
    /* 总输出 */
    g_encoder.speed_pi.total_output = g_encoder.speed_pi.output + g_encoder.speed_pi.feedforward;
    
    /* 输出限幅（同时限制PI和总输出） */
    const float max_out = g_encoder.speed_pi.max_output;
    if (g_encoder.speed_pi.output > max_out) {
        g_encoder.speed_pi.output = max_out;
    } else if (g_encoder.speed_pi.output < -max_out) {
        g_encoder.speed_pi.output = -max_out;
    }
    
    if (g_encoder.speed_pi.total_output > max_out) {
        g_encoder.speed_pi.total_output = max_out;
    } else if (g_encoder.speed_pi.total_output < -max_out) {
        g_encoder.speed_pi.total_output = -max_out;
    }
    
    /* 更新电流指令安全副本 */
    g_encoder.speed_pi.current_command_safe = g_encoder.speed_pi.total_output;
    g_encoder.speed_pi.update_flag = 1;  // 标记已更新
}

/**
 * @brief 获取速度PI控制器输出(电流指令A)
 * @param speed_target 目标速度(RPS) - 可以来自FHAN输出或直接给定
 * @return PI控制器输出的电流指令(A)
 */
float GetSpeedPIOutput(float speed_target)
{
    /* 执行PI控制算法 */
    float speed_feedback;
    if (g_encoder.fhan.enabled) {
        /* 使用FHAN输出作为反馈 */
        speed_feedback = g_encoder.fhan.x1;
    } else {
        /* 直接使用滤波后的速度反馈 */
        speed_feedback = g_encoder.speed.signed_speed_rps;
    }
    
    ProcessSpeedPI(speed_target, speed_feedback);
    
    return g_encoder.speed_pi.output;
}

/*============================ 速度环中断回调函数 ==================*/

/**
 * @brief 获取安全的电流指令（供电流环读取）
 * @return 电流指令(A)，保证数据一致性
 * @note 在20KHz电流环中调用，自动清除更新标志
 */
float GetCurrentCommandSafe(void)
{
    /* 读取电流指令的同时自动清除更新标志 */
    g_encoder.speed_pi.update_flag = 0;
    
    /* 直接读取volatile变量 */
    return g_encoder.speed_pi.current_command_safe;
}

/**
 * @brief 仅获取电流指令，不影响更新标志
 * @return 电流指令(A)，保证数据一致性
 * @note 用于需要保持更新标志状态的场合
 */
float GetCurrentCommandValue(void)
{
    /* 直接读取volatile变量，不清除标志 */
    return g_encoder.speed_pi.current_command_safe;
}

/**
 * @brief 检查电流指令是否有更新
 * @return 1=有新更新，0=无更新
 * @note 检查更新状态，不清除标志
 */
uint8_t IsCurrentCommandUpdated(void)
{
    return g_encoder.speed_pi.update_flag;
}

/**
 * @brief 清除电流指令更新标志
 * @note 手动清除更新标志，用于自定义同步策略
 */
void ClearCurrentCommandFlag(void)
{
    g_encoder.speed_pi.update_flag = 0;
}


/* F4调试数据输出 */

/**
 * @brief 更新F4调试数据
 * @param target_speed_rps 目标速度(RPS)
 */
void UpdateF4CustomDataWithTarget(float target_speed_rps)
{
    MotorDataSendFrame(FRAME_F4);
}

/* 模拟ABZ编码器 */

/**
 * @brief 模拟ABZ编码器
 * @param target_speed_rps 目标速度(RPS)
 */
void SimulateABZEncoder(float target_speed_rps)
{
    static float current_sim_speed = 0.0f;
    static float accumulated_angle = 0.0f;
    static float harmonic_phase_accumulator = 0.0f;
    
    const float ACCEL_STEP = 5.0f;
    const float TIME_STEP = 0.00005f;
    const float SPEED_INCREMENT = ACCEL_STEP * TIME_STEP;
    const float HARMONIC_AMPLITUDE_RATIO = 0.01f;
    
    /* 速度渐变控制 */
    float speed_error = target_speed_rps - current_sim_speed;
    if (_fabsf(speed_error) > SPEED_INCREMENT) {
        if (speed_error > 0) {
            current_sim_speed += SPEED_INCREMENT;
        } else {
            current_sim_speed -= SPEED_INCREMENT;
        }
    } else {
        current_sim_speed = target_speed_rps;
    }
    
    /* 计算谐波震荡 */
    /* 基频 = 当前转速(机械频率), 相位累积: phase += 2π * frequency * time_step */
    float base_frequency = _fabsf(current_sim_speed);
    harmonic_phase_accumulator += TWO_PI * base_frequency * TIME_STEP;
    
    if (harmonic_phase_accumulator >= TWO_PI) {
        harmonic_phase_accumulator = fmodf(harmonic_phase_accumulator, TWO_PI);
    }
    if (harmonic_phase_accumulator < 0.0f) {
        harmonic_phase_accumulator += TWO_PI;
    }
    
    float harmonic_2x = arm_sin_f32(2.0f * harmonic_phase_accumulator);
    float harmonic_4x = arm_sin_f32(4.0f * harmonic_phase_accumulator);
    
    float harmonic_amplitude = _fabsf(current_sim_speed) * HARMONIC_AMPLITUDE_RATIO;
    float harmonic_speed = harmonic_amplitude * (harmonic_2x + harmonic_4x) * 0.5f;
    
    float actual_sim_speed = current_sim_speed + harmonic_speed;
    
    /* 角度积分 */
    /* 圈数 = 速度(RPS) * 时间(s), 角度 = 圈数 * 2π */
    static float accumulated_revolutions = 0.0f;
    accumulated_revolutions += actual_sim_speed * TIME_STEP;
    accumulated_angle = accumulated_revolutions * TWO_PI;
    
    /* 计算计数器值 */
    /* 机械角度转换为计数器值: count = angle * (16384 / 2π) */
    float angle_for_counter = fmodf(accumulated_angle, TWO_PI);
    if (angle_for_counter < 0.0f) {
        angle_for_counter += TWO_PI;
    }
    
    uint16_t sim_timer_count = (uint16_t)(angle_for_counter * COUNTS_PER_MECH_REV / TWO_PI);
    
    if (sim_timer_count > TIM3_COUNTER_PERIOD) {
        sim_timer_count = TIM3_COUNTER_PERIOD;
    }
    
    
    /* 更新TIM3计数器 */
    TMR3->cval = sim_timer_count;
}