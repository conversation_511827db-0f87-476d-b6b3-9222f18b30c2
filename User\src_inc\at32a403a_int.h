/* define to prevent recursive inclusion -------------------------------------*/
#ifndef __AT32A403A_INT_H
#define __AT32A403A_INT_H

#ifdef __cplusplus
extern "C" {
#endif

/* includes ------------------------------------------------------------------*/
#include "at32a403a.h"



/* exported functions ------------------------------------------------------- */
void NMI_Handler(void);
void HardFault_Handler(void);
void MemManage_Handler(void);
void BusFault_Handler(void);
void UsageFault_Handler(void);
void SVC_Handler(void);
void Debug<PERSON>on_Handler(void);
void Pend<PERSON>_Handler(void);

void SysTick_Handler(void);

void DMA1_Channel1_IRQ<PERSON><PERSON><PERSON>(void);
void ADC1_2_IRQHandler(void);
void TMR1_BRK_TMR9_IRQHandler(void);
void TMR1_OVF_TMR10_IRQHandler(void);
void TMR6_GLOBAL_IRQHandler(void);
void USBFS_MAPL_IRQHandler(void);
/* add user code begin exported functions */

/* add user code end exported functions */

#ifdef __cplusplus
}
#endif

#endif
