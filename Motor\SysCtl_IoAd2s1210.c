//=============================================================================
// Filename：Resolver.c
//
// Description:	Sub function of AD2S1210 Control
//
// Current Version：1.0
//
// Target: AYJD-2014-101 Ver:1.0
//
//-----------------------------------------------------------------------------
// Revision History: 1.0
//-----------------------------------------------------------------------------
// Author: VC
// $Release Date: 2014.04.03
//-----------------------------------------------------------------------------
// Copyright (c) 2012-2014
// TianJin ANYI Mechanical & Electrical Technology Development Co.,Ltd.
// All Rights Reserved.
//=============================================================================
// Include header files used in the Resolver function


#include "at32a403a.h"
#include "SysCtl_IoAd2s1210.h"
#include "delay.h"

#define MAXTIME 50000
uint16_t Status = 0;
// int Status1 = 0;
RESOLVER Resolver = RESOLVER_DEFAULTS;

/**
  * @brief SPI 通讯故障
  * @version 0.1
  */
void Ad2s_CommunicationFault()
{
    uint8_t fault;
    fault = Ad2s_GetFaultGeneralMode();
}


/**
  * @brief  AD2S1210初始化
  * @version 0.1
  */
void Ad2s_InitDecoder(void)
{
    Ad2s_ENABLE();
    Ad2s_CONFIGMODE();
    delay_us(70);
    Ad2s_SampleSet();
    //=========================================================
    // Reset timing control
    //=========================================================
    delay_us(20);
    Status = Ad2s_DecoderRead(0x88);
    //=========================================================
    // Clear fault register
    //=========================================================
    Ad2s_SampleClear();
    delay_us(1);
    Ad2s_SampleSet();
    Resolver.Fault = Ad2s_DecoderRead(FAULTREG);
    //=========================================================
    // Write to Control register
    //=========================================================
    Ad2s_DecoderWrite(CONTROL, 0x7A);
    delay_us(1);
    Status = Ad2s_DecoderRead(CONTROL);
    //=========================================================
    // Clear fault register again
    //=========================================================
    Ad2s_SampleClear();
    delay_us(1);
    Ad2s_SampleSet();
    delay_us(1);
    Ad2s_SampleClear();
    delay_us(1);
    Ad2s_SampleSet();
    Resolver.Fault = Ad2s_DecoderRead(FAULTREG);
    //=========================================================
    // Read position register
    //=========================================================
    Resolver.Angle = Ad2s_GetPosition();
    Ad2s_AngleCompensation();
    Resolver.AnglePre = Resolver.AngleOut;
    Ad2s_DISABLE();
}

/**
  * @brief AD2S1210写数据函数
  * @param  add: 写入寄存器地址
  * @param  data: 写入数据
  * @version 0.1
  */
void Ad2s_DecoderWrite(uint16_t add, uint16_t data)
{
    uint16_t timeout = 0;
    Ad2s_WRClear();
    spi_i2s_data_transmit(SPI2, add); // SPI发送数据，SPIx,x可根据硬件配置修改
    while (spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET)
    {
        timeout++;
        if (timeout >= MAXTIME)
        {
            Ad2s_CommunicationFault();
            timeout = 0;
            break;
        }
    }
    timeout = 0;
    spi_i2s_data_receive(SPI2);
    Ad2s_WRSet();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    Ad2s_WRClear();
    spi_i2s_data_transmit(SPI2, data); // SPI发送数据，SPIx,x可根据硬件配置修改
    while (spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET)
    {
         timeout++;
        if (timeout >= MAXTIME)
        {
            Ad2s_CommunicationFault();
            timeout = 0;
            break;
        }
    }
    timeout = 0;
    spi_i2s_data_receive(SPI2);
    Ad2s_WRSet();
}

/**
  * @brief AD2S1210读数据函数
  * @param  add: 读取寄存器地址
  * @return uint16_t 
  * @version 0.1
  */
uint16_t Ad2s_DecoderRead(uint16_t add)
{
    uint16_t data;
    uint16_t timeout = 0;
    Ad2s_WRClear();
    while (spi_i2s_flag_get(SPI2, SPI_I2S_TDBE_FLAG) == RESET)
    {
        timeout++;
        if (timeout >= MAXTIME)
        {
            Ad2s_CommunicationFault();
            timeout = 0;
            break;
        }
    }
    timeout = 0;
    spi_i2s_data_transmit(SPI2, add); // SPI发送数据，SPIx,x可根据硬件配置修改
    while (spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET)
    {
        timeout++;
        if (timeout >= MAXTIME)
        {
            Ad2s_CommunicationFault();
            timeout = 0;
            break;
        }
    }
    timeout = 0;
    data = spi_i2s_data_receive(SPI2); // SPI接收数据，SPIx,x-可根据硬件配置修改
    Ad2s_WRSet();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    Ad2s_WRClear();
    spi_i2s_data_transmit(SPI2, add); // SPI发送数据，SPIx,x-可根据硬件配置修改
    while (spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET)
    {
        timeout++;
        if (timeout >= MAXTIME)
        {
            Ad2s_CommunicationFault();
            timeout = 0;
            break;
        }
    }
    timeout = 0;
    data = spi_i2s_data_receive(SPI2); // SPI接收数据，SPIx,x-可根据硬件配置修改
    Ad2s_WRSet();
    return data;
}

/**
  * @brief 常规模式读取函数
  * @param  add: 读取寄存器地址
  * @return uint16_t 
  * @version 0.1
  */
uint16_t Ad2s_DecoderReadGeneralMode(uint16_t add)
{
    uint16_t data;
    uint16_t timeout = 0;
    Ad2s_WRClear();
    while (spi_i2s_flag_get(SPI2, SPI_I2S_TDBE_FLAG) == RESET)
    {
        timeout++;
        if (timeout >= MAXTIME)
        {
            Ad2s_CommunicationFault();
            timeout = 0;
            break;
        }
    }
    timeout = 0;
    spi_i2s_data_transmit(SPI2, add); // SPI发送数据，SPIx,x可根据硬件配置修改
    while (spi_i2s_flag_get(SPI2, SPI_I2S_RDBF_FLAG) == RESET)
    {
        timeout++;
        if (timeout >= MAXTIME)
        {
            Ad2s_CommunicationFault();
            timeout = 0;
            break;
        }
    }
    timeout = 0;
    data = spi_i2s_data_receive(SPI2); // SPI接收数据，SPIx,x-可根据硬件配置修改
    Ad2s_WRSet();
    return data;
}

/**
  * @brief AD2S1210配置模式读取位置函数
  * @return uint16_t 
  * @version 0.1
  */
uint16_t Ad2s_GetPosition()
{
    uint16_t PositionMSBValue = 0x00;
    uint16_t PositionLSBValue = 0x00;

    Ad2s_SampleClear();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    PositionMSBValue = Ad2s_DecoderReadGeneralMode(POSITIONMSB) << 4;
    PositionLSBValue = Ad2s_DecoderReadGeneralMode(POSITIONLSB) >> 4;
    Ad2s_SampleSet();
    return PositionMSBValue + PositionLSBValue;
}

/**
  * @brief AD2S1210普通模式读取位置函数
  * @return uint16_t 
  * @version 0.1
  */
uint16_t Ad2s_GetAngle()
{
    uint16_t PositionMSBValue = 0x00;
    uint16_t PositionLSBValue = 0x00;
    
    Ad2s_SampleClear();
    __NOP();
    __NOP();
    __NOP();
    __NOP();
    Ad2s_POSTIONGMODE();
    Ad2s_ENABLE();
    PositionMSBValue = Ad2s_DecoderReadGeneralMode(POSITIONMSB) << 4;
    PositionLSBValue = Ad2s_DecoderReadGeneralMode(POSITIONLSB) >> 4;
    Ad2s_SampleSet();
    Ad2s_DISABLE();
    return (PositionMSBValue + PositionLSBValue);
}

/**
  * @brief AD2S1210读取速度函数
  * @return uint16_t 
  * @version 0.1
  */
uint16_t Ad2s_GetVelocity()
{
    uint16_t VelocityMSBValue = 0x00;
    uint16_t VelocityLSBValue = 0x00;
    
    Ad2s_SampleClear();
	__NOP();
    __NOP();
    __NOP();
    __NOP();
    Ad2s_ENABLE();
    Ad2s_VELOCITYGMODE();
    VelocityMSBValue = Ad2s_DecoderReadGeneralMode(VELOCITYMSB) << 4;
    VelocityLSBValue = Ad2s_DecoderReadGeneralMode(VELOCITYLSB) >> 4;
    Ad2s_SampleSet();
    Ad2s_DISABLE();
    return VelocityMSBValue + VelocityLSBValue;
}

/**
  * @brief 常规模式读取故障
  * @return uint8_t 
  * @version 0.1
  */
uint8_t  Ad2s_GetFaultGeneralMode()
{
    uint8_t data;
     Ad2s_SampleClear();
	__NOP();
    __NOP();
    __NOP();
    __NOP();
    Ad2s_ENABLE();
    Ad2s_VELOCITYGMODE();
    data = Ad2s_DecoderReadGeneralMode(FAULTREG);
    Ad2s_SampleSet();
    Ad2s_DISABLE();
    return data;
}

/**
  * @brief 角度补偿
  * @version 0.1
  */
void Ad2s_AngleCompensation(void)
{
    uint16_t temp, Angle_temp, Angle_temp2;
    temp = Resolver.MotorPairs / Resolver.ResolverPairs;
    Angle_temp = Resolver.Angle * temp;

    if (Angle_temp > 4095)
    {
        Angle_temp2 = Angle_temp - 4095;
    }
    else
    {
        Angle_temp2 = Angle_temp;
    }

    if (Angle_temp2 + Resolver.AngleOffset > 4095)
    {
        Resolver.AngleOut = Angle_temp2 + Resolver.AngleOffset - 4095;
    }
    else
    {
        Resolver.AngleOut = Angle_temp2 + Resolver.AngleOffset;
    }
}

/**
  * @brief 角度滤波
  * @attention  旋变的角度变化方有可能和速度的正负方向不一致；
  * @version 0.1
  */
void Ad2s_AngleFilter(void)
{
    /*    Resolver.FromSpeedDeltAg=0.000008725*Resolver.ResolverPairs*fabs(speed1.EstimatedSpeedRpm);
        Resolver.FromSpeedDeltAgdata=(uint16_t)(651.898*Resolver.FromSpeedDeltAg);
      if(Resolver.FirstFlag==1)
      {
        if((pwmbldc.Direction == 0)&&(fabs(speed1.EstimatedSpeedRpm)>10))                                   //the qifa2 motor speed and angle direction are not consistent.The angle decreases, but the speed is positive.
        {
           if((Resolver.AnglePre - Resolver.Angle)> 0)
           {
               Resolver.AngleDelta=Resolver.AnglePre - Resolver.Angle;
           }
           else
           {
               Resolver.AngleDelta=4096+Resolver.AnglePre - Resolver.Angle;
           }

           if((Resolver.AngleDelta>= 175)&&(Resolver.AngleDelta < 2048))     //Angle error cann't exceed 15 electrical angle;175*360/4096
           {
               Resolver.AgErrCount++;
              if (Resolver.AnglePre-Resolver.FromSpeedDeltAgdata<=0)
               {
                   Resolver.Angle = 4096+Resolver.AnglePre-Resolver.FromSpeedDeltAgdata;
               }
               else
               {
                   Resolver.Angle = Resolver.AnglePre-Resolver.FromSpeedDeltAgdata;
               }

               Resolver.AnglePre = Resolver.Angle;
           }
           else
           {
            Resolver.AnglePre = Resolver.Angle;
           }
        }
        else
        {
         Resolver.AnglePre = Resolver.Angle;
        }
      }
      else
      {
        Resolver.AnglePre = Resolver.Angle;
        Resolver.FirstFlag=1;
      }*/
}

/**
  * @brief 转速计算
  * @version 0.1
  */
void Ad2s_CalculateSpeed(void)
{
    int i;

    Resolver.Speed = Ad2s_GetVelocity();

    if (Resolver.Speed >= 2048)
    {
        Resolver.Speed = -(4095 - Resolver.Speed) * 0.488;
    }
    else
    {
        Resolver.Speed = Resolver.Speed * 0.488;
    }

    Resolver.SpeedFilterBuf[Resolver.SpeedFilterBufPointer++] = Resolver.Speed;
    if (Resolver.SpeedFilterBufPointer >= 8)
    {
        Resolver.SpeedSum = 0;
        Resolver.SpeedFilterBufPointer = 0;
        for (i = 0; i < 8; i++)
    {
        Resolver.SpeedSum += Resolver.SpeedFilterBuf[i];
    }
    Resolver.SpeedOut = Resolver.SpeedSum * 0.125 * 60 / 5;
    }
    
}

//===========================================================================
// End of file.
//===========================================================================
