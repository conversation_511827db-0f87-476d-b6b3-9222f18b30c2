/**
 * @file protection_monitor.c
 * @brief 电机控制器保护监控模块实现
 * @version 1.0
 * @date 2025-07-23
 * 
 * @description
 * 本模块实现了电机控制器的关键参数保护监控功能，包括：
 * - 母线电压/电流保护
 * - 温度保护
 * - 电流不平衡保护
 * - 堵转和超速保护
 * - 基于时间的多级保护判断
 */

#include "protection_monitor.h"
#include "sensor_proc.h"
#include "adc_pmsm.h"
#include "Sys_TimerEvent.h"
#include "system_status.h"
#include <math.h>
#include <string.h>

/* ======================== 内部数据结构定义 ======================== */

/**
 * @brief 单一保护等级规则结构（支持固定和动态延时）
 */
typedef struct {
    float           threshold;                      // 保护阈值
    uint32_t        fixed_delay_ms;                 // 固定延时时间（毫秒），为0时使用动态延时
    dynamic_delay_func_t get_dynamic_delay_ms_func; // 动态延时计算函数指针，NULL时使用固定延时
    fault_code_t    fault_code;                     // 触发后的故障码
    protect_level_t level;                          // 保护等级
} protection_rule_t;

/**
 * @brief 监控参数配置结构
 */
typedef struct {
    protect_param_id_t      param_id;           // 参数ID
    param_getter_func_t     getter_func;        // 参数值获取函数
    const protection_rule_t *rules;             // 保护规则数组
    uint8_t                 rule_count;         // 保护规则数量
    uint8_t                 compare_greater;    // 1: 大于阈值触发, 0: 小于阈值触发
} param_config_t;

/**
 * @brief 保护运行时状态结构
 */
typedef struct {
    protect_status_t    status;             // 当前保护状态
    uint32_t            trigger_time;       // 开始越限的时间戳
    float               current_value;      // 当前参数值
    uint8_t             active_rule_index;  // 当前激活的规则索引
    uint32_t            trigger_count;      // 触发次数统计
} protection_state_t;

/* ======================== 参数获取函数声明 ======================== */
static float get_bus_voltage(void);
static float get_bus_current(void);
static float get_motor_temperature(void);
static float get_driver_temperature(void);
static float get_current_imbalance(void);
static float get_stall_detection(void);
static float get_motor_speed(void);

// 堵转保护动态延时函数声明
static uint32_t get_stall_dynamic_delay_warn(float current_value);
static uint32_t get_stall_dynamic_delay_fault(float current_value);

/* ======================== 保护规则配置表 ======================== */

// 母线过压保护规则
static const protection_rule_t bus_overvolt_rules[] = {
    {320.0f, 5000,  NULL, FAULT_CODE_BUS_OVERVOLT_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 320V, 5s
    {350.0f, 1000,  NULL, FAULT_CODE_BUS_OVERVOLT_FAULT, PROTECT_LEVEL_FAULT}    // 故障: 350V, 1s
};
// 母线欠压保护规则
static const protection_rule_t bus_undervolt_rules[] = {
    {200.0f, 3000,  NULL, FAULT_CODE_BUS_UNDERVOLT_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 200V, 3s
    {180.0f, 1000,  NULL, FAULT_CODE_BUS_UNDERVOLT_FAULT, PROTECT_LEVEL_FAULT}    // 故障: 180V, 1s
};
// 母线过流保护规则
static const protection_rule_t bus_overcurrent_rules[] = {
    {4.2f,   5000,  NULL, FAULT_CODE_BUS_OVERCURRENT_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 4A, 5s
    {6.0f,   1000,  NULL, FAULT_CODE_BUS_OVERCURRENT_FAULT, PROTECT_LEVEL_FAULT}    // 故障: 9A, 1s
};
// 电机温度保护规则
static const protection_rule_t motor_temp_rules[] = {
    {100.0f,  10000, NULL, FAULT_CODE_MOTOR_TEMP_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 80°C, 10s
    {120.0f, 5000,  NULL, FAULT_CODE_MOTOR_TEMP_FAULT, PROTECT_LEVEL_FAULT}     // 故障: 100°C, 5s
};
// 驱动模块温度保护规则
static const protection_rule_t driver_temp_rules[] = {
    {90.0f,  10000, NULL, FAULT_CODE_DRIVER_TEMP_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 90°C, 10s
    {105.0f,  2000,  NULL, FAULT_CODE_DRIVER_TEMP_FAULT, PROTECT_LEVEL_FAULT}    // 故障: 105°C, 2s
};
// 三相电流不平衡保护规则
static const protection_rule_t current_imbalance_rules[] = {
    {20.0f,  5000,  NULL, FAULT_CODE_CURRENT_IMBALANCE_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 20%, 5s
    {30.0f,  2000,  NULL, FAULT_CODE_CURRENT_IMBALANCE_FAULT, PROTECT_LEVEL_FAULT}    // 故障: 30%, 2s
};
// 堵转保护规则（动态延时）
static const protection_rule_t stall_rules[] = {
    {0.5f, 0, get_stall_dynamic_delay_warn,  FAULT_CODE_STALL_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 堵转, 动态延时
    {0.5f, 0, get_stall_dynamic_delay_fault, FAULT_CODE_STALL_FAULT, PROTECT_LEVEL_FAULT}    // 故障: 堵转, 动态延时
};
// 超速保护规则
static const protection_rule_t overspeed_rules[] = {
    {12100.0f, 2000, NULL, FAULT_CODE_OVERSPEED_WARN,  PROTECT_LEVEL_WARNING}, // 警告: 12100, 2s
    {13500.0f, 500,  NULL, FAULT_CODE_OVERSPEED_FAULT, PROTECT_LEVEL_FAULT}     // 故障: 13500, 0.5s
};

/* ======================== 监控参数配置总表 ======================== */
static const param_config_t param_configs[PROTECT_PARAM_COUNT] = {
    // 母线过压 - 大于阈值触发
    {
        .param_id = PROTECT_PARAM_BUS_OVERVOLT,
        .getter_func = get_bus_voltage,
        .rules = bus_overvolt_rules,
        .rule_count = sizeof(bus_overvolt_rules) / sizeof(protection_rule_t),
        .compare_greater = 1
    },
    
    // 母线欠压 - 小于阈值触发
    {
        .param_id = PROTECT_PARAM_BUS_UNDERVOLT,
        .getter_func = get_bus_voltage,
        .rules = bus_undervolt_rules,
        .rule_count = sizeof(bus_undervolt_rules) / sizeof(protection_rule_t),
        .compare_greater = 0
    },
    
    // 母线过流 - 大于阈值触发
    {
        .param_id = PROTECT_PARAM_BUS_OVERCURRENT,
        .getter_func = get_bus_current,
        .rules = bus_overcurrent_rules,
        .rule_count = sizeof(bus_overcurrent_rules) / sizeof(protection_rule_t),
        .compare_greater = 1
    },
    
    // 电机温度 - 大于阈值触发
    {
        .param_id = PROTECT_PARAM_MOTOR_TEMP,
        .getter_func = get_motor_temperature,
        .rules = motor_temp_rules,
        .rule_count = sizeof(motor_temp_rules) / sizeof(protection_rule_t),
        .compare_greater = 1
    },
    
    // 驱动模块温度 - 大于阈值触发
    {
        .param_id = PROTECT_PARAM_DRIVER_TEMP,
        .getter_func = get_driver_temperature,
        .rules = driver_temp_rules,
        .rule_count = sizeof(driver_temp_rules) / sizeof(protection_rule_t),
        .compare_greater = 1
    },
    
    // 三相电流不平衡 - 大于阈值触发
    {
        .param_id = PROTECT_PARAM_CURRENT_IMBALANCE,
        .getter_func = get_current_imbalance,
        .rules = current_imbalance_rules,
        .rule_count = sizeof(current_imbalance_rules) / sizeof(protection_rule_t),
        .compare_greater = 1
    },

    // 堵转保护 - 大于阈值触发（动态延时）
    {
        .param_id = PROTECT_PARAM_STALL,
        .getter_func = get_stall_detection,
        .rules = stall_rules,
        .rule_count = sizeof(stall_rules) / sizeof(protection_rule_t),
        .compare_greater = 1
    },

    // 超速保护 - 大于阈值触发
    {
        .param_id = PROTECT_PARAM_OVERSPEED,
        .getter_func = get_motor_speed,
        .rules = overspeed_rules,
        .rule_count = sizeof(overspeed_rules) / sizeof(protection_rule_t),
        .compare_greater = 1
    }
};

/* ======================== 模块内部变量 ======================== */
static protection_state_t protection_states[PROTECT_PARAM_COUNT];  // 运行时状态数组
static protection_event_func_t event_handler = NULL;               // 事件处理函数指针
static uint8_t module_initialized = 0;                             // 模块初始化标志
static uint32_t total_trigger_count = 0;                           // 总触发次数统计

/* ======================== 故障记录相关变量 ======================== */
static fault_record_t fault_records[FAULT_RECORD_MAX_COUNT];       // 故障记录数组
static uint8_t fault_record_count = 0;                             // 当前故障记录数量
static uint8_t fault_record_index = 0;                             // 下一个写入位置索引

/* ======================== 内部函数声明 ======================== */
static uint32_t get_system_time_ms(void);
static uint8_t check_threshold_violation(float value, float threshold, uint8_t compare_greater);
static void process_protection_logic(uint8_t param_index);
static void trigger_protection_event(uint8_t param_index, uint8_t rule_index);
static void add_fault_record(fault_code_t fault_code, protect_param_id_t param_id,
                           protect_level_t level, float trigger_value, uint32_t trigger_time);

/* ======================== 公开API函数实现 ======================== */

/**
 * @brief 初始化保护监控模块
 */
int Protection_Monitor_Init(protection_event_func_t event_handler_func)
{
    if (event_handler_func == NULL) {
        return -1;
    }

    // 保存事件处理函数指针
    event_handler = event_handler_func;

    // 初始化所有保护状态
    for (int i = 0; i < PROTECT_PARAM_COUNT; i++) {
        protection_states[i].status = PROTECT_STATUS_NORMAL;
        protection_states[i].trigger_time = 0;
        protection_states[i].current_value = 0.0f;
        protection_states[i].active_rule_index = 0;
        protection_states[i].trigger_count = 0;
    }

    // 重置统计信息
    total_trigger_count = 0;

    // 初始化故障记录
    fault_record_count = 0;
    fault_record_index = 0;
    memset(fault_records, 0, sizeof(fault_records));

    module_initialized = 1;
    return 0;
}

/**
 * @brief 保护监控周期性任务
 */
void Protection_Monitor_Tick(void)
{
    if (!module_initialized) {
        return;
    }

    // 遍历所有监控参数
    for (int i = 0; i < PROTECT_PARAM_COUNT; i++) {
        process_protection_logic(i);
    }
}

/**
 * @brief 获取指定参数的保护状态
 */
protect_status_t Protection_Monitor_GetStatus(protect_param_id_t param_id)
{
    if (param_id >= PROTECT_PARAM_COUNT || !module_initialized) {
        return PROTECT_STATUS_NORMAL;
    }

    return protection_states[param_id].status;
}

/**
 * @brief 重置指定参数的保护状态
 */
void Protection_Monitor_Reset(protect_param_id_t param_id)
{
    if (param_id >= PROTECT_PARAM_COUNT || !module_initialized) {
        return;
    }

    protection_states[param_id].status = PROTECT_STATUS_NORMAL;
    protection_states[param_id].trigger_time = 0;
    protection_states[param_id].active_rule_index = 0;
}

/**
 * @brief 重置所有保护状态
 */
void Protection_Monitor_ResetAll(void)
{
    if (!module_initialized) {
        return;
    }

    for (int i = 0; i < PROTECT_PARAM_COUNT; i++) {
        Protection_Monitor_Reset((protect_param_id_t)i);
    }
}

/**
 * @brief 获取指定参数的当前值
 */
sys_status_t Protection_Monitor_GetValue(protect_param_id_t param_id, float *value)
{
    if (param_id >= PROTECT_PARAM_COUNT || value == NULL) {
        return SYS_STATUS_INVALID_PARAM;
    }

    if (!module_initialized) {
        return SYS_STATUS_NOT_INITIALIZED;
    }

    *value = protection_states[param_id].current_value;
    return SYS_STATUS_OK;
}

/**
 * @brief 获取模块运行统计信息
 */
void Protection_Monitor_GetStats(uint32_t *total_triggers, uint8_t *active_protections)
{
    if (!module_initialized) {
        if (total_triggers) *total_triggers = 0;
        if (active_protections) *active_protections = 0;
        return;
    }

    if (total_triggers) {
        *total_triggers = total_trigger_count;
    }

    if (active_protections) {
        uint8_t active_count = 0;
        for (int i = 0; i < PROTECT_PARAM_COUNT; i++) {
            if (protection_states[i].status != PROTECT_STATUS_NORMAL) {
                active_count++;
            }
        }
        *active_protections = active_count;
    }
}

/* ======================== 内部函数实现 ======================== */

/**
 * @brief 获取系统时间（毫秒）
 */
static uint32_t get_system_time_ms(void)
{
    // 使用系统定时器事件模块提供的毫秒级时间戳
    return Get_Runtime_Ms();
}

/**
 * @brief 检查阈值违规
 */
static uint8_t check_threshold_violation(float value, float threshold, uint8_t compare_greater)
{
    if (compare_greater) {
        return (value > threshold) ? 1 : 0;
    } else {
        return (value < threshold) ? 1 : 0;
    }
}

/**
 * @brief 处理单个参数的保护逻辑
 */
static void process_protection_logic(uint8_t param_index)
{
    const param_config_t *config = &param_configs[param_index];
    protection_state_t *state = &protection_states[param_index];

    // 获取当前参数值
    if (config->getter_func != NULL) {
        state->current_value = config->getter_func();
    } else {
        state->current_value = 0.0f;
        return;
    }

    // 检查是否为有效值
    if (isnan(state->current_value)) {
        return;
    }

    uint32_t current_time = get_system_time_ms();
    uint8_t violation_detected = 0;
    uint8_t triggered_rule_index = 0;

    // 从最严重的等级开始检查（故障级别优先于警告级别）
    for (int i = config->rule_count - 1; i >= 0; i--) {
        const protection_rule_t *rule = &config->rules[i];

        if (check_threshold_violation(state->current_value, rule->threshold, config->compare_greater)) {
            violation_detected = 1;
            triggered_rule_index = i;
            break; // 找到第一个（最严重的）违规规则就退出
        }
    }

    // 根据当前状态和检测结果进行状态转换
    switch (state->status) {
        case PROTECT_STATUS_NORMAL:
            if (violation_detected) {
                // 开始监控
                state->status = PROTECT_STATUS_MONITORING;
                state->trigger_time = current_time;
                state->active_rule_index = triggered_rule_index;
            }
            break;

        case PROTECT_STATUS_MONITORING:
            if (!violation_detected) {
                // 恢复正常
                state->status = PROTECT_STATUS_NORMAL;
                state->trigger_time = 0;
            } else {
                // 检查是否需要更新规则（更严重的规则）
                if (triggered_rule_index > state->active_rule_index) {
                    state->active_rule_index = triggered_rule_index;
                    state->trigger_time = current_time; // 重新开始计时
                }

                // 检查延时是否到达
                const protection_rule_t *active_rule = &config->rules[state->active_rule_index];
                uint32_t required_delay;

                // 判断使用固定延时还是动态延时
                if (active_rule->get_dynamic_delay_ms_func != NULL) {
                    // 使用动态延时
                    required_delay = active_rule->get_dynamic_delay_ms_func(state->current_value);
                } else {
                    // 使用固定延时
                    required_delay = active_rule->fixed_delay_ms;
                }

                if ((current_time - state->trigger_time) >= required_delay) {
                    // 触发保护
                    trigger_protection_event(param_index, state->active_rule_index);
                    state->status = PROTECT_STATUS_TRIGGERED;
                }
            }
            break;

        case PROTECT_STATUS_TRIGGERED:
            // 已触发状态，需要手动重置
            break;

        default:
            state->status = PROTECT_STATUS_NORMAL;
            break;
    }
}

/**
 * @brief 触发保护事件
 */
static void trigger_protection_event(uint8_t param_index, uint8_t rule_index)
{
    const param_config_t *config = &param_configs[param_index];
    protection_state_t *state = &protection_states[param_index];
    const protection_rule_t *rule = &config->rules[rule_index];
    uint32_t current_time = get_system_time_ms();

    // 更新统计信息
    state->trigger_count++;
    total_trigger_count++;

    // 添加故障记录
    add_fault_record(rule->fault_code, config->param_id, rule->level,
                    state->current_value, current_time);

    // 发布保护事件
    if (event_handler != NULL) {
        event_handler(rule->fault_code, config->param_id, rule->level, state->current_value);
    }
}

/* ======================== 参数获取函数实现 ======================== */

/**
 * @brief 获取母线电压
 */
static float get_bus_voltage(void)
{
    float voltage;
    if (sensor_get_value(SENSOR_U_DC, &voltage) == SYS_STATUS_OK) {
        return voltage;
    }
    return 0.0f; // 默认值，表示获取失败
}

/**
 * @brief 获取母线电流
 */
static float get_bus_current(void)
{
    // TODO: 实现母线电流获取
    // 这里需要根据实际的电流采样方式实现
    return 0.0f; // 占位符
}

/**
 * @brief 获取电机温度
 */
static float get_motor_temperature(void)
{
    float temp0, temp1;
    sys_status_t status0 = sensor_get_value(SENSOR_M_PT0, &temp0);
    sys_status_t status1 = sensor_get_value(SENSOR_M_PT1, &temp1);

    // 如果两个传感器都失败，返回默认值
    if (status0 != SYS_STATUS_OK && status1 != SYS_STATUS_OK) {
        return 25.0f; // 默认室温
    } else if (status0 != SYS_STATUS_OK) {
        return temp1; // 只有传感器1有效
    } else if (status1 != SYS_STATUS_OK) {
        return temp0; // 只有传感器0有效
    } else {
        return (temp0 > temp1) ? temp0 : temp1; // 返回较高的温度
    }
}

/**
 * @brief 获取驱动模块温度
 */
static float get_driver_temperature(void)
{
    float temperature;
    if (sensor_get_value(SENSOR_B_PT0, &temperature) == SYS_STATUS_OK) {
        return temperature;
    }
    return 25.0f; // 默认室温
}

/**
 * @brief 获取三相电流不平衡度
 */
static float get_current_imbalance(void)
{
    // 获取三相电流的整流平均值
    float ia_avg = ADC_GET_IA_RECTIFIED_AVG();
    float ib_avg = ADC_GET_IB_RECTIFIED_AVG();
    float ic_avg = ADC_GET_IC_RECTIFIED_AVG();

    // 计算三相电流的平均值
    float avg_current = (ia_avg + ib_avg + ic_avg) * 0.3333f;

    // 避免除零错误，如果平均电流太小则认为不平衡度为0
    if (avg_current < 0.1f) {
        return 0.0f;
    }

    // 找出最大值和最小值
    float max_current = ia_avg;
    float min_current = ia_avg;

    if (ib_avg > max_current) max_current = ib_avg;
    if (ic_avg > max_current) max_current = ic_avg;

    if (ib_avg < min_current) min_current = ib_avg;
    if (ic_avg < min_current) min_current = ic_avg;

    // 计算不平衡度百分比
    // 不平衡度 = (最大电流 - 最小电流) / 平均电流 * 100%
    float imbalance_percent = ((max_current - min_current) / avg_current) * 100.0f;

    return imbalance_percent;
}

/**
 * @brief 获取电机转速
 */
static float get_motor_speed(void)
{
    // TODO: 实现电机转速获取
    // 需要从编码器或其他速度反馈源获取
    return 0.0f; // 占位符
}

/* ======================== 故障记录功能实现 ======================== */

/**
 * @brief 添加故障记录
 */
static void add_fault_record(fault_code_t fault_code, protect_param_id_t param_id,
                           protect_level_t level, float trigger_value, uint32_t trigger_time)
{
    // 获取写入位置
    fault_record_t *record = &fault_records[fault_record_index];

    // 填充故障记录
    record->trigger_time_ms = trigger_time;
    record->fault_code = fault_code;
    record->param_id = param_id;
    record->level = level;
    record->trigger_value = trigger_value;
    record->valid = 1;

    // 更新索引和计数
    fault_record_index = (fault_record_index + 1) % FAULT_RECORD_MAX_COUNT;
    if (fault_record_count < FAULT_RECORD_MAX_COUNT) {
        fault_record_count++;
    }
}

/**
 * @brief 获取故障记录数量
 */
uint8_t Protection_Monitor_GetFaultRecordCount(void)
{
    if (!module_initialized) {
        return 0;
    }

    return fault_record_count;
}

/**
 * @brief 获取指定索引的故障记录
 */
int Protection_Monitor_GetFaultRecord(uint8_t index, fault_record_t *record)
{
    if (!module_initialized || record == NULL || index >= fault_record_count) {
        return -1;
    }

    // 计算实际索引（0为最新记录）
    uint8_t actual_index;
    if (fault_record_count < FAULT_RECORD_MAX_COUNT) {
        // 未满时，从最新开始倒序
        if (index >= fault_record_index) {
            return -1;
        }
        actual_index = (fault_record_index - 1 - index + FAULT_RECORD_MAX_COUNT) % FAULT_RECORD_MAX_COUNT;
    } else {
        // 已满时，环形缓冲区
        actual_index = (fault_record_index - 1 - index + FAULT_RECORD_MAX_COUNT) % FAULT_RECORD_MAX_COUNT;
    }

    // 检查记录有效性
    if (!fault_records[actual_index].valid) {
        return -1;
    }

    // 复制记录
    *record = fault_records[actual_index];
    return 0;
}

/**
 * @brief 清除所有故障记录
 */
void Protection_Monitor_ClearFaultRecords(void)
{
    if (!module_initialized) {
        return;
    }

    fault_record_count = 0;
    fault_record_index = 0;
    memset(fault_records, 0, sizeof(fault_records));
}

/**
 * @brief 获取最新的故障记录
 */
int Protection_Monitor_GetLatestFaultRecord(fault_record_t *record)
{
    return Protection_Monitor_GetFaultRecord(0, record);
}

/**
 * @brief 根据故障码查找故障记录
 */
int Protection_Monitor_FindFaultRecord(fault_code_t fault_code, fault_record_t *record)
{
    if (!module_initialized || record == NULL) {
        return -1;
    }

    // 从最新记录开始查找
    for (uint8_t i = 0; i < fault_record_count; i++) {
        fault_record_t temp_record;
        if (Protection_Monitor_GetFaultRecord(i, &temp_record) == 0) {
            if (temp_record.fault_code == fault_code) {
                *record = temp_record;
                return 0;
            }
        }
    }

    return -1; // 未找到匹配记录
}



/* ======================== 外部接口函数实现（占位符） ======================== */

/**
 * @brief 获取电机转速
 */
float Protection_Monitor_GetMotorSpeed(void)
{
    // TODO: 实现电机转速获取
    // 需要从电机控制模块获取实际转速
    return 0.0f; // 占位符
}

/**
 * @brief 获取转矩电流Iq
 */
float Protection_Monitor_GetTorqueCurrent(void)
{
    // TODO: 实现转矩电流获取
    // 需要从电机控制模块获取实际Iq值
    return 0.0f; // 占位符
}

/* ======================== 堵转检测集成函数 ======================== */

/**
 * @brief 获取堵转检测状态（集成到保护框架）
 */
static float get_stall_detection(void)
{
    // 获取当前转速和转矩电流
    float current_speed = Protection_Monitor_GetMotorSpeed();
    float current_iq = Protection_Monitor_GetTorqueCurrent();
    float iq_abs = _fabsf(current_iq);

    // 检查堵转条件
    // 条件1: 转速低于阈值
    uint8_t speed_condition = (current_speed < STALL_SPEED_THRESHOLD) ? 1 : 0;

    // 条件2: 转矩电流满足任一级别的阈值
    uint8_t current_condition = (iq_abs >= STALL_IQ_THRESHOLD_1) ? 1 : 0;

    // 两个条件都满足才认为堵转条件成立
    return (speed_condition && current_condition) ? 1.0f : 0.0f;
}

/**
 * @brief 堵转保护警告级别动态延时计算
 * @param current_value: 当前堵转检测值（1.0表示检测到堵转）
 * @return 动态延时时间(ms)
 */
static uint32_t get_stall_dynamic_delay_warn(float current_value)
{
    if (current_value < 1.0f) {
        return UINT32_MAX; // 未检测到堵转，永远不会触发
    }

    // 获取当前转矩电流
    float current_iq = Protection_Monitor_GetTorqueCurrent();
    float iq_abs = _fabsf(current_iq);

    // 根据电流大小返回不同的延时时间
    if (iq_abs >= STALL_IQ_THRESHOLD_3) {
        return STALL_TIME_THRESHOLD_3 + 1000;  // 12A -> 1200ms
    } else if (iq_abs >= STALL_IQ_THRESHOLD_2) {
        return STALL_TIME_THRESHOLD_2 + 1000;  // 9A -> 1300ms
    } else if (iq_abs >= STALL_IQ_THRESHOLD_1) {
        return STALL_TIME_THRESHOLD_1 + 1000;  // 6A -> 1500ms
    } else {
        return UINT32_MAX;  // 电流不足，永远不会触发
    }
}

/**
 * @brief 堵转保护故障级别动态延时计算
 * @param current_value: 当前堵转检测值（1.0表示检测到堵转）
 * @return 动态延时时间(ms)
 */
static uint32_t get_stall_dynamic_delay_fault(float current_value)
{
    if (current_value < 1.0f) {
        return UINT32_MAX; // 未检测到堵转，永远不会触发
    }

    // 获取当前转矩电流
    float current_iq = Protection_Monitor_GetTorqueCurrent();
    float iq_abs = _fabsf(current_iq);

    // 根据电流大小返回不同的延时时间（故障级别使用原始延时）
    if (iq_abs >= STALL_IQ_THRESHOLD_3) {
        return STALL_TIME_THRESHOLD_3;  // 12A -> 200ms
    } else if (iq_abs >= STALL_IQ_THRESHOLD_2) {
        return STALL_TIME_THRESHOLD_2;  // 9A -> 300ms
    } else if (iq_abs >= STALL_IQ_THRESHOLD_1) {
        return STALL_TIME_THRESHOLD_1;  // 6A -> 500ms
    } else {
        return UINT32_MAX;  // 电流不足，永远不会触发
    }
}
