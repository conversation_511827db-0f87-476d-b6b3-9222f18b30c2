/*
// Version: V1.0
// Date: 	Oct 25, 2012
// Author:	csc @YD
//===========================================================================
//
// 文件名:	DMC_GlobalPrototypes.h
//
// 用途:	电机控制库全局定义头文件，包含各功能模块的头文件。
//
//===========================================================================
*/
#ifndef SYSCTL_ALLHEADERS_H
#define SYSCTL_ALLHEADERS_H
//============================
// #include <math.h>
#include "MathBasic.h"
#include "SysCtl_SysMoore.h"
#include "SysCtl_AnalogProcess.h"
#include "SysCtl_ConstDef.h"
#include "SysCtl_CsvParamDef.h"
#include "Motor_VectorControl.h"
#include "SysCtl_RotorGet.h"
#include "SysCtl_IoAd2s1210.h"
#include "arm_math.h"
/****************************/
#include "SysCtl_GlobalVar.h"
// #include "MotorParams.h"
// #include "MotorData.h"
#include "SysVoltBase.h"
#include "HWInterface.h"
#include "ad2s1212_spi.h"
//#include "SysCtl_RunMoore.h"
//#include "SysCtl_Switch.h"

//#include "COMMInterface.h"

// #include "FpgaInf.h"


//#include "VVVF.h"
///===========矢量控制============
 //#include "cur_vol_model.h"
//#include "DMC_CLARKE.h"
//#include "DMC_Filter.h"
//#include "DMC_Filter1Order.h"
//#include "DMC_Inertia1order.h"
//#include "DMC_PARK.h"
//#include "DMC_PI.h"
//#include "DmcLib.h"
//#include "Exciting_Current.h"
//#include "filter.h"
//#include "iClarke.h"
//#include "Interg.h"
//#include "ipark.h"
//#include "LPF.h"

//#include "Ratelimit.h"
//#include "Rms_out.h"

//#include "Slip.h"
//#include "speed_kpki.h"
//#include "vc_slvc_parameter.h"
//#include "Vector_SpeedTest.h"
//#include "Vectorcontrol.h"

// #include "Gird_PLL.h"
// #include "PWM_Rectifier.h"
// #include "PWM_Inverter.h"
 //#include "PI_Regulator.h"
//#include "coordinate_transform.h"
//#include "filter.h"
// #include "Motor_MRAS.h"

#endif
//===========================================================================
// No more
//===========================================================================
