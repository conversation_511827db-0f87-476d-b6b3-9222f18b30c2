


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       *******************
    2 00000000         ;* @file     startup_at32a403a.s
    3 00000000         ;* @brief    at32a403a startup file for keil
    4 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>  
    5 00000000         ;*******************************************************
                       *******************
    6 00000000         ;
    7 00000000         
    8 00000000         ; Amount of memory (in bytes) allocated for Stack
    9 00000000         ; Tailor this value to your application needs
   10 00000000         ; <h> Stack Configuration
   11 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   12 00000000         ; </h>
   13 00000000         
   14 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   15 00000000         
   16 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   17 00000000         Stack_Mem
                               SPACE            Stack_Size
   18 00000400         __initial_sp
   19 00000400         
   20 00000400         ; <h> Heap Configuration
   21 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   22 00000400         ; </h>
   23 00000400         
   24 00000400 00000400 
                       Heap_Size
                               EQU              0x00000400
   25 00000400         
   26 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   27 00000000         __heap_base
   28 00000000         Heap_Mem
                               SPACE            Heap_Size
   29 00000400         __heap_limit
   30 00000400         
   31 00000400                 PRESERVE8
   32 00000400                 THUMB
   33 00000400         
   34 00000400         
   35 00000400         ; Vector Table Mapped to Address 0 at Reset
   36 00000400                 AREA             RESET, DATA, READONLY
   37 00000000                 EXPORT           __Vectors
   38 00000000                 EXPORT           __Vectors_End
   39 00000000                 EXPORT           __Vectors_Size
   40 00000000         
   41 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   42 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   43 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   44 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   45 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler



ARM Macro Assembler    Page 2 


                                                            
   46 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   47 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   48 0000001C 00000000        DCD              0           ; Reserved
   49 00000020 00000000        DCD              0           ; Reserved
   50 00000024 00000000        DCD              0           ; Reserved
   51 00000028 00000000        DCD              0           ; Reserved
   52 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   53 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   54 00000034 00000000        DCD              0           ; Reserved
   55 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   56 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   57 00000040         
   58 00000040         ; External Interrupts
   59 00000040 00000000        DCD              WWDT_IRQHandler ; Window Watchd
                                                            og Timer
   60 00000044 00000000        DCD              PVM_IRQHandler ; PVM through EX
                                                            INT Line detect
   61 00000048 00000000        DCD              TAMPER_IRQHandler ; Tamper
   62 0000004C 00000000        DCD              RTC_IRQHandler ; RTC
   63 00000050 00000000        DCD              FLASH_IRQHandler ; Flash
   64 00000054 00000000        DCD              CRM_IRQHandler ; CRM
   65 00000058 00000000        DCD              EXINT0_IRQHandler 
                                                            ; EXINT Line 0
   66 0000005C 00000000        DCD              EXINT1_IRQHandler 
                                                            ; EXINT Line 1
   67 00000060 00000000        DCD              EXINT2_IRQHandler 
                                                            ; EXINT Line 2
   68 00000064 00000000        DCD              EXINT3_IRQHandler 
                                                            ; EXINT Line 3
   69 00000068 00000000        DCD              EXINT4_IRQHandler 
                                                            ; EXINT Line 4
   70 0000006C 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   71 00000070 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; DMA1 Channel 2
   72 00000074 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; DMA1 Channel 3
   73 00000078 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; DMA1 Channel 4
   74 0000007C 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; DMA1 Channel 5
   75 00000080 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; DMA1 Channel 6
   76 00000084 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; DMA1 Channel 7
   77 00000088 00000000        DCD              ADC1_2_IRQHandler ; ADC1 & ADC2
                                                            
   78 0000008C 00000000        DCD              USBFS_H_CAN1_TX_IRQHandler ; US
                                                            B High Priority or 
                                                            CAN1 TX
   79 00000090 00000000        DCD              USBFS_L_CAN1_RX0_IRQHandler ; U
                                                            SB Low  Priority or



ARM Macro Assembler    Page 3 


                                                             CAN1 RX0
   80 00000094 00000000        DCD              CAN1_RX1_IRQHandler ; CAN1 RX1
   81 00000098 00000000        DCD              CAN1_SE_IRQHandler ; CAN1 SE
   82 0000009C 00000000        DCD              EXINT9_5_IRQHandler 
                                                            ; EXINT Line [9:5]
   83 000000A0 00000000        DCD              TMR1_BRK_TMR9_IRQHandler ; TMR1
                                                             Brake and TMR9
   84 000000A4 00000000        DCD              TMR1_OVF_TMR10_IRQHandler ; TMR
                                                            1 overflow and TMR1
                                                            0
   85 000000A8 00000000        DCD              TMR1_TRG_HALL_TMR11_IRQHandler 
                                                            ; TMR1 Trigger and 
                                                            hall and TMR11
   86 000000AC 00000000        DCD              TMR1_CH_IRQHandler 
                                                            ; TMR1 channel
   87 000000B0 00000000        DCD              TMR2_GLOBAL_IRQHandler ; TMR2
   88 000000B4 00000000        DCD              TMR3_GLOBAL_IRQHandler ; TMR3
   89 000000B8 00000000        DCD              TMR4_GLOBAL_IRQHandler ; TMR4
   90 000000BC 00000000        DCD              I2C1_EVT_IRQHandler 
                                                            ; I2C1 Event
   91 000000C0 00000000        DCD              I2C1_ERR_IRQHandler 
                                                            ; I2C1 Error
   92 000000C4 00000000        DCD              I2C2_EVT_IRQHandler 
                                                            ; I2C2 Event
   93 000000C8 00000000        DCD              I2C2_ERR_IRQHandler 
                                                            ; I2C2 Error
   94 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1
   95 000000D0 00000000        DCD              SPI2_I2S2EXT_IRQHandler 
                                                            ; SPI2 & I2S2EXT
   96 000000D4 00000000        DCD              USART1_IRQHandler ; USART1
   97 000000D8 00000000        DCD              USART2_IRQHandler ; USART2
   98 000000DC 00000000        DCD              USART3_IRQHandler ; USART3
   99 000000E0 00000000        DCD              EXINT15_10_IRQHandler ; EXINT L
                                                            ine [15:10]
  100 000000E4 00000000        DCD              RTCAlarm_IRQHandler ; RTC Alarm
                                                             through EXINT Line
                                                            
  101 000000E8 00000000        DCD              USBFSWakeUp_IRQHandler ; USB Wa
                                                            keup from suspend
  102 000000EC 00000000        DCD              TMR8_BRK_TMR12_IRQHandler ; TMR
                                                            8 Brake and TMR12
  103 000000F0 00000000        DCD              TMR8_OVF_TMR13_IRQHandler ; TMR
                                                            8 overflow and TMR1
                                                            3
  104 000000F4 00000000        DCD              TMR8_TRG_HALL_TMR14_IRQHandler 
                                                            ; TMR8 Trigger and 
                                                            hall and TMR14
  105 000000F8 00000000        DCD              TMR8_CH_IRQHandler 
                                                            ; TMR8 channel
  106 000000FC 00000000        DCD              ADC3_IRQHandler ; ADC3
  107 00000100 00000000        DCD              XMC_IRQHandler ; XMC
  108 00000104 00000000        DCD              SDIO1_IRQHandler ; SDIO1
  109 00000108 00000000        DCD              TMR5_GLOBAL_IRQHandler ; TMR5
  110 0000010C 00000000        DCD              SPI3_I2S3EXT_IRQHandler 
                                                            ; SPI3 & I2S3EXT
  111 00000110 00000000        DCD              UART4_IRQHandler ; UART4
  112 00000114 00000000        DCD              UART5_IRQHandler ; UART5
  113 00000118 00000000        DCD              TMR6_GLOBAL_IRQHandler ; TMR6
  114 0000011C 00000000        DCD              TMR7_GLOBAL_IRQHandler ; TMR7



ARM Macro Assembler    Page 4 


  115 00000120 00000000        DCD              DMA2_Channel1_IRQHandler 
                                                            ; DMA2 Channel1
  116 00000124 00000000        DCD              DMA2_Channel2_IRQHandler 
                                                            ; DMA2 Channel2
  117 00000128 00000000        DCD              DMA2_Channel3_IRQHandler 
                                                            ; DMA2 Channel3
  118 0000012C 00000000        DCD              DMA2_Channel4_5_IRQHandler ; DM
                                                            A2 Channel4 & Chann
                                                            el5
  119 00000130 00000000        DCD              SDIO2_IRQHandler ; SDIO2
  120 00000134 00000000        DCD              I2C3_EVT_IRQHandler 
                                                            ; I2C3 Event
  121 00000138 00000000        DCD              I2C3_ERR_IRQHandler 
                                                            ; I2C3 Error
  122 0000013C 00000000        DCD              SPI4_IRQHandler ; SPI4
  123 00000140 00000000        DCD              0           ; Reserved
  124 00000144 00000000        DCD              0           ; Reserved
  125 00000148 00000000        DCD              0           ; Reserved
  126 0000014C 00000000        DCD              0           ; Reserved
  127 00000150 00000000        DCD              CAN2_TX_IRQHandler ; CAN2 TX
  128 00000154 00000000        DCD              CAN2_RX0_IRQHandler ; CAN2 RX0
  129 00000158 00000000        DCD              CAN2_RX1_IRQHandler ; CAN2 RX1
  130 0000015C 00000000        DCD              CAN2_SE_IRQHandler ; CAN2 SE
  131 00000160 00000000        DCD              ACC_IRQHandler ; ACC
  132 00000164 00000000        DCD              USBFS_MAPH_IRQHandler 
                                                            ; USB Map High
  133 00000168 00000000        DCD              USBFS_MAPL_IRQHandler 
                                                            ; USB Map Low
  134 0000016C 00000000        DCD              DMA2_Channel6_7_IRQHandler ; DM
                                                            A2 Channel6 & Chann
                                                            el7
  135 00000170 00000000        DCD              USART6_IRQHandler ; USART6
  136 00000174 00000000        DCD              UART7_IRQHandler ; UART7
  137 00000178 00000000        DCD              UART8_IRQHandler ; UART8
  138 0000017C 00000000        DCD              EMAC_IRQHandler ; EMAC
  139 00000180 00000000        DCD              EMAC_WKUP_IRQHandler 
                                                            ; EMAC_WKUP
  140 00000184         __Vectors_End
  141 00000184         
  142 00000184 00000184 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  143 00000184         
  144 00000184                 AREA             |.text|, CODE, READONLY
  145 00000000         
  146 00000000         ; Reset handler
  147 00000000         Reset_Handler
                               PROC
  148 00000000                 EXPORT           Reset_Handler                  
     [WEAK]
  149 00000000                 IMPORT           __main
  150 00000000                 IMPORT           SystemInit
  151 00000000 4809            LDR              R0, =SystemInit
  152 00000002 4780            BLX              R0
  153 00000004 4809            LDR              R0, =__main
  154 00000006 4700            BX               R0
  155 00000008                 ENDP
  156 00000008         
  157 00000008         ; Dummy Exception Handlers (infinite loops which can be 



ARM Macro Assembler    Page 5 


                       modified)
  158 00000008         
  159 00000008         NMI_Handler
                               PROC
  160 00000008                 EXPORT           NMI_Handler                    
     [WEAK]
  161 00000008 E7FE            B                .
  162 0000000A                 ENDP
  164 0000000A         HardFault_Handler
                               PROC
  165 0000000A                 EXPORT           HardFault_Handler              
     [WEAK]
  166 0000000A E7FE            B                .
  167 0000000C                 ENDP
  169 0000000C         MemManage_Handler
                               PROC
  170 0000000C                 EXPORT           MemManage_Handler              
     [WEAK]
  171 0000000C E7FE            B                .
  172 0000000E                 ENDP
  174 0000000E         BusFault_Handler
                               PROC
  175 0000000E                 EXPORT           BusFault_Handler               
     [WEAK]
  176 0000000E E7FE            B                .
  177 00000010                 ENDP
  179 00000010         UsageFault_Handler
                               PROC
  180 00000010                 EXPORT           UsageFault_Handler             
     [WEAK]
  181 00000010 E7FE            B                .
  182 00000012                 ENDP
  183 00000012         SVC_Handler
                               PROC
  184 00000012                 EXPORT           SVC_Handler                    
     [WEAK]
  185 00000012 E7FE            B                .
  186 00000014                 ENDP
  188 00000014         DebugMon_Handler
                               PROC
  189 00000014                 EXPORT           DebugMon_Handler               
     [WEAK]
  190 00000014 E7FE            B                .
  191 00000016                 ENDP
  192 00000016         PendSV_Handler
                               PROC
  193 00000016                 EXPORT           PendSV_Handler                 
     [WEAK]
  194 00000016 E7FE            B                .
  195 00000018                 ENDP
  196 00000018         SysTick_Handler
                               PROC
  197 00000018                 EXPORT           SysTick_Handler                
     [WEAK]
  198 00000018 E7FE            B                .
  199 0000001A                 ENDP
  200 0000001A         
  201 0000001A         Default_Handler
                               PROC



ARM Macro Assembler    Page 6 


  202 0000001A         
  203 0000001A                 EXPORT           WWDT_IRQHandler                
     [WEAK]
  204 0000001A                 EXPORT           PVM_IRQHandler                 
     [WEAK]
  205 0000001A                 EXPORT           TAMPER_IRQHandler              
     [WEAK]
  206 0000001A                 EXPORT           RTC_IRQHandler                 
     [WEAK]
  207 0000001A                 EXPORT           FLASH_IRQHandler               
     [WEAK]
  208 0000001A                 EXPORT           CRM_IRQHandler                 
     [WEAK]
  209 0000001A                 EXPORT           EXINT0_IRQHandler              
     [WEAK]
  210 0000001A                 EXPORT           EXINT1_IRQHandler              
     [WEAK]
  211 0000001A                 EXPORT           EXINT2_IRQHandler              
     [WEAK]
  212 0000001A                 EXPORT           EXINT3_IRQHandler              
     [WEAK]
  213 0000001A                 EXPORT           EXINT4_IRQHandler              
     [WEAK]
  214 0000001A                 EXPORT           DMA1_Channel1_IRQHandler       
     [WEAK]
  215 0000001A                 EXPORT           DMA1_Channel2_IRQHandler       
     [WEAK]
  216 0000001A                 EXPORT           DMA1_Channel3_IRQHandler       
     [WEAK]
  217 0000001A                 EXPORT           DMA1_Channel4_IRQHandler       
     [WEAK]
  218 0000001A                 EXPORT           DMA1_Channel5_IRQHandler       
     [WEAK]
  219 0000001A                 EXPORT           DMA1_Channel6_IRQHandler       
     [WEAK]
  220 0000001A                 EXPORT           DMA1_Channel7_IRQHandler       
     [WEAK]
  221 0000001A                 EXPORT           ADC1_2_IRQHandler              
     [WEAK]
  222 0000001A                 EXPORT           USBFS_H_CAN1_TX_IRQHandler     
     [WEAK]
  223 0000001A                 EXPORT           USBFS_L_CAN1_RX0_IRQHandler    
     [WEAK]
  224 0000001A                 EXPORT           CAN1_RX1_IRQHandler            
     [WEAK]
  225 0000001A                 EXPORT           CAN1_SE_IRQHandler             
     [WEAK]
  226 0000001A                 EXPORT           EXINT9_5_IRQHandler            
     [WEAK]
  227 0000001A                 EXPORT           TMR1_BRK_TMR9_IRQHandler       
     [WEAK]
  228 0000001A                 EXPORT           TMR1_OVF_TMR10_IRQHandler      
     [WEAK]
  229 0000001A                 EXPORT           TMR1_TRG_HALL_TMR11_IRQHandler 
     [WEAK]
  230 0000001A                 EXPORT           TMR1_CH_IRQHandler             
     [WEAK]
  231 0000001A                 EXPORT           TMR2_GLOBAL_IRQHandler         
     [WEAK]



ARM Macro Assembler    Page 7 


  232 0000001A                 EXPORT           TMR3_GLOBAL_IRQHandler         
     [WEAK]
  233 0000001A                 EXPORT           TMR4_GLOBAL_IRQHandler         
     [WEAK]
  234 0000001A                 EXPORT           I2C1_EVT_IRQHandler            
     [WEAK]
  235 0000001A                 EXPORT           I2C1_ERR_IRQHandler            
     [WEAK]
  236 0000001A                 EXPORT           I2C2_EVT_IRQHandler            
     [WEAK]
  237 0000001A                 EXPORT           I2C2_ERR_IRQHandler            
     [WEAK]
  238 0000001A                 EXPORT           SPI1_IRQHandler                
     [WEAK]
  239 0000001A                 EXPORT           SPI2_I2S2EXT_IRQHandler        
     [WEAK]
  240 0000001A                 EXPORT           USART1_IRQHandler              
     [WEAK]
  241 0000001A                 EXPORT           USART2_IRQHandler              
     [WEAK]
  242 0000001A                 EXPORT           USART3_IRQHandler              
     [WEAK]
  243 0000001A                 EXPORT           EXINT15_10_IRQHandler          
     [WEAK]
  244 0000001A                 EXPORT           RTCAlarm_IRQHandler            
     [WEAK]
  245 0000001A                 EXPORT           USBFSWakeUp_IRQHandler         
     [WEAK]
  246 0000001A                 EXPORT           TMR8_BRK_TMR12_IRQHandler      
     [WEAK]
  247 0000001A                 EXPORT           TMR8_OVF_TMR13_IRQHandler      
     [WEAK]
  248 0000001A                 EXPORT           TMR8_TRG_HALL_TMR14_IRQHandler 
     [WEAK]
  249 0000001A                 EXPORT           TMR8_CH_IRQHandler             
     [WEAK]
  250 0000001A                 EXPORT           ADC3_IRQHandler                
     [WEAK]
  251 0000001A                 EXPORT           XMC_IRQHandler                 
     [WEAK]
  252 0000001A                 EXPORT           SDIO1_IRQHandler               
     [WEAK]
  253 0000001A                 EXPORT           TMR5_GLOBAL_IRQHandler         
     [WEAK]
  254 0000001A                 EXPORT           SPI3_I2S3EXT_IRQHandler        
     [WEAK]
  255 0000001A                 EXPORT           UART4_IRQHandler               
     [WEAK]
  256 0000001A                 EXPORT           UART5_IRQHandler               
     [WEAK]
  257 0000001A                 EXPORT           TMR6_GLOBAL_IRQHandler         
     [WEAK]
  258 0000001A                 EXPORT           TMR7_GLOBAL_IRQHandler         
     [WEAK]
  259 0000001A                 EXPORT           DMA2_Channel1_IRQHandler       
     [WEAK]
  260 0000001A                 EXPORT           DMA2_Channel2_IRQHandler       
     [WEAK]
  261 0000001A                 EXPORT           DMA2_Channel3_IRQHandler       



ARM Macro Assembler    Page 8 


     [WEAK]
  262 0000001A                 EXPORT           DMA2_Channel4_5_IRQHandler     
     [WEAK]
  263 0000001A                 EXPORT           SDIO2_IRQHandler               
     [WEAK]
  264 0000001A                 EXPORT           I2C3_EVT_IRQHandler            
     [WEAK]
  265 0000001A                 EXPORT           I2C3_ERR_IRQHandler            
     [WEAK]
  266 0000001A                 EXPORT           SPI4_IRQHandler                
     [WEAK]
  267 0000001A                 EXPORT           CAN2_TX_IRQHandler             
     [WEAK]
  268 0000001A                 EXPORT           CAN2_RX0_IRQHandler            
     [WEAK]
  269 0000001A                 EXPORT           CAN2_RX1_IRQHandler            
     [WEAK]
  270 0000001A                 EXPORT           CAN2_SE_IRQHandler             
     [WEAK]
  271 0000001A                 EXPORT           ACC_IRQHandler                 
     [WEAK]
  272 0000001A                 EXPORT           USBFS_MAPH_IRQHandler          
     [WEAK]
  273 0000001A                 EXPORT           USBFS_MAPL_IRQHandler          
     [WEAK]
  274 0000001A                 EXPORT           DMA2_Channel6_7_IRQHandler     
     [WEAK]
  275 0000001A                 EXPORT           USART6_IRQHandler              
     [WEAK]
  276 0000001A                 EXPORT           UART7_IRQHandler               
     [WEAK]
  277 0000001A                 EXPORT           UART8_IRQHandler               
     [WEAK]
  278 0000001A                 EXPORT           EMAC_IRQHandler                
     [WEAK]
  279 0000001A                 EXPORT           EMAC_WKUP_IRQHandler           
     [WEAK]
  280 0000001A         WWDT_IRQHandler
  281 0000001A         PVM_IRQHandler
  282 0000001A         TAMPER_IRQHandler
  283 0000001A         RTC_IRQHandler
  284 0000001A         FLASH_IRQHandler
  285 0000001A         CRM_IRQHandler
  286 0000001A         EXINT0_IRQHandler
  287 0000001A         EXINT1_IRQHandler
  288 0000001A         EXINT2_IRQHandler
  289 0000001A         EXINT3_IRQHandler
  290 0000001A         EXINT4_IRQHandler
  291 0000001A         DMA1_Channel1_IRQHandler
  292 0000001A         DMA1_Channel2_IRQHandler
  293 0000001A         DMA1_Channel3_IRQHandler
  294 0000001A         DMA1_Channel4_IRQHandler
  295 0000001A         DMA1_Channel5_IRQHandler
  296 0000001A         DMA1_Channel6_IRQHandler
  297 0000001A         DMA1_Channel7_IRQHandler
  298 0000001A         ADC1_2_IRQHandler
  299 0000001A         USBFS_H_CAN1_TX_IRQHandler
  300 0000001A         USBFS_L_CAN1_RX0_IRQHandler
  301 0000001A         CAN1_RX1_IRQHandler



ARM Macro Assembler    Page 9 


  302 0000001A         CAN1_SE_IRQHandler
  303 0000001A         EXINT9_5_IRQHandler
  304 0000001A         TMR1_BRK_TMR9_IRQHandler
  305 0000001A         TMR1_OVF_TMR10_IRQHandler
  306 0000001A         TMR1_TRG_HALL_TMR11_IRQHandler
  307 0000001A         TMR1_CH_IRQHandler
  308 0000001A         TMR2_GLOBAL_IRQHandler
  309 0000001A         TMR3_GLOBAL_IRQHandler
  310 0000001A         TMR4_GLOBAL_IRQHandler
  311 0000001A         I2C1_EVT_IRQHandler
  312 0000001A         I2C1_ERR_IRQHandler
  313 0000001A         I2C2_EVT_IRQHandler
  314 0000001A         I2C2_ERR_IRQHandler
  315 0000001A         SPI1_IRQHandler
  316 0000001A         SPI2_I2S2EXT_IRQHandler
  317 0000001A         USART1_IRQHandler
  318 0000001A         USART2_IRQHandler
  319 0000001A         USART3_IRQHandler
  320 0000001A         EXINT15_10_IRQHandler
  321 0000001A         RTCAlarm_IRQHandler
  322 0000001A         USBFSWakeUp_IRQHandler
  323 0000001A         TMR8_BRK_TMR12_IRQHandler
  324 0000001A         TMR8_OVF_TMR13_IRQHandler
  325 0000001A         TMR8_TRG_HALL_TMR14_IRQHandler
  326 0000001A         TMR8_CH_IRQHandler
  327 0000001A         ADC3_IRQHandler
  328 0000001A         XMC_IRQHandler
  329 0000001A         SDIO1_IRQHandler
  330 0000001A         TMR5_GLOBAL_IRQHandler
  331 0000001A         SPI3_I2S3EXT_IRQHandler
  332 0000001A         UART4_IRQHandler
  333 0000001A         UART5_IRQHandler
  334 0000001A         TMR6_GLOBAL_IRQHandler
  335 0000001A         TMR7_GLOBAL_IRQHandler
  336 0000001A         DMA2_Channel1_IRQHandler
  337 0000001A         DMA2_Channel2_IRQHandler
  338 0000001A         DMA2_Channel3_IRQHandler
  339 0000001A         DMA2_Channel4_5_IRQHandler
  340 0000001A         SDIO2_IRQHandler
  341 0000001A         I2C3_EVT_IRQHandler
  342 0000001A         I2C3_ERR_IRQHandler
  343 0000001A         SPI4_IRQHandler
  344 0000001A         CAN2_TX_IRQHandler
  345 0000001A         CAN2_RX0_IRQHandler
  346 0000001A         CAN2_RX1_IRQHandler
  347 0000001A         CAN2_SE_IRQHandler
  348 0000001A         ACC_IRQHandler
  349 0000001A         USBFS_MAPH_IRQHandler
  350 0000001A         USBFS_MAPL_IRQHandler
  351 0000001A         DMA2_Channel6_7_IRQHandler
  352 0000001A         USART6_IRQHandler
  353 0000001A         UART7_IRQHandler
  354 0000001A         UART8_IRQHandler
  355 0000001A         EMAC_IRQHandler
  356 0000001A         EMAC_WKUP_IRQHandler
  357 0000001A E7FE            B                .
  358 0000001C         
  359 0000001C                 ENDP
  360 0000001C         



ARM Macro Assembler    Page 10 


  361 0000001C                 ALIGN
  362 0000001C         
  363 0000001C         ;*******************************************************
                       ************************
  364 0000001C         ; User Stack and Heap initialization
  365 0000001C         ;*******************************************************
                       ************************
  366 0000001C                 IF               :DEF:__MICROLIB
  373 0000001C         
  374 0000001C                 IMPORT           __use_two_region_memory
  375 0000001C                 EXPORT           __user_initial_stackheap
  376 0000001C         
  377 0000001C         __user_initial_stackheap
  378 0000001C         
  379 0000001C 4804            LDR              R0, = Heap_Mem
  380 0000001E 4905            LDR              R1, = (Stack_Mem + Stack_Size)
  381 00000020 4A05            LDR              R2, = (Heap_Mem +  Heap_Size)
  382 00000022 4B06            LDR              R3, = Stack_Mem
  383 00000024 4770            BX               LR
  384 00000026         
  385 00000026 00 00           ALIGN
  386 00000028         
  387 00000028                 ENDIF
  388 00000028         
  389 00000028                 END
              00000000 
              00000000 
              00000000 
              00000400 
              00000400 
              00000000 
Command Line: --debug --xref --diag_suppress=9931,A1950W --cpu=Cortex-M4.fp.sp 
--depend=.\objects\startup_at32a403a.d -o.\objects\startup_at32a403a.o -I"D:\Pr
ogram Files\Keil5l\Arm\Packs\ArteryTek\AT32A403A_DFP\2.0.8\Device\Include" --pr
edefine="__UVISION_VERSION SETA 540" --predefine="AT32A403ARGT7 SETA 1" --list=
.\listings\startup_at32a403a.lst startup_at32a403a.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 16 in file startup_at32a403a.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 17 in file startup_at32a403a.s
   Uses
      At line 380 in file startup_at32a403a.s
      At line 382 in file startup_at32a403a.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 18 in file startup_at32a403a.s
   Uses
      At line 41 in file startup_at32a403a.s
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 26 in file startup_at32a403a.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 28 in file startup_at32a403a.s
   Uses
      At line 379 in file startup_at32a403a.s
      At line 381 in file startup_at32a403a.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 27 in file startup_at32a403a.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 29 in file startup_at32a403a.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 36 in file startup_at32a403a.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 41 in file startup_at32a403a.s
   Uses
      At line 37 in file startup_at32a403a.s
      At line 142 in file startup_at32a403a.s

__Vectors_End 00000184

Symbol: __Vectors_End
   Definitions
      At line 140 in file startup_at32a403a.s
   Uses
      At line 38 in file startup_at32a403a.s
      At line 142 in file startup_at32a403a.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 144 in file startup_at32a403a.s
   Uses
      None
Comment: .text unused
ACC_IRQHandler 0000001A

Symbol: ACC_IRQHandler
   Definitions
      At line 348 in file startup_at32a403a.s
   Uses
      At line 131 in file startup_at32a403a.s
      At line 271 in file startup_at32a403a.s

ADC1_2_IRQHandler 0000001A

Symbol: ADC1_2_IRQHandler
   Definitions
      At line 298 in file startup_at32a403a.s
   Uses
      At line 77 in file startup_at32a403a.s
      At line 221 in file startup_at32a403a.s

ADC3_IRQHandler 0000001A

Symbol: ADC3_IRQHandler
   Definitions
      At line 327 in file startup_at32a403a.s
   Uses
      At line 106 in file startup_at32a403a.s
      At line 250 in file startup_at32a403a.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 174 in file startup_at32a403a.s
   Uses
      At line 46 in file startup_at32a403a.s
      At line 175 in file startup_at32a403a.s

CAN1_RX1_IRQHandler 0000001A

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 301 in file startup_at32a403a.s
   Uses
      At line 80 in file startup_at32a403a.s
      At line 224 in file startup_at32a403a.s

CAN1_SE_IRQHandler 0000001A

Symbol: CAN1_SE_IRQHandler
   Definitions
      At line 302 in file startup_at32a403a.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 81 in file startup_at32a403a.s
      At line 225 in file startup_at32a403a.s

CAN2_RX0_IRQHandler 0000001A

Symbol: CAN2_RX0_IRQHandler
   Definitions
      At line 345 in file startup_at32a403a.s
   Uses
      At line 128 in file startup_at32a403a.s
      At line 268 in file startup_at32a403a.s

CAN2_RX1_IRQHandler 0000001A

Symbol: CAN2_RX1_IRQHandler
   Definitions
      At line 346 in file startup_at32a403a.s
   Uses
      At line 129 in file startup_at32a403a.s
      At line 269 in file startup_at32a403a.s

CAN2_SE_IRQHandler 0000001A

Symbol: CAN2_SE_IRQHandler
   Definitions
      At line 347 in file startup_at32a403a.s
   Uses
      At line 130 in file startup_at32a403a.s
      At line 270 in file startup_at32a403a.s

CAN2_TX_IRQHandler 0000001A

Symbol: CAN2_TX_IRQHandler
   Definitions
      At line 344 in file startup_at32a403a.s
   Uses
      At line 127 in file startup_at32a403a.s
      At line 267 in file startup_at32a403a.s

CRM_IRQHandler 0000001A

Symbol: CRM_IRQHandler
   Definitions
      At line 285 in file startup_at32a403a.s
   Uses
      At line 64 in file startup_at32a403a.s
      At line 208 in file startup_at32a403a.s

DMA1_Channel1_IRQHandler 0000001A

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 291 in file startup_at32a403a.s
   Uses
      At line 70 in file startup_at32a403a.s
      At line 214 in file startup_at32a403a.s

DMA1_Channel2_IRQHandler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 292 in file startup_at32a403a.s
   Uses
      At line 71 in file startup_at32a403a.s
      At line 215 in file startup_at32a403a.s

DMA1_Channel3_IRQHandler 0000001A

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 293 in file startup_at32a403a.s
   Uses
      At line 72 in file startup_at32a403a.s
      At line 216 in file startup_at32a403a.s

DMA1_Channel4_IRQHandler 0000001A

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 294 in file startup_at32a403a.s
   Uses
      At line 73 in file startup_at32a403a.s
      At line 217 in file startup_at32a403a.s

DMA1_Channel5_IRQHandler 0000001A

Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 295 in file startup_at32a403a.s
   Uses
      At line 74 in file startup_at32a403a.s
      At line 218 in file startup_at32a403a.s

DMA1_Channel6_IRQHandler 0000001A

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 296 in file startup_at32a403a.s
   Uses
      At line 75 in file startup_at32a403a.s
      At line 219 in file startup_at32a403a.s

DMA1_Channel7_IRQHandler 0000001A

Symbol: DMA1_Channel7_IRQHandler
   Definitions
      At line 297 in file startup_at32a403a.s
   Uses
      At line 76 in file startup_at32a403a.s
      At line 220 in file startup_at32a403a.s

DMA2_Channel1_IRQHandler 0000001A

Symbol: DMA2_Channel1_IRQHandler
   Definitions
      At line 336 in file startup_at32a403a.s
   Uses
      At line 115 in file startup_at32a403a.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 259 in file startup_at32a403a.s

DMA2_Channel2_IRQHandler 0000001A

Symbol: DMA2_Channel2_IRQHandler
   Definitions
      At line 337 in file startup_at32a403a.s
   Uses
      At line 116 in file startup_at32a403a.s
      At line 260 in file startup_at32a403a.s

DMA2_Channel3_IRQHandler 0000001A

Symbol: DMA2_Channel3_IRQHandler
   Definitions
      At line 338 in file startup_at32a403a.s
   Uses
      At line 117 in file startup_at32a403a.s
      At line 261 in file startup_at32a403a.s

DMA2_Channel4_5_IRQHandler 0000001A

Symbol: DMA2_Channel4_5_IRQHandler
   Definitions
      At line 339 in file startup_at32a403a.s
   Uses
      At line 118 in file startup_at32a403a.s
      At line 262 in file startup_at32a403a.s

DMA2_Channel6_7_IRQHandler 0000001A

Symbol: DMA2_Channel6_7_IRQHandler
   Definitions
      At line 351 in file startup_at32a403a.s
   Uses
      At line 134 in file startup_at32a403a.s
      At line 274 in file startup_at32a403a.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 188 in file startup_at32a403a.s
   Uses
      At line 53 in file startup_at32a403a.s
      At line 189 in file startup_at32a403a.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 201 in file startup_at32a403a.s
   Uses
      None
Comment: Default_Handler unused
EMAC_IRQHandler 0000001A

Symbol: EMAC_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 355 in file startup_at32a403a.s
   Uses
      At line 138 in file startup_at32a403a.s
      At line 278 in file startup_at32a403a.s

EMAC_WKUP_IRQHandler 0000001A

Symbol: EMAC_WKUP_IRQHandler
   Definitions
      At line 356 in file startup_at32a403a.s
   Uses
      At line 139 in file startup_at32a403a.s
      At line 279 in file startup_at32a403a.s

EXINT0_IRQHandler 0000001A

Symbol: EXINT0_IRQHandler
   Definitions
      At line 286 in file startup_at32a403a.s
   Uses
      At line 65 in file startup_at32a403a.s
      At line 209 in file startup_at32a403a.s

EXINT15_10_IRQHandler 0000001A

Symbol: EXINT15_10_IRQHandler
   Definitions
      At line 320 in file startup_at32a403a.s
   Uses
      At line 99 in file startup_at32a403a.s
      At line 243 in file startup_at32a403a.s

EXINT1_IRQHandler 0000001A

Symbol: EXINT1_IRQHandler
   Definitions
      At line 287 in file startup_at32a403a.s
   Uses
      At line 66 in file startup_at32a403a.s
      At line 210 in file startup_at32a403a.s

EXINT2_IRQHandler 0000001A

Symbol: EXINT2_IRQHandler
   Definitions
      At line 288 in file startup_at32a403a.s
   Uses
      At line 67 in file startup_at32a403a.s
      At line 211 in file startup_at32a403a.s

EXINT3_IRQHandler 0000001A

Symbol: EXINT3_IRQHandler
   Definitions
      At line 289 in file startup_at32a403a.s
   Uses
      At line 68 in file startup_at32a403a.s
      At line 212 in file startup_at32a403a.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

EXINT4_IRQHandler 0000001A

Symbol: EXINT4_IRQHandler
   Definitions
      At line 290 in file startup_at32a403a.s
   Uses
      At line 69 in file startup_at32a403a.s
      At line 213 in file startup_at32a403a.s

EXINT9_5_IRQHandler 0000001A

Symbol: EXINT9_5_IRQHandler
   Definitions
      At line 303 in file startup_at32a403a.s
   Uses
      At line 82 in file startup_at32a403a.s
      At line 226 in file startup_at32a403a.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 284 in file startup_at32a403a.s
   Uses
      At line 63 in file startup_at32a403a.s
      At line 207 in file startup_at32a403a.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 164 in file startup_at32a403a.s
   Uses
      At line 44 in file startup_at32a403a.s
      At line 165 in file startup_at32a403a.s

I2C1_ERR_IRQHandler 0000001A

Symbol: I2C1_ERR_IRQHandler
   Definitions
      At line 312 in file startup_at32a403a.s
   Uses
      At line 91 in file startup_at32a403a.s
      At line 235 in file startup_at32a403a.s

I2C1_EVT_IRQHandler 0000001A

Symbol: I2C1_EVT_IRQHandler
   Definitions
      At line 311 in file startup_at32a403a.s
   Uses
      At line 90 in file startup_at32a403a.s
      At line 234 in file startup_at32a403a.s

I2C2_ERR_IRQHandler 0000001A

Symbol: I2C2_ERR_IRQHandler
   Definitions
      At line 314 in file startup_at32a403a.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 93 in file startup_at32a403a.s
      At line 237 in file startup_at32a403a.s

I2C2_EVT_IRQHandler 0000001A

Symbol: I2C2_EVT_IRQHandler
   Definitions
      At line 313 in file startup_at32a403a.s
   Uses
      At line 92 in file startup_at32a403a.s
      At line 236 in file startup_at32a403a.s

I2C3_ERR_IRQHandler 0000001A

Symbol: I2C3_ERR_IRQHandler
   Definitions
      At line 342 in file startup_at32a403a.s
   Uses
      At line 121 in file startup_at32a403a.s
      At line 265 in file startup_at32a403a.s

I2C3_EVT_IRQHandler 0000001A

Symbol: I2C3_EVT_IRQHandler
   Definitions
      At line 341 in file startup_at32a403a.s
   Uses
      At line 120 in file startup_at32a403a.s
      At line 264 in file startup_at32a403a.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 169 in file startup_at32a403a.s
   Uses
      At line 45 in file startup_at32a403a.s
      At line 170 in file startup_at32a403a.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 159 in file startup_at32a403a.s
   Uses
      At line 43 in file startup_at32a403a.s
      At line 160 in file startup_at32a403a.s

PVM_IRQHandler 0000001A

Symbol: PVM_IRQHandler
   Definitions
      At line 281 in file startup_at32a403a.s
   Uses
      At line 60 in file startup_at32a403a.s
      At line 204 in file startup_at32a403a.s

PendSV_Handler 00000016



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: PendSV_Handler
   Definitions
      At line 192 in file startup_at32a403a.s
   Uses
      At line 55 in file startup_at32a403a.s
      At line 193 in file startup_at32a403a.s

RTCAlarm_IRQHandler 0000001A

Symbol: RTCAlarm_IRQHandler
   Definitions
      At line 321 in file startup_at32a403a.s
   Uses
      At line 100 in file startup_at32a403a.s
      At line 244 in file startup_at32a403a.s

RTC_IRQHandler 0000001A

Symbol: RTC_IRQHandler
   Definitions
      At line 283 in file startup_at32a403a.s
   Uses
      At line 62 in file startup_at32a403a.s
      At line 206 in file startup_at32a403a.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 147 in file startup_at32a403a.s
   Uses
      At line 42 in file startup_at32a403a.s
      At line 148 in file startup_at32a403a.s

SDIO1_IRQHandler 0000001A

Symbol: SDIO1_IRQHandler
   Definitions
      At line 329 in file startup_at32a403a.s
   Uses
      At line 108 in file startup_at32a403a.s
      At line 252 in file startup_at32a403a.s

SDIO2_IRQHandler 0000001A

Symbol: SDIO2_IRQHandler
   Definitions
      At line 340 in file startup_at32a403a.s
   Uses
      At line 119 in file startup_at32a403a.s
      At line 263 in file startup_at32a403a.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 315 in file startup_at32a403a.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 94 in file startup_at32a403a.s
      At line 238 in file startup_at32a403a.s

SPI2_I2S2EXT_IRQHandler 0000001A

Symbol: SPI2_I2S2EXT_IRQHandler
   Definitions
      At line 316 in file startup_at32a403a.s
   Uses
      At line 95 in file startup_at32a403a.s
      At line 239 in file startup_at32a403a.s

SPI3_I2S3EXT_IRQHandler 0000001A

Symbol: SPI3_I2S3EXT_IRQHandler
   Definitions
      At line 331 in file startup_at32a403a.s
   Uses
      At line 110 in file startup_at32a403a.s
      At line 254 in file startup_at32a403a.s

SPI4_IRQHandler 0000001A

Symbol: SPI4_IRQHandler
   Definitions
      At line 343 in file startup_at32a403a.s
   Uses
      At line 122 in file startup_at32a403a.s
      At line 266 in file startup_at32a403a.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 183 in file startup_at32a403a.s
   Uses
      At line 52 in file startup_at32a403a.s
      At line 184 in file startup_at32a403a.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 196 in file startup_at32a403a.s
   Uses
      At line 56 in file startup_at32a403a.s
      At line 197 in file startup_at32a403a.s

TAMPER_IRQHandler 0000001A

Symbol: TAMPER_IRQHandler
   Definitions
      At line 282 in file startup_at32a403a.s
   Uses
      At line 61 in file startup_at32a403a.s
      At line 205 in file startup_at32a403a.s

TMR1_BRK_TMR9_IRQHandler 0000001A




ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

Symbol: TMR1_BRK_TMR9_IRQHandler
   Definitions
      At line 304 in file startup_at32a403a.s
   Uses
      At line 83 in file startup_at32a403a.s
      At line 227 in file startup_at32a403a.s

TMR1_CH_IRQHandler 0000001A

Symbol: TMR1_CH_IRQHandler
   Definitions
      At line 307 in file startup_at32a403a.s
   Uses
      At line 86 in file startup_at32a403a.s
      At line 230 in file startup_at32a403a.s

TMR1_OVF_TMR10_IRQHandler 0000001A

Symbol: TMR1_OVF_TMR10_IRQHandler
   Definitions
      At line 305 in file startup_at32a403a.s
   Uses
      At line 84 in file startup_at32a403a.s
      At line 228 in file startup_at32a403a.s

TMR1_TRG_HALL_TMR11_IRQHandler 0000001A

Symbol: TMR1_TRG_HALL_TMR11_IRQHandler
   Definitions
      At line 306 in file startup_at32a403a.s
   Uses
      At line 85 in file startup_at32a403a.s
      At line 229 in file startup_at32a403a.s

TMR2_GLOBAL_IRQHandler 0000001A

Symbol: TMR2_GLOBAL_IRQHandler
   Definitions
      At line 308 in file startup_at32a403a.s
   Uses
      At line 87 in file startup_at32a403a.s
      At line 231 in file startup_at32a403a.s

TMR3_GLOBAL_IRQHandler 0000001A

Symbol: TMR3_GLOBAL_IRQHandler
   Definitions
      At line 309 in file startup_at32a403a.s
   Uses
      At line 88 in file startup_at32a403a.s
      At line 232 in file startup_at32a403a.s

TMR4_GLOBAL_IRQHandler 0000001A

Symbol: TMR4_GLOBAL_IRQHandler
   Definitions
      At line 310 in file startup_at32a403a.s
   Uses
      At line 89 in file startup_at32a403a.s



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 233 in file startup_at32a403a.s

TMR5_GLOBAL_IRQHandler 0000001A

Symbol: TMR5_GLOBAL_IRQHandler
   Definitions
      At line 330 in file startup_at32a403a.s
   Uses
      At line 109 in file startup_at32a403a.s
      At line 253 in file startup_at32a403a.s

TMR6_GLOBAL_IRQHandler 0000001A

Symbol: TMR6_GLOBAL_IRQHandler
   Definitions
      At line 334 in file startup_at32a403a.s
   Uses
      At line 113 in file startup_at32a403a.s
      At line 257 in file startup_at32a403a.s

TMR7_GLOBAL_IRQHandler 0000001A

Symbol: TMR7_GLOBAL_IRQHandler
   Definitions
      At line 335 in file startup_at32a403a.s
   Uses
      At line 114 in file startup_at32a403a.s
      At line 258 in file startup_at32a403a.s

TMR8_BRK_TMR12_IRQHandler 0000001A

Symbol: TMR8_BRK_TMR12_IRQHandler
   Definitions
      At line 323 in file startup_at32a403a.s
   Uses
      At line 102 in file startup_at32a403a.s
      At line 246 in file startup_at32a403a.s

TMR8_CH_IRQHandler 0000001A

Symbol: TMR8_CH_IRQHandler
   Definitions
      At line 326 in file startup_at32a403a.s
   Uses
      At line 105 in file startup_at32a403a.s
      At line 249 in file startup_at32a403a.s

TMR8_OVF_TMR13_IRQHandler 0000001A

Symbol: TMR8_OVF_TMR13_IRQHandler
   Definitions
      At line 324 in file startup_at32a403a.s
   Uses
      At line 103 in file startup_at32a403a.s
      At line 247 in file startup_at32a403a.s

TMR8_TRG_HALL_TMR14_IRQHandler 0000001A

Symbol: TMR8_TRG_HALL_TMR14_IRQHandler



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 325 in file startup_at32a403a.s
   Uses
      At line 104 in file startup_at32a403a.s
      At line 248 in file startup_at32a403a.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler
   Definitions
      At line 332 in file startup_at32a403a.s
   Uses
      At line 111 in file startup_at32a403a.s
      At line 255 in file startup_at32a403a.s

UART5_IRQHandler 0000001A

Symbol: UART5_IRQHandler
   Definitions
      At line 333 in file startup_at32a403a.s
   Uses
      At line 112 in file startup_at32a403a.s
      At line 256 in file startup_at32a403a.s

UART7_IRQHandler 0000001A

Symbol: UART7_IRQHandler
   Definitions
      At line 353 in file startup_at32a403a.s
   Uses
      At line 136 in file startup_at32a403a.s
      At line 276 in file startup_at32a403a.s

UART8_IRQHandler 0000001A

Symbol: UART8_IRQHandler
   Definitions
      At line 354 in file startup_at32a403a.s
   Uses
      At line 137 in file startup_at32a403a.s
      At line 277 in file startup_at32a403a.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 317 in file startup_at32a403a.s
   Uses
      At line 96 in file startup_at32a403a.s
      At line 240 in file startup_at32a403a.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 318 in file startup_at32a403a.s
   Uses
      At line 97 in file startup_at32a403a.s
      At line 241 in file startup_at32a403a.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols


USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 319 in file startup_at32a403a.s
   Uses
      At line 98 in file startup_at32a403a.s
      At line 242 in file startup_at32a403a.s

USART6_IRQHandler 0000001A

Symbol: USART6_IRQHandler
   Definitions
      At line 352 in file startup_at32a403a.s
   Uses
      At line 135 in file startup_at32a403a.s
      At line 275 in file startup_at32a403a.s

USBFSWakeUp_IRQHandler 0000001A

Symbol: USBFSWakeUp_IRQHandler
   Definitions
      At line 322 in file startup_at32a403a.s
   Uses
      At line 101 in file startup_at32a403a.s
      At line 245 in file startup_at32a403a.s

USBFS_H_CAN1_TX_IRQHandler 0000001A

Symbol: USBFS_H_CAN1_TX_IRQHandler
   Definitions
      At line 299 in file startup_at32a403a.s
   Uses
      At line 78 in file startup_at32a403a.s
      At line 222 in file startup_at32a403a.s

USBFS_L_CAN1_RX0_IRQHandler 0000001A

Symbol: USBFS_L_CAN1_RX0_IRQHandler
   Definitions
      At line 300 in file startup_at32a403a.s
   Uses
      At line 79 in file startup_at32a403a.s
      At line 223 in file startup_at32a403a.s

USBFS_MAPH_IRQHandler 0000001A

Symbol: USBFS_MAPH_IRQHandler
   Definitions
      At line 349 in file startup_at32a403a.s
   Uses
      At line 132 in file startup_at32a403a.s
      At line 272 in file startup_at32a403a.s

USBFS_MAPL_IRQHandler 0000001A

Symbol: USBFS_MAPL_IRQHandler
   Definitions



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

      At line 350 in file startup_at32a403a.s
   Uses
      At line 133 in file startup_at32a403a.s
      At line 273 in file startup_at32a403a.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 179 in file startup_at32a403a.s
   Uses
      At line 47 in file startup_at32a403a.s
      At line 180 in file startup_at32a403a.s

WWDT_IRQHandler 0000001A

Symbol: WWDT_IRQHandler
   Definitions
      At line 280 in file startup_at32a403a.s
   Uses
      At line 59 in file startup_at32a403a.s
      At line 203 in file startup_at32a403a.s

XMC_IRQHandler 0000001A

Symbol: XMC_IRQHandler
   Definitions
      At line 328 in file startup_at32a403a.s
   Uses
      At line 107 in file startup_at32a403a.s
      At line 251 in file startup_at32a403a.s

__user_initial_stackheap 0000001C

Symbol: __user_initial_stackheap
   Definitions
      At line 377 in file startup_at32a403a.s
   Uses
      At line 375 in file startup_at32a403a.s
Comment: __user_initial_stackheap used once
90 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000400

Symbol: Heap_Size
   Definitions
      At line 24 in file startup_at32a403a.s
   Uses
      At line 28 in file startup_at32a403a.s
      At line 381 in file startup_at32a403a.s

Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 14 in file startup_at32a403a.s
   Uses
      At line 17 in file startup_at32a403a.s
      At line 380 in file startup_at32a403a.s

__Vectors_Size 00000184

Symbol: __Vectors_Size
   Definitions
      At line 142 in file startup_at32a403a.s
   Uses
      At line 39 in file startup_at32a403a.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 150 in file startup_at32a403a.s
   Uses
      At line 151 in file startup_at32a403a.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 149 in file startup_at32a403a.s
   Uses
      At line 153 in file startup_at32a403a.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 374 in file startup_at32a403a.s
   Uses
      None
Comment: __use_two_region_memory unused
3 symbols
443 symbols in table
