#ifndef __ANOPTV8PAR_H
#define __ANOPTV8PAR_H
#include "AnoPTv8.h" 

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


void AnoPTv8ParFrameAnl(const _un_frame_v8 *p);
int	AnoPTv8ParGetCount(void);
void AnoPTv8ParRegister(const _st_par_info * _pi);
const _st_par_info * AnoPTv8ParGetInfo(uint16_t parid);
uint8_t AnoPTv8GetParamType(uint16_t parid);
float AnoPTv8ParGetVal(uint16_t parid);
void anoPTv8ParSetVal(uint16_t parid, float val);

#endif
