#ifndef __AD2S1212_SPI_H
#define __AD2S1212_SPI_H

#include "at32a403a_wk_config.h"

/* 寄存器映射 */
#define POSITIONMSB        0x80    // 位置寄存器高字节 (D15至D8)
#define POSITIONLSB        0x81    // 位置寄存器低字节 (D7至D0)
#define VELOCITYMSB        0x82    // 速度寄存器高字节 (D15至D8)
#define VELOCITYLSB        0x83    // 速度寄存器低字节 (D7至D0)
#define LOSTHRES           0x88    // LOS门限值 (D7至D0)
#define DOSORTHRES         0x89    // DOS超量程阈值 (D7至D0)
#define DOSMISTHRES        0x8A    // DOS失配阈值 (D7至D0)
#define DOSRSTMXTHRES      0x8B    // DOS复位最大阈值 (D7至D0)
#define DOSRSTMITHRES      0x8C    // DOS复位最小阈值 (D7至D0)
#define LOTHITHRES         0x8D    // LOT上限 (D7至D0)
#define LOTLOTHRES         0x8E    // LOT下限 (D7至D0)
#define EXFREQUENCY        0x91    // 激励频率 (D7至D0)
#define CONTROL            0x92    // 控制寄存器 (D7至D0)
#define SOFTRESET          0xF0    // 软复位 (只写)
#define FAULT              0xFF    // 故障 (只读)

/* 操作GPIO定义 */
#define AD2S_RESET_H        gpio_bits_set(RDC_Reset_GPIO_PORT, RDC_Reset_PIN)     //复位信号高
#define AD2S_RESET_L        gpio_bits_reset(RDC_Reset_GPIO_PORT, RDC_Reset_PIN)   //复位信号低

//片选。低电平有效逻辑输入。CS保持低电平时，器件使能。
#define PCS_H               gpio_bits_set(RDC_CS_GPIO_PORT, RDC_CS_PIN)       //片选
#define PCS_L               gpio_bits_reset(RDC_CS_GPIO_PORT, RDC_CS_PIN)     //片选

//边沿触发的逻辑输入。SOE引脚为高电平时，该引脚用作并行数据输入DB7至DB0的帧同步信号和输入使能信号。
//CS和WR/FSYNC保持低电平时，输入缓冲器使能。
//SOE引脚为低电平时，WR/FSYNC引脚用作串行数据总线的帧同步信号和使能信号。
#define NWR_H               gpio_bits_set(RDC_WR_GPIO_PORT, RDC_WR_PIN)       //WR使能信号
#define NWR_L               gpio_bits_reset(RDC_WR_GPIO_PORT, RDC_WR_PIN)     //

//采样结果。逻辑输入。SAMPLE信号发生高电平至低电平转换后，数据从位置和速度积分器传输到位置和速度
//寄存器，故障寄存器也会进行更新。
#define SMAPLE_H            gpio_bits_set(RDC_SAMPLE_GPIO_PORT, RDC_SAMPLE_PIN)   //采样SAMPLE信号
#define SMAPLE_L            gpio_bits_reset(RDC_SAMPLE_GPIO_PORT, RDC_SAMPLE_PIN) //采样SAMPLE信号

#define RES0_H              1
#define RES0_L              0 
#define RES1_H              1
#define RES1_L              0
//由外部电平设置，目前为RES0=0，RES1=0，分辨率12位，位置5.3LSB

#define A0_H                gpio_bits_set(RDC_A0_GPIO_PORT, RDC_A0_PIN)       //A0管脚控制
#define A0_L                gpio_bits_reset(RDC_A0_GPIO_PORT, RDC_A0_PIN)     //A0管脚控制

#define A1_H                gpio_bits_set(RDC_A1_GPIO_PORT, RDC_A1_PIN)       //A1管脚控制
#define A1_L                gpio_bits_reset(RDC_A1_GPIO_PORT, RDC_A1_PIN)     //A1管脚控制

//信号降级。逻辑输出。当旋变输入(正弦或余弦)超过规定的DOS正弦/余弦阈值时，或者当正弦输入电压与余弦
//输入电压之间出现幅度失配时，就会检测到信号降级(DOS)。DOS由DOS引脚为逻辑低电平来表示。参见信号降
//级检测部分。 
#define DOS                 gpio_input_data_bit_read(RDC_IN_DOS_GPIO_PORT, RDC_IN_DOS_PIN)     // DOS错误状态读取
//跟踪丢失。逻辑输出。LOT由LOT引脚为逻辑低电平(不闩锁)来表示。参见位置跟踪检测丢失部分。
#define LOT                 gpio_input_data_bit_read(RDC_IN_LOT_GPIO_PORT, RDC_IN_LOT_PIN)     // LOT错误状态读取

//配置模式
#define Mode_POSIT          0x00    // 位置读取模式
#define Mode_SPEED          0x01    // 速度读取模式
#define Mode_COFIG          0x03    // 寄存器配置模式 (A0=1, A1=1)

#define TMRx_CVAL          TMR3->cval  //正交编码器定时器值计数寄存器

/* 自检结果结构体定义 */
typedef struct {
    uint8_t lot_status;                    // LOT引脚状态：0=故障(低电平)，1=正常(高电平)
    uint8_t dos_status;                    // DOS引脚状态：0=故障(低电平)，1=正常(高电平)
    uint8_t fault_register;                // 故障寄存器值
    uint8_t fault_count;                   // 检测到的故障数量
} AD2S1210_SelfTestResult_t;

//函数声明
void AD2S1210_Init(void);               // 初始化
void AD2S1210_REFAULT(void);            // 复位错误标志
void AD2S1210_RESET(void);              // 硬件复位
void AD2S1210_Delay(uint32_t nCount);   // 延时函数
void AD2S1210_WRITE(uint8_t addr,uint8_t data);    // 寄存器写入
uint8_t AD2S1210_READ(uint8_t addr);    // 寄存器读取
uint16_t AD2S1210_CommRead(void);       // 普通模式读取（位置或速度）
void AD2S2S1210_ModeCfg(uint8_t ModeConfig);  // 模式配置
float AD2S1210_GetRotationSpeed(uint8_t unit);

/********************读取压榨时序 开始 ********************/
// 8.192MHz，tCK=122ns
#define TCK_NS         122
#define NOP_7NS        20
#define NOP_CNT(ns)    ((ns + NOP_7NS - 1) / NOP_7NS)

// 时序点需要的NOP数 (计算过程: 2*TCK_NS + 20 = 2*122 + 20 = 264ns ≈ 14*20ns)
#define T16_NOP   14      // SAMPLE脉冲宽度
#define T6_NOP    1       // CS上升到下降
#define T31_NOP   0       // CS下降到WR/FSYNC下降 (<5ns不需要延时)
#define T34_NOP   1       // WR/FSYNC上升到下降
#define T23_NOP   1       // WR/FSYNC下降到SDO解除高阻
#define T29_NOP   1       // WR/FSYNC上升到SDO高阻

// NOP宏展开
#define NOP1  __NOP()
#define NOP2  NOP1; NOP1
#define NOP3  NOP2; NOP1
#define NOP4  NOP3; NOP1
#define NOP5  NOP4; NOP1
#define NOP6  NOP5; NOP1
#define NOP7  NOP6; NOP1
#define NOP8  NOP7; NOP1
#define NOP9  NOP8; NOP1
#define NOP10 NOP9; NOP1
#define NOP11 NOP10; NOP1
#define NOP12 NOP11; NOP1
#define NOP13 NOP12; NOP1
#define NOP14 NOP13; NOP1
#define NOP15 NOP14; NOP1
#define NOP16 NOP15; NOP1
#define NOP17 NOP16; NOP1
#define NOP18 NOP17; NOP1
#define NOP19 NOP18; NOP1
#define NOP20 NOP19; NOP1
#define NOP21 NOP20; NOP1
#define NOP22 NOP21; NOP1
#define NOP23 NOP22; NOP1
#define NOP24 NOP23; NOP1
#define NOP25 NOP24; NOP1
#define NOP26 NOP25; NOP1
#define NOP27 NOP26; NOP1
#define NOP28 NOP27; NOP1
#define NOP29 NOP28; NOP1
#define NOP30 NOP29; NOP1
#define NOP31 NOP30; NOP1
#define NOP32 NOP31; NOP1
#define NOP33 NOP32; NOP1
#define NOP34 NOP33; NOP1
#define NOP35 NOP34; NOP1
#define NOP36 NOP35; NOP1
#define NOP37 NOP36; NOP1
#define NOP38 NOP37; NOP1
#define NOP39 NOP38; NOP1
#define NOP40 NOP39; NOP1

#define INSERT_NOP(n)  NOP##n


// 位置初始化功能
uint8_t AD2S1210_InitHardwareFromSPI(tmr_type* timer_instance, uint16_t align_offset);

// 角度偏移对齐相关函数
uint8_t Sensor_InitAndCalibrateOffset(float *offset_rad_out);
float PositionSensorENC_Update_20KHz_Optimized(void);

// 自检功能相关函数
void AD2S1210_ReadStatusPins(uint8_t* lot_status, uint8_t* dos_status);
uint8_t AD2S1210_ReadFaultRegister(void);
void AD2S1210_ClearFaultRegister(void);
uint8_t AD2S1210_SelfTest(AD2S1210_SelfTestResult_t* self_test_result);





/********************读取压榨时序 结束 ********************/







#endif
