/**
 * @file PT1000_table.h
 * @brief PT1000温度传感器查找表和转换函数
 *
 * 本模块为PT1000温度传感器提供查找表和转换函数。
 * 包含高效的二分查找算法和线性插值算法，用于精确的ADC值到温度转换。
 */

#ifndef __PT1000_TABLE_H
#define __PT1000_TABLE_H

#include "at32a403a.h"

/**
 * @brief PT1000查找表大小
 */
#define PT1000_TABLE_SIZE 311

/**
 * @brief PT1000表的温度范围定义
 */
#define PT1000_MIN_TEMP   -50    /**< 表中最低温度 (°C) */
#define PT1000_MAX_TEMP   260    /**< 表中最高温度 (°C) */
#define PT1000_TEMP_STEP  1      /**< 表中温度步长 (°C) */

/**
 * @brief PT1000查找表（对应温度的ADC值）
 *
 * 此表将温度从-50°C到260°C以1°C步长映射到对应的ADC值（0-4095）。
 * 表存储在ROM中。
 */
extern const uint16_t pt1000_adc_table[PT1000_TABLE_SIZE];

/**
 * @brief 使用二分查找和线性插值将ADC值转换为温度
 *
 * 此函数对PT1000查找表执行高效的二分查找以找到最接近的温度值，
 * 然后使用线性插值进行精确的温度计算。
 * 超出范围时返回边界温度值。
 *
 * @param adc_value 要转换的ADC值 (0-4095)
 * @return 摄氏度温度值（带小数）
 */
float PT1000_ADC_to_Temperature(uint16_t adc_value);

/**
 * @brief 使用二分查找和线性插值将温度转换为ADC值
 *
 * 此函数对PT1000查找表执行高效的二分查找以找到最接近的ADC值，
 * 然后使用线性插值进行精确的ADC值计算。
 * 超出范围时返回边界ADC值。
 *
 * @param temperature 摄氏度温度值
 * @return ADC值 (0-4095)
 */
uint16_t PT1000_Temperature_to_ADC(float temperature);

#endif /* __PT1000_TABLE_H */
