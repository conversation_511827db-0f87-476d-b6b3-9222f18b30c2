
/*
//===========================================================================
//
// 文件名:    SysCtl_GlobalVar.c
//
// 用途:        系统全局结构体变量定义
//
//===========================================================================
*/
//#include "DSP2833x_Device.h"
#include "at32a403a.h"
#include "SysCtl_AllHeaders.h"

//#pragma DATA_SECTION(DMABuf1,"DMARAML4");//对数据进行储存，对程序储存是#pragma CODE_SECTION
uint16_t HMIBuffer[2000] = {0};
uint32_t  uDMA_Addr = 0;
volatile uint16_t DMABuf1[BUF_SIZE];//DMA数据缓冲区
volatile uint16_t *DMADest1 = (volatile uint16_t *)0x200000;
volatile uint16_t *DMASource1;
volatile uint16_t *pARMParamRdFlag = (volatile uint16_t *)0x2803fc;//ARM芯片参数准备就绪标志，DSP读到0xCE认为ARM就绪
volatile uint16_t *RdDSPtoARM = (volatile uint16_t *)0x2803fd;  //DSP芯片主循环正常，循环写0xfa
volatile float *pOffsetRam = ((float*) (HMIBuffer + 928));  //读零漂设置值起始地址
volatile uint16_t *pWaveParamStart = &DMABuf1[0x20];//(volatile uint16_t *)0x200020; //波形参数传递起始地址DSP->ARM

volatile float *pRunSpeed = (float*)&DMABuf1[0x42];
volatile float *pEstSpeed = (float*)&DMABuf1[0x44];
//volatile float *pDsptoArmUab = (float*)&DMABuf1[0x6A];
//volatile float *pDsptoArmUbc = (float*)&DMABuf1[0x6C];

volatile float *pFaultWaveStart = (float*)&DMABuf1[0];

TypeSysMoore SysMoore = SysMoore_DEFAULTS;//系统状态机
TypeSysRatedParameter SysRatedParameter = SysRatedParameter_DEFAULTS;//系统额定值
union SysErrIndex_REG SysErrIndexReg;//系统故障索引
union SysCtlMode_REG SysCtlModeREG;//系统控制模式
union SysCtl_REG  SysCtlReg;//系统控制指令，包括启动、急停等
union DSPFault_REG DSPFaultCodeReg;//DSP故障
union ARMErr_REG ARMFaultReg;//ARM故障
//COMMUN sInComm = COMM_DEFAULTS;//通讯
SCOPEDATA *ScopeDATAUpLoad = (SCOPEDATA*)&HMIBuffer[1384];//csv地址映射

TypeSysOffsetParameter SysSampOffset = SysOffsetParameter_DEFAULTS;//零漂参数结构体
TypeSysSamScaParameter SysSamScaParameter = SysSamScaParameter_DEFAULTS;//采样整定结构体
TypeSysProParameter SysProParamReg = SysProParameter_DEFAULTS;//保护参数结构体
TypeSysBaseValue SysBaseValue = SysBaseValue_DEFAULTS;//基值结构体
TypeSamDSPtoARM  SysSamDSPtoARM = TypeSamDSPtoARM_DEFAULTS;//采样数据由DSP传至ARM
TypeAnalogInput AnalogInput = AnalogInput_DEFAULTS;//模拟采样输入结构体
//TypeAnalogInput:头文件定义的结构体名称；AnalogInput：程序中的结构体变量名；AnalogInput_DEFAULTS：结构体初始值2018.4.14
TypeSysEnvironParameter SysEnviConfg = SysEnvironParameter_DEFAULTS;//系统运行环境参数定义
TypeDAChoose DAOut = TypeDAChoose_DEFAULTS;//DA数据选择
TypeSYSCTRL SysCtlParameter = SYSCtrl_DEFAULTS;//系统控制结构体

TypeRotorSpeedpara RotorSpeedclc = RotorSpeedpara_DEFAULTS;
//===========算法=======
TypeVoltBaseCal  SysVoltBase = SysVoltBaseCal_DEFAULTS; //系统电压基波计算
// TypeSysConTestParam  SysConTest = SysConTestParam_DEFAULTS;//电抗器测试
// TypeSysVVVFCal  SysVVVFCtrl = VVVFVal_DEFAULTS;  //VVVF控制结构体




//=========================矢量控制====================

//VCANDSLVCORPARA VC_SLVC_PARAMETER = VCANDSLVCORPARA_DEFAULTS;//电机矢量控制结构体
//VECTOR_ST SpeedTest = VECTOR_ST_DEFAULTS;//矢量控制转速测试

/*
CLARKE Vol_clarke = CLARKE_DEFAULTS;//CLARKE变换
ICLARKE Vol_iclarke = ICLARKE_DEFAULTS;
IPARK Vol_ipark = IPARK_DEFAULTS;
CLARKE Cur_clarke = CLARKE_DEFAULTS;
//CLARKE OutVolt_clarke = CLARKE_DEFAULTS;
PARK Cur_park = PARK_DEFAULTS;//PARK变换
*/

//LPF_PLL2  LPF_W = LPF_PLL_DEFAULTS2;//低通滤波
//LPF_PLL3 Spd_LPF = LPF_PLL_DEFAULTS3;
//RATELIMIT Ratelimit = RATELIMIT_DEFAULTS;//限幅
//INTERG Interg_phsAlpha = INTERG_DEFAULTS;
//INTERG Interg_phsBeta = INTERG_DEFAULTS;
//INTERG Interg_Deta = INTERG_DEFAULTS;
//INTERG Interg_Vector = INTERG_DEFAULTS;
//INTERG interg_pf = INTERG_DEFAULTS;
//PIREG SpeedTestPI = PIREG_DEFAULTS;//PI参数寄存器
//PIREG SpeedControl = PIREG_DEFAULTS;
//PIREG CurrentControl_M = PIREG_DEFAULTS;
//PIREG CurrentControl_T = PIREG_DEFAULTS;
//PIREG PhrPI = PIREG_DEFAULTS;
//SLIP Slipspeed = SLIP_DEFAULTS;
//EXCITCURRENT ExcitingCurrent = EXCITCURRENT_DEFAULTS;
//VECTORCONTROL Vector_Control = VECTORCONTROL_DEFAULTS;
//SFILTER1OR CurrentFilter_D = SFILTER1OR_DEFAULTS;//d、q轴电流滤波
//SFILTER1OR CurrentFilter_Q = SFILTER1OR_DEFAULTS;
//PARAMETERTEST Parameter_Test = PARAMETERTEST_DEFAULTS;
//RMS_OUT Rms_Para_Vol = RMS_OUT_DEFAULTS;
//RMS_OUT Rms_Para_Cur = RMS_OUT_DEFAULTS;
//RMS_OUT Rms_Grid_PhaseVolt = RMS_OUT_DEFAULTS;
//SPEEDKPKI SpeedKpKi = SPEEDKPKI_DEFAULTS;//速度环PI
//SFILTER1OR ErFilter1_Alpha = SFILTER1OR_DEFAULTS;
//SFILTER1OR ErFilter2_Alpha = SFILTER1OR_DEFAULTS;
//SFILTER1OR ErFilter3_Alpha = SFILTER1OR_DEFAULTS;
//SFILTER1OR PhrCurFilter_Alpha = SFILTER1OR_DEFAULTS;
//SFILTER1OR ErFilter1_Beta = SFILTER1OR_DEFAULTS;
//SFILTER1OR ErFilter2_Beta = SFILTER1OR_DEFAULTS;
//SFILTER1OR ErFilter3_Beta = SFILTER1OR_DEFAULTS;
//SFILTER1OR PhrCurFilter_Beta = SFILTER1OR_DEFAULTS;
//INTERG Interg_PhrVolAlpha = INTERG_DEFAULTS;
//INTERG Interg_PhrVolBeta = INTERG_DEFAULTS;
//INERTIA1OR Inertia_PhrVolAlpha = INERTIA1OR_DEFAULTS;
//INERTIA1OR Inertia_PhrVolBeta = INERTIA1OR_DEFAULTS;
//SPDESTCURVOL SpeedTest_CurVol = SPDESTCURVOL_DEFAULTS;
//CLARKE CurPhr_clarke = CLARKE_DEFAULTS;
//CLARKE VolPhr_clarke = CLARKE_DEFAULTS;
//PIREG SpeedTestPI_CurVol = PIREG_DEFAULTS;
//INTERG Interg_CurVol = INTERG_DEFAULTS;
////float fFrAct;
uint16_t uInterCount = 0;
uint32_t uSoftStartCount = 0;
//uint16_t uGridCount = 0;
uint16_t uDirCount=0;
uint16_t uDirCountMax=0;
//VCTRLPARAM VectorCtrlPAMA = VCTRLPARAM_DEFAULTS;
//VCTRLPARAM SynVectorCtrlPAMA = VCTRLPARAM_DEFAULTS;
Vector_ctrl SynMotorVc = Vector_ctrl_DEFAULTS;//矢量控制结构体声明,初始化矢量控制
float machineAngle;
float eleAngle;
//===========================================================================
// No more.
//===========================================================================
