<html>
<head>
<META http-equiv="Content-Type" content="text/html">
<style type="text/css">
		h1, h2, h3, h4, h5, h6 {
			font-family : segoe ui;
			color : black;
			background-color : #EDE7D9;
			padding: 0.3em;
		}

		h1 {
			font-size: 1.2em;
		}		

		h2 {
			font-size: 1.2em;
		}

		body {
			font-family : segoe ui;
		}

		td, th {
			padding: 0.5em;
			text-align : left;
			width: 10em;
		}
		th {
			background-color : #EEEEEE;

		}
		th.column1, td.column1 {
			text-align: left;
			width : auto;
		}
		table {
			width : 100%;
			font-size: 0.9em;
		}

		.DRC_summary_header {
			padding-bottom : 0.1em;
			border : 0px solid black;
			width: 100%;
			align: left;
		}

		.DRC_summary_header_col1,
		.DRC_summary_header_col2, 
		.DRC_summary_header_col3 {
			color : black;
			font-size:100%;
			padding : 0em;
			padding-top : 0.2em;
			padding-bottom 0.2em;
			border : 0px solid black;
			vertical-align: top;
			text-align: left;
		}

		.DRC_summary_header_col1 {
			font-weight: bold;
			width: 9em;
		}

		.DRC_summary_header_col2 {
			width: 0.1em;
		
		}

		.DRC_summary_header_col3 {
			width : auto;
		}

		.header_holder {
		    Width = 100%;
		    border = 0px solid green;
		    padding = 0;
		}


		.front_matter, .front_matter_column1, .front_matter_column2, .front_matter_column3
		{
			left : 0;
			top : 0;
			padding: 0em;
			padding-top : 0.1em;
			border : 0px solid black;
			width : 100%;
			vertical-align: top;
			text-align: left;
		}

		.front_matter_column1 {
			width : 8em;
			font-weight: bold;
		}

		.front_matter_column2 {
			width: 0.1em;
		}

		.front_matter_column3 {
			width : auto;
		}

		.total_column1, .total_column {
			font-weight : bold;
		}
		.total_column1 {
			text-align : left;
		}
		.warning, .error {
			color : red;
			font-weight : bold;
		}
		tr.onmouseout_odd {
			background-color : #white;
		}
		tr.onmouseout_even { 
			background-color : #FAFAFA;
		}
		tr.onmouseover_odd, tr.onmouseover_even { 
			background-color : #EEEEEE;
		} 
		a:link, a:visited, .q a:link,.q a:active,.q {
			color: #21489e; 
		}
		a:link.callback, a:visited.callback { 
			color: #21489e;
		}
		a:link.customize, a:visited.customize {
			color: #C0C0C0;
			position: absolute; 
			right: 10px;
		}	
		p.contents_level1 {
			font-weight : bold;
			font-size : 110%;
			margin : 0.5em;
		}
		p.contents_level2 {
			position : relative;
			left : 20px;
			margin : 0.5em;
		}
	</style><script type="text/javascript">
		function coordToMils(coord) {
			var number = coord / 10000;
			
			if (number != number.toFixed(3))
				number = number.toFixed(3);

			return number + 'mil'
		}

		function coordToMM(coord) {
			var number = 0.0254 * coord / 10000;
			
			if (number != number.toFixed(4))
				number = number.toFixed(4);
			
			return number + 'mm'
		}
	
		function convertCoord(coordNode, units) {
			for (var i = 0; i < coordNode.childNodes.length; i++) {
				coordNode.removeChild(coordNode.childNodes[i]);
			}

			var coord = coordNode.getAttribute('value');
			if (coord != null) {
				if (units == 'mm') {
					textNode = document.createTextNode(coordToMM(coord));
					coordNode.appendChild(textNode);
				} else if (units == 'mil') {
					textNode = document.createTextNode(coordToMils(coord));		
					coordNode.appendChild(textNode);	
				}
			}
		}
	
		function convertUnits(unitNode, units) {
			for (var i = 0; i < unitNode.childNodes.length; i++) {
				unitNode.removeChild(unitNode.childNodes[i]);		
			}
		
			textNode = document.createTextNode(units); 
			unitNode.appendChild(textNode);
		}
	
		function changeUnits(radio_input, units) {
			if (radio_input.checked) {
			
				var elements = document.getElementsByName('coordinate');
				if (elements) {
					for (var i = 0; i < elements.length; i++) {
						convertCoord(elements[i], units);
					}
				}
	
				var elements = document.getElementsByName('units');
				if (elements) {
					for (var i = 0; i < elements.length; i++) {
						convertUnits(elements[i], units);
					}
				}
			}
		}
	</script><title>Design Rule Verification Report</title>
</head>
<body onload=""><img ALT="Altium" src="
			file://C:\Users\<USER>\Documents\Altium\AD23\Templates\AD_logo.png
		"><h1>Design Rule Verification Report</h1>
<table class="header_holder">
<td class="column1">
<table class="front_matter">
<tr class="front_matter">
<td class="front_matter_column1">Date:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">2025/8/10</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Time:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">12:26:29</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Elapsed Time:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">00:00:01</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Filename:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3"><a href="file:///C:\Users\<USER>\Documents\Altium\154\800W\800W\J-SM-800W_MCU_V1.0_Project\J-SM-800W_MCU_V1.0.PcbDoc" class="file"><acronym title="C:\Users\<USER>\Documents\Altium\154\800W\800W\J-SM-800W_MCU_V1.0_Project\J-SM-800W_MCU_V1.0.PcbDoc">C:\Users\<USER>\Documents\Altium\154\800W\800W\J-SM-800W_MCU_V1.0_Project\J-SM-800W_MCU_V1.0.PcbDoc</acronym></a></td>
</tr>
</table>
</td>
<td class="column2">
<table class="DRC_summary_header">
<tr>
<td class="DRC_summary_header_col1">Warnings:</td>
<td class="DRC_summary_header_col2"></td>
<td class="DRC_summary_header_col3">0</td></tr>
<tr>
<td class="DRC_summary_header_col1">Rule Violations:</td>
<td class="DRC_summary_header_col2"></td>
<td class="DRC_summary_header_col3">0</td></tr>
</table>
</td>
</table><a name="IDG00KEZB4NEG2CVRV1MS25RJLXNTYX4EMCHWJSGLTRSQORHIN0Q2F"><h2>Summary</h2></a><table>
<tr>
<th class="column1">Warnings</th>
<th class="column2">Count</th>
</tr>
<tr>
<td style="font-weight : bold; text-align : right" class="column1">Total</td>
<td style="font-weight : bold" class="column2">0</td>
</tr>
</table><br><table>
<tr>
<th class="column1">Rule Violations</th>
<th class="column2">Count</th>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDVC203FMZZG1XI33EQ2305KPGMP4KJYBEL1400SGMTQU0Z2VMLQ2K">Clearance Constraint (Gap=19.685mil) (InPoly),(InPoly)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDXGRN0ZNKCJJ3NIYYJPLYCEWYXG4EGYZKNH0R2PEFPD3LGF1WDKOC">Clearance Constraint (Gap=10mil) (InPoly),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDKRPKMQP3FP2UCWPGHVKMNNIJHPYV5ZRIKPB0YICWQXNVAIZPOLQI">Clearance Constraint (Gap=23.622mil) (InNet('PE')),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDERVX5LAGZL4DJNHJQLK4B00ODPPSZD3ILMZTTEL0TU34T4AHW2B">Clearance Constraint (Gap=5.984mil) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDUMN2WOYCQGJKH3YVPVNGK0OSYH4MKUKFIGYIQHI4P451NHNWAEUB">Short-Circuit Constraint (Allowed=No) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDLWXLLJRDRWBJHJ2BIB5E5PHFKOURUIVSYXOO32IJ4R1PM3SUBYDF">Un-Routed Net Constraint ( (All) )</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDN4152J20CMOKNN2QIJI02GKFBMGUA3V5A0OLBWJUKIEX4DEXMYE">Modified Polygon (Allow modified: No), (Allow shelved: No)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDJXZFE5C2NAR2KBC1NTYXMA511EPBIFURPXJYAUKDTO2UYBWPM3TD">Width Constraint (Min=3.937mil) (Max=393.701mil) (Preferred=10mil) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDRB4JZ353X4Y1O2WLPS301PDCGBTTIQILTKG2CGHISQ0NOK0C2YKM">Power Plane Connect Rule(Relief Connect )(Expansion=20mil) (Conductor Width=10mil) (Air Gap=10mil) (Entries=4) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDK24ZR2NDAZZZD2ZFBFH3DBSQRBIHCP5F5NB33IBMPIIC1VNCICKI">Hole Size Constraint (Min=1mil) (Max=393.701mil) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDQITGAVTX3G2IKG5ZKWSTSWXXOICWL1WJTDSCTIFSFHS5NJPD5ZIL">Hole To Hole Clearance (Gap=0mil) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDC0P022VFOLHRLN3SLH22E0ZNMNKZOIBEANF3AZHTTVG2TJTX4OXP">Minimum Solder Mask Sliver (Gap=0mil) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDUM3TMMDIYUALPQSBAWNXOZDODNBGOSB33HO0SZLWY1A3JTO5QGOH">Silk To Solder Mask (Clearance=0mil) (IsPad),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDJPC2GZURBXZRNETHOORJSHPGLP5CN1X1MS1DDQHXUMOPQCRFIF">Silk to Silk (Clearance=0mil) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDJAB1GKX04R2FLFWQUS43WQUN5E4LGPUFMFTLBZKI1RIRANITZTOP">Net Antennae (Tolerance=0mil) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#ID3WNH1WQT0XBLBKDW4JIQIQ0TBKD4FPNS5YSHHAI52KIG3N4QR5OD">Height Constraint (Min=0mil) (Max=1000mil) (Prefered=500mil) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr>
<td style="font-weight : bold; text-align : right" class="column1">Total</td>
<td style="font-weight : bold" class="column2">0</td>
</tr>
</table><br></body>
</html>
