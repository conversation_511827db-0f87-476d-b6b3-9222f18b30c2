
/* define to prevent recursive inclusion -------------------------------------*/
#ifndef __USB_CONF_H
#define __USB_CONF_H

#ifdef __cplusplus
extern "C" {
#endif

#include "at32a403a.h"
#include "stdio.h"

/* private includes ----------------------------------------------------------*/
/* add user code begin private includes */

/* add user code end private includes */

/* private define ------------------------------------------------------------*/
/* add user code begin private define */

/* add user code end private define */

/**
  * @brief usb endpoint max num define
  */
#ifndef USB_EPT_MAX_NUM
#define USB_EPT_MAX_NUM                   8  /*!< usb device support endpoint number */
#endif

/**
  * @brief usb buffer extend to 768-1280 bytes
  */
#define USB_BUFFER_SIZE_EX  /*!< usb enable extend buffer */


/**
  * @brief auto malloc usb endpoint buffer
  */
/*  #define USB_EPT_AUTO_MALLOC_BUFFER  */  /*!< usb auto malloc endpoint tx and rx buffer */


#ifndef USB_EPT_AUTO_MALLOC_BUFFER
/**
  * @brief user custom endpoint buffer
  *        EPTn_TX_ADDR, EPTn_RX_ADDR must less than usb buffer size
  */

/* ept0 tx start address 0x40, size 0x40,
   so rx start address is 0x40 + 0x40 = 0x80 */
#define EPT0_TX_ADDR                     0x40    /*!< usb endpoint 0 tx buffer address offset */
#define EPT0_RX_ADDR                     0x180    /*!< usb endpoint 0 rx buffer address offset */

#define EPT1_TX_ADDR                     0x1C0    /*!< usb endpoint 1 tx buffer address offset */
#define EPT1_RX_ADDR                     0x440   /*!< usb endpoint 1 rx buffer address offset */

#define EPT2_TX_ADDR                     0x480   /*!< usb endpoint 2 tx buffer address offset */
#define EPT2_RX_ADDR                     0x4C0   /*!< usb endpoint 2 rx buffer address offset */

#define EPT3_TX_ADDR                     0x500    /*!< usb endpoint 3 tx buffer address offset */
#define EPT3_RX_ADDR                     0x500    /*!< usb endpoint 3 rx buffer address offset */

#define EPT4_TX_ADDR                     0x500    /*!< usb endpoint 4 tx buffer address offset */
#define EPT4_RX_ADDR                     0x500    /*!< usb endpoint 4 rx buffer address offset */

#define EPT5_TX_ADDR                     0x500    /*!< usb endpoint 5 tx buffer address offset */
#define EPT5_RX_ADDR                     0x500    /*!< usb endpoint 5 rx buffer address offset */

#define EPT6_TX_ADDR                     0x500    /*!< usb endpoint 6 tx buffer address offset */
#define EPT6_RX_ADDR                     0x500    /*!< usb endpoint 6 rx buffer address offset */

#define EPT7_TX_ADDR                     0x500    /*!< usb endpoint 7 tx buffer address offset */
#define EPT7_RX_ADDR                     0x500    /*!< usb endpoint 7 rx buffer address offset */

#endif

/**
  * @brief support winusb
  */
/*  #define USBD_SUPPORT_WINUSB    1  */

void usb_delay_ms(uint32_t ms);
void usb_delay_us(uint32_t us);

/* add user code begin exported functions */

/* add user code end exported functions */

/**
  * @}
  */

/**
  * @}
  */

#ifdef __cplusplus
}
#endif

#endif

