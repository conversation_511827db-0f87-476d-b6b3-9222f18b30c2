#include "AnoPTv8Run.h"
#include "AnoPTv8Par.h"
#include "AnoPTv8Cmd.h"

//接收缓冲区
_recvST recvBuf;
//当前解析的帧
_st_frame_v8 AnoPTv8CurrentAnlFrame;

// 大缓冲区实例
_largeBufST AnoPTv8LargeTxBuf = {0};

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/*
1毫秒周期执行函数，用来检查是否有数据需要发送以及其他需要定期检查的功能
*/
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**
 * @brief 发送线程函数
 * @details 每隔1ms调用一次，用于处理发送缓冲区中的数据
 * @note 统一使用大缓冲区发送
 */
void AnoPTv8TxRunThread1ms(void)
{
    // 只处理大缓冲区发送
    AnoPTv8TxLargeBufSend();
}


/**
 * @brief 发送线程函数 - 主循环单帧版本  
 * @details 适合在主循环中调用，只处理大缓冲区发送
 * @note 统一使用大缓冲区，简化逻辑
 */
void AnoPTv8TxMainLoopSingle(void)
{
    // 只处理大缓冲区发送
    AnoPTv8TxLargeBufSend();
}


/**
 * @brief 发送线程函数 - 主循环全帧扫描版本
 * @details 适合在主循环中调用，只处理大缓冲区发送
 * @note 统一使用大缓冲区，简化逻辑
 */
void AnoPTv8TxMainLoopAll(void)
{
    // 只处理大缓冲区发送
    AnoPTv8TxLargeBufSend();
}


/**
 * @brief 发送线程函数 - 主循环批量发送版本
 * @details 只处理大缓冲区发送
 * @note 统一使用大缓冲区，简化逻辑
 */
void AnoPTv8TxMainLoopBatch(void)
{
    // 只处理大缓冲区发送
    AnoPTv8TxLargeBufSend();
}


/**
 * @brief 大缓冲区发送函数
 * @details 发送大缓冲区中累积的所有帧数据
 * @note 适合主循环调用，一次性发送所有累积的帧
 */
void AnoPTv8TxLargeBufSend(void)
{
    if(AnoPTv8LargeTxBuf.readyToSend || AnoPTv8LargeTxBuf.writePos > 0)
    {
        // 发送大缓冲区中的所有数据
        AnoPTv8HwSendBytes(AnoPTv8LargeTxBuf.buffer, AnoPTv8LargeTxBuf.writePos);
        
        // 重置缓冲区状态
        AnoPTv8LargeTxBuf.writePos = 0;
        AnoPTv8LargeTxBuf.dataLength = 0;
        AnoPTv8LargeTxBuf.readyToSend = 0;
    }
}


/**
 * @brief 强制发送大缓冲区
 * @details 强制发送大缓冲区中的数据，无论是否达到发送条件
 * @note 用于关键时刻或缓冲区满时的强制发送
 */
void AnoPTv8TxLargeBufFlush(void)
{
    if(AnoPTv8LargeTxBuf.writePos > 0)
    {
        AnoPTv8HwSendBytes(AnoPTv8LargeTxBuf.buffer, AnoPTv8LargeTxBuf.writePos);
        AnoPTv8LargeTxBuf.writePos = 0;
        AnoPTv8LargeTxBuf.dataLength = 0;
        AnoPTv8LargeTxBuf.readyToSend = 0;
    }
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/*
对接收到的帧进行校验
*/
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
static int8_t anoPTv8FrameCheck(const _un_frame_v8 *p)
{
    uint8_t sumcheck = 0, addcheck = 0;
    if (p->frame.head != ANOPTV8_FRAME_HEAD)
        return 0;
    for (uint16_t i = 0; i < (ANOPTV8_FRAME_HEADLEN + p->frame.datalen); i++)
    {
        sumcheck += p->rawBytes[i];
        addcheck += sumcheck;
    }
    if (sumcheck == p->frame.sumcheck && addcheck == p->frame.addcheck)
        return 1;
    else
        return 0;
}
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/*
对校验通过的数据帧进行解析，执行相应的功能
*/
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
static void anoPTv8FrameAnl(const _un_frame_v8 *p)
{
    if(p->frame.ddevid != ANOPTV8_MYDEVID && p->frame.ddevid != ANOPTV8DEVID_ALL)
        return;

    AnoPTv8CurrentAnlFrame = p->frame;
    //根据帧ID执行不同的功能
    switch (p->frame.frameid)
    {
    case 0xC0:
    case 0xC1:
        //命令相关帧
        AnoPTv8CmdFrameAnl(p);
        break;
    case 0xE0:
    case 0xE1:
        //参数读写相关帧
        AnoPTv8ParFrameAnl(p);
        break;
    case 0xF0:
    {
        // AnoIapGetFrame(&p->frame);
        // AnoDTSendStr("anoDTFrameAnl", 1);
    }
    break;
    }
}
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/*
串口等通信方式下，每收到1字节数据，调用本函数一次，将接收到的数据传入进行数据解析
*/
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**
 * @brief 批量接收数据处理函数
 * @param buf 数据缓冲区指针
 * @param len 数据长度
 * @details 极简状态机处理接收到的数据，消除复杂的批量拷贝和索引计算
 * @note 优化要点：
 *       - 使用简单的switch-case状态机
 *       - 逐字节处理，消除复杂索引回退
 *       - 消除边界计算和批量拷贝复杂性
 *       - 保持与原版本完全相同的功能和接口
 */
void AnoPTv8RecvBytes(uint8_t *buf, uint16_t len)
{
    if(buf == NULL || len == 0) return;
    
    for(uint16_t i = 0; i < len; i++)
    {
        uint8_t dat = buf[i];
        
        switch(recvBuf.recvSta)
        {
            case 0: // 第一步：搜索帧头
                if(dat == ANOPTV8_FRAME_HEAD)
                {
                    recvBuf.recvSta = 1;
                    recvBuf.recvDataLen = 1;
                    recvBuf.dataBuf.rawBytes[0] = dat;
                }
                break;
                
            case 1: // 第二步：接收帧头数据
                recvBuf.dataBuf.rawBytes[recvBuf.recvDataLen++] = dat;
                if(recvBuf.recvDataLen >= ANOPTV8_FRAME_HEADLEN)
                {
                    // 检查数据长度是否合法
                    if(recvBuf.dataBuf.frame.datalen >= ANOPTV8_FRAME_MAXDATALEN)
                    {
                        recvBuf.recvSta = 0; // 数据长度非法，重新开始
                    }
                    else
                    {
                        recvBuf.recvSta = 2;
                    }
                }
                break;
                
            case 2: // 第三步：接收数据内容
                recvBuf.dataBuf.rawBytes[recvBuf.recvDataLen++] = dat;
                if(recvBuf.recvDataLen >= ANOPTV8_FRAME_HEADLEN + recvBuf.dataBuf.frame.datalen)
                {
                    recvBuf.recvSta = 3;
                }
                break;
                
            case 3: // 第四步：接收校验字节
                recvBuf.dataBuf.rawBytes[recvBuf.recvDataLen++] = dat;
                recvBuf.dataBuf.frame.sumcheck = dat;
                recvBuf.recvSta = 4;
                break;
                
            case 4: // 第五步：接收附加校验字节
                recvBuf.dataBuf.rawBytes[recvBuf.recvDataLen++] = dat;
                recvBuf.dataBuf.frame.addcheck = dat;
                recvBuf.recvSta = 0; // 重置状态，准备处理下一帧
                
                // 一帧数据接收完毕，进行校验和解析
                if(anoPTv8FrameCheck(&recvBuf.dataBuf))
                {
                    anoPTv8FrameAnl(&recvBuf.dataBuf);
                }
                break;
                
            default: // 异常状态处理
                recvBuf.recvSta = 0; // 重新开始
                break;
        }
    }
}


/**
 * @brief 单字节接收数据处理函数
 * @param dat 接收到的单字节数据
 * @details 兼容原有接口，内部调用批量处理函数
 */
void AnoPTv8RecvOneByte(uint8_t dat)
{
    AnoPTv8RecvBytes(&dat, 1);
}


