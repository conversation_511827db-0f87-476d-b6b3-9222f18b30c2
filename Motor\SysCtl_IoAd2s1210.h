//=============================================================================
// Filename：Resolver.h
//
// Description:	Resolver specific definitions and declaration
//
// Current Version：1.0
//
// Target: 2014-101 Ver:2.0
//
//-----------------------------------------------------------------------------
// Revision History: 1.0
//-----------------------------------------------------------------------------
// Author: VC
// $Release Date: 2013.11.01
//-----------------------------------------------------------------------------
// Copyright (c) 2012-2014
// TianJin ANYI Mechanical & Electrical Technology Development Co.,Ltd.
// All Rights Reserved.
//=============================================================================

#ifndef RESOLVER_H_
#define RESOLVER_H_
#include "at32a403a.h"
//=======Mode Select==========
#define  POSITION	 0
#define	 VELOCITY	 1
#define  CONFIG		 2

//=======Register Address=======
#define	 POSITIONMSB		0x80
#define	 POSITIONLSB		0x81
#define	 VELOCITYMSB		0x82
#define	 VELOCITYLSB		0x83
#define	 LOSTHRES			0x88
#define	 DOSORTHRES			0x89
#define	 DOSMISTHRES		0x8A
#define	 DOSRSTMXTHRES	    0x8B
#define	 DOSRSTMITHRES	    0x8C
#define	 LOTHITHRES			0x8D
#define	 LOTLOTHRES			0x8E
#define	 EXFREQUENCY		0x91
#define	 CONTROL			0x92
#define	 SOFTRESET			0xF0
#define	 FAULTREG			0xFF

//==========FCW==========
#define  F2KHz              0x08
#define  F5KHz              0x14
#define  F10KHz             0x28
#define  F15KHz             0x3C
#define  F20KHz             0x50

/*以下GPIO编号根据硬件设计更改*/
#define  Ad2s_WRSet()           gpio_bits_set(GPIOD,GPIO_PINS_0)   //GpioDataRegs.GPASET.bit.GPIO23 = 1 
#define  Ad2s_WRClear()         gpio_bits_reset(GPIOD,GPIO_PINS_0) //GpioDataRegs.GPACLEAR.bit.GPIO23 = 1

#define  Ad2s_SampleSet()       gpio_bits_set(GPIOE,GPIO_PINS_4)  //GpioDataRegs.GPASET.bit.GPIO13 = 1
#define  Ad2s_SampleClear()     gpio_bits_reset(GPIOE,GPIO_PINS_4)  //GpioDataRegs.GPACLEAR.bit.GPIO13 = 1

//#define  Ad2s_ResetSet()        GpioDataRegs.GPBSET.bit.GPIO50 = 1
//#define  Ad2s_ResetClear()      GpioDataRegs.GPBCLEAR.bit.GPIO50 = 1

// #define  Ad2s_CONFIGMODE()     GpioDataRegs.GPBSET.bit.GPIO58 = 1; \
//                                GpioDataRegs.GPBSET.bit.GPIO49 = 1

#define  Ad2s_CONFIGMODE()     gpio_bits_set(GPIOE,GPIO_PINS_5); \
                               gpio_bits_set(GPIOE,GPIO_PINS_6)  

#define  Ad2s_POSTIONGMODE()   gpio_bits_reset(GPIOE,GPIO_PINS_5); \
                               gpio_bits_reset(GPIOE,GPIO_PINS_6)  	

#define  Ad2s_VELOCITYGMODE()  gpio_bits_reset(GPIOE,GPIO_PINS_5); \
                               gpio_bits_set(GPIOE,GPIO_PINS_6) 

#define  Ad2s_DISABLE()        gpio_bits_set(GPIOF,GPIO_PINS_9)   //GpioDataRegs.GPBSET.bit.GPIO59 = 1
#define  Ad2s_ENABLE()         gpio_bits_reset(GPIOF,GPIO_PINS_9)  //GpioDataRegs.GPBCLEAR.bit.GPIO59 = 1
/*-----------------------------------------------------------------------------
	Define the structure of the Resolver Object
-----------------------------------------------------------------------------*/
typedef struct {
   float  Speed;                   // Variable: Real time speed
   float  SpeedSum;                // Variable: Total sum of speed data
   float  SpeedOut;                // Variable: Real speed output
   float  SpeedFilterBuf[8];       // Variable: storage speed value
	uint16_t  SpeedFilterBufPointer;   // Variable: Point to the SpeedFilterBuf add
	uint16_t  Angle;    	   	         // Variable: Detected angle
	uint16_t  AngleDelta;              // Variable: angle delta in tow sample time
	 int16_t  AnglePre;                // Variable: Previous angle Data
	 int16_t  AngleOut;                // Variable: Real angle output
	uint16_t  AngleOffset;             // Variable: Angle compensation value
	uint16_t  Fault;                   // Variable: Current fault status
	uint16_t  MotorPairs;              // motor pole pairs
	uint16_t  ResolverPairs;           // resolver pole pairs
   float  FromSpeedDeltAg;         // electric angle delta in tow sample time
	uint16_t  FromSpeedDeltAgdata;     // transfer to sample data reference to 4096
	uint16_t  AgErrCount;              // angle read error accumulation counter
	uint16_t   FirstFlag;
} RESOLVER;

/*-----------------------------------------------------------------------------
	Default initializer for the Resolver object.
-----------------------------------------------------------------------------*/
#define RESOLVER_DEFAULTS  {0, \
							0, \
							0, \
			{0,0,0,0,0,0,0,0}, \
							0, \
							0, \
							0, \
							0, \
							0, \
							0, \
							0, \
							4, \
							1, \
							0, \
							0, \
							0, \
							0, \
}

/*------------------------------------------------------------------------------
	Decoder Function Declaration
------------------------------------------------------------------------------*/
uint16_t Ad2s_GetVelocity();
uint16_t Ad2s_GetPosition();
void   Ad2s_InitDecoder(void);
void   Ad2s_AngleFilter(void);
void   Ad2s_CalculateSpeed(void);
void   Ad2s_AngleCompensation(void);
uint16_t Ad2s_DecoderRead(uint16_t add);
void   Ad2s_DecoderWrite(uint16_t add,uint16_t data);
uint16_t Ad2s_GetAngle();
uint8_t Ad2s_GetFaultGeneralMode();
extern RESOLVER Resolver;

#endif /* RESOLVER_H_ */
