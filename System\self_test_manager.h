/**
 * @file self_test_manager.h
 * @brief 自检模块管理器层和接口抽象层头文件
 * <AUTHOR> @date 2025-07-14
 */

#ifndef __SELF_TEST_MANAGER_H
#define __SELF_TEST_MANAGER_H

#include "at32a403a.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/****************************** 接口抽象层 ******************************/

/**
 * @brief 自检项目枚举
 */
typedef enum {
    SELF_TEST_ITEM_NONE = 0,               // 无效项目
    SELF_TEST_ITEM_ADC_CURRENT = 1,        // ADC电流自检
    SELF_TEST_ITEM_BUS_VOLTAGE = 2,        // 母线电压自检
    SELF_TEST_ITEM_MOTOR_TEMP = 3,         // 电机温度自检
    SELF_TEST_ITEM_BOARD_TEMP = 4,         // 板上温度自检
    SELF_TEST_ITEM_AD2S1210 = 5,           // 旋变自检
    SELF_TEST_ITEM_WATCHDOG = 6,           // 看门狗自检
    SELF_TEST_ITEM_MAX = 7                 // 最大项目数
} self_test_item_t;

/**
 * @brief 自检结果状态枚举
 */
typedef enum {
    SELF_TEST_RESULT_PASS = 0,         // 自检通过
    SELF_TEST_RESULT_FAIL,             // 自检失败
    SELF_TEST_RESULT_WARNING,          // 自检警告
    SELF_TEST_RESULT_NOT_TESTED,       // 未测试
    SELF_TEST_RESULT_NOT_SUPPORTED     // 不支持
} self_test_result_t;

/**
 * @brief 自检项详细结果结构体
 */
typedef struct {
    self_test_item_t item;             // 自检项目
    self_test_result_t result;         // 自检结果
    uint32_t error_code;               // 错误代码
    uint32_t test_time_ms;             // 测试耗时
} self_test_item_result_t;

/**
 * @brief 自检函数原型
 * @param result [out] 自检结果结构体指针
 * @return true: 执行成功, false: 执行失败
 */
typedef bool (*self_test_func_t)(self_test_item_result_t* result);

/**
 * @brief 自检项注册结构体
 */
typedef struct {
    self_test_item_t item;             // 自检项目
    self_test_func_t test_func;        // 自检函数指针
    bool enabled;                      // 是否启用
    uint32_t timeout_ms;               // 超时时间
    const char* name;                  // 项目名称
} self_test_item_config_t;

/****************************** 管理器层 ******************************/

/**
 * @brief 自检管理器配置结构体
 */
typedef struct {
    uint32_t max_total_time_ms;        // 最大总测试时间
    bool stop_on_first_fail;           // 遇到第一个失败是否停止
} self_test_manager_config_t;

/**
 * @brief 自检管理器状态枚举
 */
typedef enum {
    SELF_TEST_MANAGER_IDLE = 0,        // 空闲状态
    SELF_TEST_MANAGER_RUNNING,         // 运行中
    SELF_TEST_MANAGER_COMPLETED,       // 完成
    SELF_TEST_MANAGER_ABORTED          // 中止
} self_test_manager_state_t;

/**
 * @brief 自检管理器结果汇总结构体
 */
typedef struct {
    self_test_manager_state_t state;   // 管理器状态
    uint32_t total_items;              // 总测试项目数
    uint32_t passed_items;             // 通过项目数
    uint32_t failed_items;             // 失败项目数
    uint32_t warning_items;            // 警告项目数
    uint32_t not_tested_items;         // 未测试项目数
    uint32_t total_time_ms;            // 总测试时间
    self_test_item_result_t* item_results; // 各项目详细结果数组
} self_test_manager_result_t;

/****************************** 管理器API ******************************/

/**
 * @brief 初始化自检管理器
 * @param config 管理器配置
 * @return true: 初始化成功, false: 初始化失败
 */
bool SelfTestManager_Init(const self_test_manager_config_t* config);

/**
 * @brief 注册自检项目
 * @param item_config 自检项目配置
 * @return true: 注册成功, false: 注册失败
 */
bool SelfTestManager_RegisterItem(const self_test_item_config_t* item_config);

/**
 * @brief 启用/禁用指定自检项目
 * @param item 自检项目
 * @param enabled 是否启用
 * @return true: 设置成功, false: 设置失败
 */
bool SelfTestManager_EnableItem(self_test_item_t item, bool enabled);

/**
 * @brief 执行所有已注册的自检项目
 * @param result [out] 自检结果汇总
 * @return true: 执行完成, false: 执行失败
 */
bool SelfTestManager_RunAll(self_test_manager_result_t* result);

/**
 * @brief 执行指定的自检项目
 * @param item 自检项目
 * @param result [out] 自检结果
 * @return true: 执行完成, false: 执行失败
 */
bool SelfTestManager_RunSingle(self_test_item_t item, self_test_item_result_t* result);

/**
 * @brief 获取管理器当前状态
 * @return 管理器状态
 */
self_test_manager_state_t SelfTestManager_GetState(void);

/**
 * @brief 中止正在进行的自检
 * @return true: 中止成功, false: 中止失败
 */
bool SelfTestManager_Abort(void);

/**
 * @brief 获取自检项目名称字符串
 * @param item 自检项目
 * @return 项目名称字符串
 */
const char* SelfTestManager_GetItemName(self_test_item_t item);

/**
 * @brief 获取自检结果状态字符串
 * @param result 自检结果
 * @return 结果状态字符串
 */
const char* SelfTestManager_GetResultString(self_test_result_t result);

/**
 * @brief 反初始化自检管理器
 */
void SelfTestManager_Deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* __SELF_TEST_MANAGER_H */
