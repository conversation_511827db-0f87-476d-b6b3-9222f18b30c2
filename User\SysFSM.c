/**********************************************************
  * @file     SysFSM.c
  * @brief    系统状态机文件
  * <AUTHOR>
  * @date     2024-01-05
  * @version  V1.0.0
  * @note     包含系统状态管理和定时任务功能
************************************************************/
#include "SysFSM.h"
#include "AnoPTv8.h"
#include "adc_pmsm.h"
#include "at32a403a_wk_config.h"
#include "wk_system.h"
#include "SysCtl_AllHeaders.h"
#include "Sys_TimerEvent.h"
#include "HWInterface.h"
#include "MotorData.h"
#include "ad2s1212_spi.h"
/*----------------------- 第一部分：定时事件驱动 -----------------------*/

uint32_t test_time=0;
uint16_t jiaodu =0;
static uint8_t ms_cnt = 0;  // 毫秒计数器，用于10ms周期任务

void Timer_Tasks_Execute(void)
{
    // 检查并执行500us任务
    if(gTimerFlag.flag_500us) {
        //UpdateF1FocData();
        jiaodu=AD2S1210_CommRead();
        //gpio_bits_toggle(GPIOD, GPIO_PINS_2);
        ADC_PMSM_ProcessRectifiedAverage();  // 计算三相整流平均值
        //UpdateF1FocData();
        gTimerFlag.flag_500us = 0;
    }
    // 检查并执行1ms任务
    if(gTimerFlag.flag_1ms) {
        // 添加1ms周期需要执行的任务

			UpdateF1FocData();

            

        if(++ms_cnt >= 10) {
        // 10ms周期任务

            ms_cnt = 0;
        }

        gTimerFlag.flag_1ms = 0;
    }

    // 检查并执行1s任务
    if(gTimerFlag.flag_1s) {
				test_time++;

        gTimerFlag.flag_1s = 0;
    }

    // 检查并执行1min任务
    if(gTimerFlag.flag_1min) {
        gTimerFlag.flag_1min = 0;
    }
}

/*----------------------- 第二部分：状态机核心结构 -----------------------*/

/* 全局变量定义 */
SystemStatus_TypeDef g_SystemStatus = {0};  // 初始化
StateMachineContext_TypeDef g_state_machine_ctx = {0};  // 初始化为0
