#include "SysCtl_AllHeaders.h"

/***********************************************************
//函数名称：fnSysVoltBaseInit
//函数功能：基波电压处理初始化
//函数参数：无
************************************************************/
void fnSysVoltBaseInit(TypeVoltBaseCal *p)
{
  p->fUu_base = 0.0;
  p->fUv_base = 0.0;
  p->fUw_base = 0.0;
  p->uCarrCount = uPeriodCounter;
  p->uUu_Fib = 0;
  p->uUv_Fib = 0;
  p->uUw_Fib = 0;
  // 将传递变量扩展为6相
  p->fUu1_base = 0.0;
  p->fUv1_base = 0.0;
  p->fUw1_base = 0.0;
  p->uCarrCount = uPeriodCounter;
  p->uUu2_Fib = 0;
  p->uUv2_Fib = 0;
  p->uUw2_Fib = 0;
}
/***********************************************************
//函数名称：fnSysVoltBaseReset
//函数功能：基波电压处理复位
//函数参数：无
************************************************************/
void fnSysVoltBaseReset(TypeVoltBaseCal *p)
{
  p->fUu_base = 0.0;
  p->fUv_base = 0.0;
  p->fUw_base = 0.0;
  //p->uCarrCount = uPeriodCounter;
  p->uUu_Fib = 0;
  p->uUv_Fib = 0;
  p->uUw_Fib = 0;

    p->fUu1_base = 0.0;
    p->fUv1_base = 0.0;
    p->fUw1_base = 0.0;
    p->fUu2_base = 0.0;
    p->fUv2_base = 0.0;
    p->fUw2_base = 0.0;

    p->uUu1_Fib = 0;
    p->uUv1_Fib = 0;
    p->uUw1_Fib = 0;
    p->uUu2_Fib = 0;
    p->uUv2_Fib = 0;
    p->uUw2_Fib = 0;
}
/***********************************************************
//函数名称：fnSysVoltBaseCalc
//函数功能：基波电压计算接口float->int
//函数参数：无
************************************************************/
void fnSysVoltBaseCalc(TypeVoltBaseCal *p)
{

//	if(p->fUu_base>0.95)
//	{p->fUu_base=0.95;}
//	if(p->fUu_base<-0.95)
//	{p->fUu_base=-0.95;}
//    if(p->fUv_base>0.95)
//    {p->fUv_base=0.95;}
//    if(p->fUv_base<-0.95)
//    {p->fUv_base=-0.95;}
//    if(p->fUw_base>0.95)
//    {p->fUw_base=0.95;}
//    if(p->fUw_base<-0.95)
//    {p->fUw_base=-0.95;}
	//==============以下是之前使用的程序===================//
    // extern  float OPEN_flag;
    // extern  float C2OPEN_flag;
//    


	//InvModuWave(p->uUu1_Fib,p->uUv1_Fib,p->uUw1_Fib,p->uUu2_Fib,p->uUv2_Fib,p->uUw2_Fib);  //需要扩展到双三相
//=============三相断相故障控制算法
    // if (OPEN_flag == 1)

    //   {
    //       //DEP_DSP_GPWM_ENABLE=0;      //封锁第一通道的PWM信号
    //   }
    // else
    // {
    //     //DEP_DSP_GPWM_ENABLE = 1;
    // }

	//DEP_DSP_GPWM_ENABLE1 = 1;
//===========三相容错故障控制算法

//=============C2相容错算法
//	 if (C2OPEN_flag > 0)
//
//	      {
//	          DEP_DSP_GPWM_ENABLE1 = 0;      //封锁第一通道的PWM信号
//	      }
//	    else
//	    {
//	        DEP_DSP_GPWM_ENABLE1 = 1;
//	    }
//
//	    DEP_DSP_GPWM_ENABLE = 1;

//=============C2相容错算法
	//DEP_DSP_GPWM_ENABLE = 3;

}
