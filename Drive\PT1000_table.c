/**
 * @file PT1000_table.c
 * @brief PT1000温度传感器查找表和转换函数实现
 */

#include "PT1000_table.h"

/**
 * @brief PT1000查找表（对应温度的ADC值）
 *
 * 此表将温度从-50°C到260°C以1°C步长映射到对应的ADC值（0-4095）。
 * 表存储在ROM中。
 */
const uint16_t pt1000_adc_table[PT1000_TABLE_SIZE] = {
    1826, 1835, 1843, 1851, 1860, 1868, 1876, 1885, 1893, 1901,  // 下标  0-  9:  -50°C 到  -41°C
    1910, 1918, 1926, 1934, 1943, 1951, 1959, 1967, 1975, 1984,  // 下标 10- 19:  -40°C 到  -31°C
    1992, 2000, 2008, 2016, 2025, 2033, 2041, 2049, 2057, 2065,  // 下标 20- 29:  -30°C 到  -21°C
    2073, 2081, 2089, 2098, 2106, 2114, 2122, 2130, 2138, 2146,  // 下标 30- 39:  -20°C 到  -11°C
    2154, 2162, 2170, 2178, 2186, 2194, 2202, 2210, 2218, 2226,  // 下标 40- 49:  -10°C 到   -1°C
    2234, 2242, 2250, 2257, 2265, 2273, 2281, 2289, 2297, 2305,  // 下标 50- 59:    0°C 到    9°C
    2313, 2321, 2328, 2336, 2344, 2352, 2360, 2367, 2375, 2383,  // 下标 60- 69:   10°C 到   19°C
    2391, 2399, 2406, 2414, 2422, 2430, 2437, 2445, 2453, 2461,  // 下标 70- 79:   20°C 到   29°C
    2468, 2476, 2484, 2491, 2499, 2507, 2514, 2522, 2530, 2537,  // 下标 80- 89:   30°C 到   39°C
    2545, 2553, 2560, 2568, 2575, 2583, 2591, 2598, 2606, 2613,  // 下标 90- 99:   40°C 到   49°C
    2621, 2628, 2636, 2643, 2651, 2658, 2666, 2674, 2681, 2688,  // 下标100-109:   50°C 到   59°C
    2696, 2703, 2711, 2718, 2726, 2733, 2741, 2748, 2756, 2763,  // 下标110-119:   60°C 到   69°C
    2770, 2778, 2785, 2793, 2800, 2807, 2815, 2822, 2829, 2837,  // 下标120-129:   70°C 到   79°C
    2844, 2851, 2859, 2866, 2873, 2881, 2888, 2895, 2903, 2910,  // 下标130-139:   80°C 到   89°C
    2917, 2924, 2932, 2939, 2946, 2953, 2960, 2968, 2975, 2982,  // 下标140-149:   90°C 到   99°C
    2989, 2996, 3004, 3011, 3018, 3025, 3032, 3039, 3047, 3054,  // 下标150-159:  100°C 到  109°C
    3061, 3068, 3075, 3082, 3089, 3096, 3103, 3111, 3118, 3125,  // 下标160-169:  110°C 到  119°C
    3132, 3139, 3146, 3153, 3160, 3167, 3174, 3181, 3188, 3195,  // 下标170-179:  120°C 到  129°C
    3202, 3209, 3216, 3223, 3230, 3237, 3244, 3251, 3258, 3265,  // 下标180-189:  130°C 到  139°C
    3271, 3278, 3285, 3292, 3299, 3306, 3313, 3320, 3327, 3333,  // 下标190-199:  140°C 到  149°C
    3340, 3347, 3354, 3361, 3368, 3375, 3381, 3388, 3395, 3402,  // 下标200-209:  150°C 到  159°C
    3409, 3415, 3422, 3429, 3436, 3442, 3449, 3456, 3463, 3469,  // 下标210-219:  160°C 到  169°C
    3476, 3483, 3490, 3496, 3503, 3510, 3516, 3523, 3530, 3536,  // 下标220-229:  170°C 到  179°C
    3543, 3550, 3556, 3563, 3570, 3576, 3583, 3589, 3596, 3603,  // 下标230-239:  180°C 到  189°C
    3609, 3616, 3622, 3629, 3636, 3642, 3649, 3655, 3662, 3668,  // 下标240-249:  190°C 到  199°C
    3675, 3682, 3688, 3695, 3701, 3708, 3714, 3721, 3727, 3734,  // 下标250-259:  200°C 到  209°C
    3740, 3747, 3753, 3759, 3766, 3772, 3779, 3785, 3792, 3798,  // 下标260-269:  210°C 到  219°C
    3804, 3811, 3817, 3824, 3830, 3836, 3843, 3849, 3856, 3862,  // 下标270-279:  220°C 到  229°C
    3868, 3875, 3881, 3887, 3894, 3900, 3906, 3913, 3919, 3925,  // 下标280-289:  230°C 到  239°C
    3932, 3938, 3944, 3950, 3957, 3963, 3969, 3976, 3982, 3988,  // 下标290-299:  240°C 到  249°C
    3994, 4001, 4007, 4013, 4019, 4025, 4032, 4038, 4044, 4050,  // 下标300-309:  250°C 到  259°C
    4056  // 下标310-310:  260°C 到  260°C
};

/**
 * @brief 使用二分查找和线性插值将ADC值转换为温度
 *
 * 此函数对PT1000查找表执行高效的二分查找以找到最接近的温度值，
 * 然后使用左右两个温度点进行线性插值计算精确的温度值。
 *
 * @param adc_value 要转换的ADC值 (0-4095)
 * @return 摄氏度温度值（带小数）
 */
float PT1000_ADC_to_Temperature(uint16_t adc_value)
{
    // 边界检查：如果小于最小值则返回最小温度
    if (adc_value <= pt1000_adc_table[0]) {
        return (float)PT1000_MIN_TEMP;
    }

    // 边界检查：如果大于最大值则返回最大温度
    if (adc_value >= pt1000_adc_table[PT1000_TABLE_SIZE - 1]) {
        return (float)PT1000_MAX_TEMP;
    }

    // 二分查找找到ADC值所在的区间
    uint16_t left = 0;
    uint16_t right = PT1000_TABLE_SIZE - 1;
    uint16_t mid;

    while (right - left > 1) {
        mid = (left + right) / 2;
        if (pt1000_adc_table[mid] <= adc_value) {
            left = mid;
        } else {
            right = mid;
        }
    }

    // 现在 left 和 right 是包含 adc_value 的两个相邻索引
    // pt1000_adc_table[left] <= adc_value <= pt1000_adc_table[right]

    // 如果ADC值正好匹配左边的值，直接返回对应温度
    if (pt1000_adc_table[left] == adc_value) {
        return (float)(PT1000_MIN_TEMP + left);
    }

    // 如果ADC值正好匹配右边的值，直接返回对应温度
    if (pt1000_adc_table[right] == adc_value) {
        return (float)(PT1000_MIN_TEMP + right);
    }

    // 获取左右两点的ADC值和对应温度
    uint16_t adc_left = pt1000_adc_table[left];
    uint16_t adc_right = pt1000_adc_table[right];
    float temp_left = (float)(PT1000_MIN_TEMP + left);
    float temp_right = (float)(PT1000_MIN_TEMP + right);

    // 线性插值计算精确温度
    // temp = temp_left + (adc_value - adc_left) * (temp_right - temp_left) / (adc_right - adc_left)
    float temperature = temp_left + ((float)(adc_value - adc_left) * (temp_right - temp_left)) / (float)(adc_right - adc_left);

    return temperature;
}

/**
 * @brief 使用二分查找和线性插值将温度转换为ADC值
 *
 * 此函数对PT1000查找表执行高效的二分查找以找到最接近的ADC值，
 * 然后使用左右两个温度点进行线性插值计算精确的ADC值。
 *
 * @param temperature 摄氏度温度值
 * @return ADC值 (0-4095)
 */
uint16_t PT1000_Temperature_to_ADC(float temperature)
{
    // 边界检查：如果温度低于表范围，返回最小ADC值
    if (temperature <= (float)PT1000_MIN_TEMP) {
        return pt1000_adc_table[0];
    }

    // 边界检查：如果温度高于表范围，返回最大ADC值
    if (temperature >= (float)PT1000_MAX_TEMP) {
        return pt1000_adc_table[PT1000_TABLE_SIZE - 1];
    }

    // 计算温度对应的索引（浮点数）
    float index_float = (temperature - (float)PT1000_MIN_TEMP) / (float)PT1000_TEMP_STEP;

    // 如果温度正好是整数度，直接返回对应的ADC值
    if (index_float == (float)((int)index_float)) {
        uint16_t index = (uint16_t)index_float;
        if (index < PT1000_TABLE_SIZE) {
            return pt1000_adc_table[index];
        }
    }

    // 获取左右两个索引
    uint16_t left_index = (uint16_t)index_float;
    uint16_t right_index = left_index + 1;

    // 边界检查
    if (right_index >= PT1000_TABLE_SIZE) {
        return pt1000_adc_table[PT1000_TABLE_SIZE - 1];
    }

    // 获取左右两点的温度和对应ADC值
    float temp_left = (float)(PT1000_MIN_TEMP + left_index * PT1000_TEMP_STEP);
    float temp_right = (float)(PT1000_MIN_TEMP + right_index * PT1000_TEMP_STEP);
    uint16_t adc_left = pt1000_adc_table[left_index];
    uint16_t adc_right = pt1000_adc_table[right_index];

    // 线性插值计算精确ADC值
    // adc = adc_left + (temperature - temp_left) * (adc_right - adc_left) / (temp_right - temp_left)
    float adc_value = (float)adc_left + ((temperature - temp_left) * (float)(adc_right - adc_left)) / (temp_right - temp_left);

    return (uint16_t)(adc_value + 0.5f);  // 四舍五入
}