The lwIP PPP support is based from pppd 2.4.5 (http://ppp.samba.org) with
huge changes to match code size and memory requirements for embedded devices.

Anyway, pppd has a mature codebase for years and the average commit count
is getting low on their Git repository, meaning that we can follow what
is happening on their side and merge what is relevant for lwIP.

So, here is the pppd follow up, so that we don't get away too far from pppd.


== Patch fetched from from pppd Debian packages ==

This has nothing to do with pppd, but we merged some good patch from
Debian and this is a good place to be.

- LCP adaptive echo, so that we don't send LCP echo request if we
  are receiving data from peer, can be enabled by setting PPP_LCP_ADAPTIVE
  to true.
 
- IPCP no/replace default route option, were added in the early stage of
  the ppp port, but it wasn't really helpful and was disabled when adding
  the new API ppp_set_default() call, which gives the lwIP user control over
  which one is the default interface, it was actually a requirement if you
  are doing PPP over PPP (i.e. PPPoL2TP, VPN link, over PPPoE, ADSL link).

- using rp-pppoe pppd exits with EXIT_OK after receiving a timeout waiting
  for PADO due to no modem attached, bug reported to pppd bug tracker, fixed
  in Debian but not in the latest (at the time when the port were started)
  pppd release.


== Commits on pppd ==

2010-03-06 - Document +ipv6 and ipv6cp-accept-local
  e7537958aee79b3f653c601e903cb31d78fb7dcc

Don't care.


2010-03-06 - Install pppol2tp plugins with sane permissions
  406215672cfadc03017341fe03802d1c7294b903

Don't care.


2010-03-07 - pppd: Terminate correctly if lcp_lowerup delayed calling
             fsm_lowerup
  3eb9e810cfa515543655659b72dde30c54fea0a5

Merged 2012-05-17.


2010-03-07 - rp_pppoe: Copy acName and pppd_pppoe_service after option parsing
  cab58617fd9d328029fffabc788020264b4fa91f

Don't care, is a patch for pppd/plugins/rp-pppoe/plugin.c which is not part
of the port.


2010-08-23 - set and reset options to control environment variables
             for scripts.
  2b6310fd24dba8e0fca8999916a162f0a1842a84

We can't fork processes in embedded, therefore all the pppd process run
feature is disabled in the port, so we don't care about the new
"environment variables" pppd feature.


2010-08-23 - Nit: use _exit when exec fails and restrict values to 0-255
             per POSIX.
  2b4ea140432eeba5a007c0d4e6236bd0e0c12ba4

Again, we are not running as a heavy process, so all exit() or _exit() calls
were removed.


2010-08-23 - Fix quote handling in configuration files to be more like shell
             quoting.
  3089132cdf5b58dbdfc2daf08ec5c08eb47f8aca

We are not parsing config file, all the filesystem I/O stuff were disabled
in our port.


2010-08-24 - rp-pppoe: allow MTU to be increased up to 1500
  fd1dcdf758418f040da3ed801ab001b5e46854e7

Only concern changes on RP-PPPoE plugin, which we don't use.


2010-09-11 - chat: Allow TIMEOUT value to come from environment variable
  ae80bf833e48a6202f44a935a68083ae52ad3824

See 2b6310fd24dba8e0fca8999916a162f0a1842a84.


2011-03-05 - pppdump: Fix printfs with insufficient arguments
  7b8db569642c83ba3283745034f2e2c95e459423

pppdump is a ppp tool outside pppd source tree.


2012-05-06 - pppd: Don't unconditionally disable VJ compression under Linux
  d8a66adf98a0e525cf38031b42098d539da6eeb6

Patch for sys-linux.c, which we don't use.


2012-05-20 - Remove old version of Linux if_pppol2tp.h
  c41092dd4c49267f232f6cba3d31c6c68bfdf68d

Not in the port.


2012-05-20 - pppd: Make MSCHAP-v2 cope better with packet loss
  08ef47ca532294eb428238c831616748940e24a2

This is an interesting patch. However it consumes much more memory for
MSCHAP and I am not sure if the benefit worth it. The PPP client can
always start the authentication again if it failed for whatever reason.


2012-05-20 - scripts: Make poff ignore extra arguments to pppd
  18f515f32c9f5723a9c2c912601e04335106534b

Again, we are not running scripts.


2012-05-20 - rp-pppoe plugin: Print leading zeros in MAC address
  f5dda0cfc220c4b52e26144096d729e27b30f0f7

Again, we are not using the RP-PPPoE plugin.


2012-05-20 - pppd: Notify IPv6 up/down as we do for IPv4
  845cda8fa18939cf56e60b073f63a7efa65336fc

This is just a patch that adds plugins hooks for IPv6, the plugin interface
was disabled because we don't have .so plugins in embedded.


2012-05-20 - pppd: Enable IPV6 by default and fix some warnings
  0b6118239615e98959f7e0b4e746bdd197533248

Change on Makefile for IPv6, warnings were already cleared during port.


2012-05-20 - contrib: Fix pppgetpass.gtk compilation
  80a8e2ce257ca12cce723519a0f20ea1d663b14a

Change on Makefile, don't care.


2012-05-20 - pppd: Don't crash if crypt() returns NULL
  04c4348108d847e034dd91066cc6843f60d71731

We are using the PolarSSL DES implementation that does not return NULL.


2012-05-20 - pppd: Eliminate some warnings
  c44ae5e6a7338c96eb463881fe709b2dfaffe568

Again, we are handling compilation warnings on our own.


2012-05-20 - rp-pppoe plugin: Import some fixes from rp-pppoe-3.10
  1817d83e51a411044e730ba89ebdb0480e1c8cd4

Once more, we are not using the RP-PPPoE plugin.


2013-01-23 - pppd: Clarify circumstances where DNS1/DNS2 environment variables are set
  cf2f5c9538b9400ade23446a194729b0a4113b3a

Documentation only.


2013-02-03 - ppp: ignore unrecognised radiusclient configuration directives
  7f736dde0da3c19855997d9e67370e351e15e923

Radius plugin, not in the port.


2013-02-03 - pppd: Take out unused %r conversion completely
  356d8d558d844412119aa18c8e5a113bc6459c7b

Merged 2014-04-15.


2013-02-03 - pppd: Arrange to use logwtmp from libutil on Linux
  9617a7eb137f4fee62799a677a9ecf8d834db3f5

Patch for sys-linux.c, which we don't use.


2013-02-03 - pppdump: Eliminate some compiler warnings
  3e3acf1ba2b3046c072a42c19164788a9e419bd1

pppdump is a ppp tool outside pppd source tree.


2013-02-03 - chat: Correct spelling errors in the man page
  8dea1b969d266ccbf6f3a8c5474eb6dcd8838e3b

Documentation only.


2013-02-03 - pppd: Fix spelling errors in man page
  9e05a25d76b3f83096c661678010320df673df6b

Documentation only.


2013-02-03 - plugins/passprompt: Fix potential out-of-bounds array reference
  8edb889b753056a691a3e4b217a110a35f9fdedb

Plugin patch, we do not have plugins.


2013-02-03 - chat: Fix *roff errors in the man page
  a7c3489eeaf44e83ce592143c7c8a5b5c29f4c48

Documentation only.


2013-03-02 - pppd: Fix man page description of case when remote IP address isn't known
  224841f4799f4f1e2e71bc490c54448d66740f4f

Documentation only.


2013-03-02 - pppd: Add master_detach option
  398ed2585640d198c53e736ee5bbd67f7ce8168e

Option for multilink support, we do not support multilink and this option
is about detaching from the terminal, which is out of the embedded scope.


2013-03-11 - pppd: Default exit status to EXIT_CONNECT_FAILED during connection phase
  225361d64ae737afdc8cb57579a2f33525461bc9

Commented out in our port, and already fixed by a previously applied Debian patch.


2013-03-11 - pppstats: Fix undefined macro in man page
  d16a3985eade5280b8e171f5dd0670a91cba0d39

Documentation only.


2013-05-11 - plugins/radius: Handle bindaddr keyword in radiusclient.conf
  d883b2dbafeed3ebd9d7a56ab1469373bd001a3b

Radius plugin, not in the port.


2013-06-09 - pppoatm: Remove explicit loading of pppoatm kernel module
  52cd43a84bea524033b918b603698104f221bbb7

PPPoATM plugin, not in the port.


2013-06-09 - pppd: Fix segfault in update_db_entry()
  37476164f15a45015310b9d4b197c2d7db1f7f8f

We do not use the samba db.


2013-06-09 - chat: Fix some text that was intended to be literal
  cd9683676618adcee8add2c3cfa3382341b5a1f6

Documentation only.


2013-06-09 - README.pppoe: Minor semantic fix
  b5b8898af6fd3d44e873cfc66810ace5f1f47e17

Documentation only.


2013-06-10 - radius: Handle additional attributes
  2f581cd986a56f2ec4a95abad4f8297a1b10d7e2

Radius plugin, not in the port.


2013-06-10 - chat, pppd: Use \e instead of \\ in man pages
  8d6942415d22f6ca4377340ca26e345c3f5fa5db

Documentation only.


2014-01-02 - pppd: Don't crash if NULL pointer passed to vslprintf for %q or %v
  906814431bddeb2061825fa1ebad1a967b6d87a9

Merged 2014-04-15.


2014-01-02 - pppd: Accept IPCP ConfAck packets containing MS-WINS options
  a243f217f1c6ac1aa7793806bc88590d077f490a

Merged 2014-04-15.


2014-01-02 - config: Update Solaris compiler options and enable CHAPMS and IPV6
  99c46caaed01b7edba87962aa52b77fad61bfd7b

Solaris port, don't care.


2014-01-02 - Update README and patchlevel for 2.4.6 release
  4043750fca36e7e0eb90d702e048ad1da4929418

Just release stuff.


2014-02-18 - pppd: Add option "stop-bits" to set number of serial port stop bits.
  ad993a20ee485f0d0e2ac4105221641b200da6e2

Low level serial port, not in the port.


2014-03-09 - pppd: Separate IPv6 handling for sifup/sifdown
  b04d2dc6df5c6b5650fea44250d58757ee3dac4a

Reimplemented.


2014-03-09 - pppol2tp: Connect up/down events to notifiers and add IPv6 ones
  fafbe50251efc7d6b4a8be652d085316e112b34f

Not in the port.


2014-03-09 - pppd: Add declarations to eliminate compile warnings
  50967962addebe15c7a7e63116ff46a0441dc464

We are handling compilation warnings on our own


2014-03-09 - pppd: Eliminate some unnecessary ifdefs
  de8da14d845ee6db9236ccfddabf1d8ebf045ddb

We mostly did that previously. Anyway, merged 2014-12-24.


2014-08-01 - radius: Fix realms-config-file option
  880a81be7c8e0fe8567227bc17a1bff3ea035943

Radius plugin, not in the port.


2014-08-01 - pppd: Eliminate potential integer overflow in option parsing
  7658e8257183f062dc01f87969c140707c7e52cb

pppd config file parser, not in the port.


2014-08-01 - pppd: Eliminate memory leak with multiple instances of a string option
  b94b7fbbaa0589aa6ec5fdc733aeb9ff294d2656

pppd config file parser, not in the port.


2014-08-01 - pppd: Fix a stack variable overflow in MSCHAP-v2
  36733a891fb56594fcee580f667b33a64b990981

This fixes a bug introduced in 08ef47ca ("pppd: Make MSCHAP-v2 cope better with packet loss").

We didn't merge 08ef47ca ;-)


2014-08-01 - winbind plugin: Add -DMPPE=1 to eliminate compiler warnings
  2b05e22c62095e97dd0a97e4b5588402c2185071

Linux plugin, not in the port.


2014-08-09 - Update README and patchlevel for 2.4.7 release
  6e8eaa7a78b31cdab2edf140a9c8afdb02ffaca5

Just release stuff.


2014-08-10 - abort on errors in subdir builds
  5e90783d11a59268e05f4cfb29ce2343b13e8ab2

Linux Makefile, not in the port.


2014-06-03 - pppd: add support for defaultroute-metric option
  35e5a569c988b1ff865b02a24d9a727a00db4da9

Only necessary for Linux, lwIP does not support route metrics.


2014-12-13 - scripts: Avoid killing wrong pppd
  67811a647d399db5d188a242827760615a0f86b5

pppd helper script, not in the port.


2014-12-20 - pppd: Fix sign-extension when displaying bytes in octal
  5e8c3cb256a7e86e3572a82a75d51c6850efdbdc

Merged 2016-07-02.


2015-03-01 - Suppress false error message on PPPoE disconnect
  219aac3b53d0827549377f1bfe22853ee52d4405

PPPoE plugin, not in the port.


2015-03-01 - Send PADT on PPPoE disconnect
  cd2c14f998c57bbe6a01dc5854f2763c0d7f31fb

PPPoE plugin, not in the port. And our PPPoE implementation already does
that: pppoe_disconnect() calls pppoe_send_padt().


2015-08-14 - pppd: ipxcp: Prevent buffer overrun on remote router name
  fe149de624f96629a7f46732055d8f718c74b856

We never ported IPX support. lwIP does not support IPX.


2015-03-25 - pppd: Fix ccp_options.mppe type
  234edab99a6bb250cc9ecd384cca27b0c8b475ce

We found that while working on MPPE support in lwIP, that's our patch ;-)


2015-03-24 - pppd: Fix ccp_cilen calculated size if both deflate_correct and deflate_draft are enabled
  094cb8ae4c61db225e67fedadb4964f846dd0c27

We found that while working on MPPE support in lwIP, that's our patch ;-)


2015-08-14 - Merge branch 'master' of https://github.com/ncopa/ppp
  3a5c9a8fbc8970375cd881151d44e4b6fe249c6a

Merge commit, we don't care.


2015-08-14 - Merge branch 'master' of git://github.com/vapier/ppp
  912e4fc6665aca188dced7ea7fdc663ce5a2dd24

Merge commit, we don't care.


2015-08-14 - Merge branch 'bug_fix' of git://github.com/radaiming/ppp
  dfd33d7f526ecd7b39dd1bba8101260d02af5ebb

Merge commit, we don't care.


2015-08-14 - Merge branch 'master' of git://github.com/pprindeville/ppp
  aa4a985f6114d08cf4e47634fb6325da71016473

Merge commit, we don't care.


2015-08-14 - Merge branch 'no-error-on-already-closed' of git://github.com/farnz/ppp
  6edf252483b30dbcdcc5059f01831455365d5b6e

Merge commit, we don't care.


2015-08-14 - Merge branch 'send-padt-on-disconnect' of git://github.com/farnz/ppp
  84684243d651f55f6df69d2a6707b52fbbe62bb9

Merge commit, we don't care.
