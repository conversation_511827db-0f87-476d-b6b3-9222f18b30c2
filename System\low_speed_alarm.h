#ifndef __LOW_SPEED_ALARM_H
#define __LOW_SPEED_ALARM_H

#include "at32a403a.h"
#include <stdint.h>

// 告警阈值定义
#define LOW_SPEED_THRESHOLD_RPM     9000        // 低速告警阈值
#define MOTOR_TEMP_THRESHOLD        150.0f      // 电机温度告警阈值(°C)
#define BOARD_TEMP_THRESHOLD        105.0f      // 板上温度告警阈值(°C)
#define VOLTAGE_270V_MIN            180.0f      // 270V最低工作电压
#define ALARM_DELAY_TIME_MS         30000       // 告警延时时间(30秒)
#define STARTUP_RECOVERY_TIME_MS    3000        // 启动恢复延时时间(3秒)

// GPIO定义
#define SET_TEMP_NoFail gpio_bits_set(GPIOC, GPIO_PINS_14)
#define SET_TEMP_IsFail gpio_bits_reset(GPIOC, GPIO_PINS_14)
#define SET_LowS_NoFail gpio_bits_set(GPIOC, GPIO_PINS_15)
#define SET_LowS_IsFail gpio_bits_reset(GPIOC, GPIO_PINS_15)

// 系统状态枚举
typedef enum {
    SYSTEM_STATE_POWER_OFF = 0,     // 系统断电
    SYSTEM_STATE_STARTUP,           // 启动过程
    SYSTEM_STATE_NORMAL_RUN,        // 正常运行
    SYSTEM_STATE_FAULT_DELAY,       // 故障延时判断
    SYSTEM_STATE_ALARM              // 告警状态
} system_state_t;

// 告警类型枚举
typedef enum {
    ALARM_TYPE_NONE = 0,
    ALARM_TYPE_LOW_SPEED = 0x01,
    ALARM_TYPE_MOTOR_TEMP = 0x02,
    ALARM_TYPE_BOARD_TEMP = 0x04
} alarm_type_t;

// 故障原因枚举
typedef enum {
    FAULT_REASON_NONE = 0,
    FAULT_REASON_270V_LOST,         // 270V掉电
    FAULT_REASON_LOW_SPEED,         // 转速过低
    FAULT_REASON_OVERCURRENT,       // 过流
    FAULT_REASON_UNDERVOLTAGE,      // 欠压
    FAULT_REASON_STALL              // 堵转
} fault_reason_t;

// 低速告警模块数据结构
typedef struct {
    // 系统状态
    system_state_t current_state;
    system_state_t previous_state;
    
    // 告警状态
    uint8_t active_alarms;          // 当前激活的告警(位掩码)
    uint8_t alarm_outputs;          // 告警输出状态
    
    // 时间戳
    uint32_t state_enter_time;      // 进入当前状态的时间
    uint32_t fault_start_time;      // 故障开始时间
    uint32_t voltage_recovery_time; // 电压恢复时间
    
    // 故障信息
    fault_reason_t fault_reason;    // 故障原因
    uint8_t is_startup_process;     // 是否在启动过程中
    uint8_t voltage_270v_ok;        // 270V电压状态
    
    // 统计信息
    uint32_t low_speed_alarm_count;
    uint32_t temp_alarm_count;
} low_speed_alarm_data_t;

// 外部接口函数
void Low_Speed_Alarm_Init(void);
void Low_Speed_Alarm_Task(void);
void Low_Speed_Alarm_Reset(void);

// 状态查询函数
uint8_t Low_Speed_Alarm_IsActive(alarm_type_t alarm_type);
system_state_t Low_Speed_Alarm_GetSystemState(void);
fault_reason_t Low_Speed_Alarm_GetFaultReason(void);

// 外部输入接口
float Low_Speed_Alarm_GetMotorSpeed(void);      // 获取电机转速
float Low_Speed_Alarm_GetMotorTemp(void);       // 获取电机温度
float Low_Speed_Alarm_GetBoardTemp(void);       // 获取板上温度
float Low_Speed_Alarm_Get270VVoltage(void);     // 获取270V电压
uint8_t Low_Speed_Alarm_IsMotorRunning(void);   // 电机是否在运行
uint8_t Low_Speed_Alarm_HasProtectionFault(void); // 是否有保护故障

#endif /* __LOW_SPEED_ALARM_H */
