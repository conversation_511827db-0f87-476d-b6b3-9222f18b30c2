/*
 * SysCtl_RotorGet.h
 *
 *  Created on: 2018年10月11日
 *      Author: Hiter
 */

#ifndef INCLUDE_SYSCTL_ROTORGET_H_
#define INCLUDE_SYSCTL_ROTORGET_H_

/*
// Version: V1.0 文件创建
//
// Date:    Dec 18, 2017
//
// Author:  ch<PERSON><PERSON><PERSON> @YD
//===================================================================
//
// 文件名: SysCtl_RotorGet.h
//
// 用途:    转速计算结构体

//===================================================================
*/
#ifndef SYSCTRL_ROTORGETPROCESS_H
#define SYSCTRL_ROTORGETPROCESS_H

//============
typedef struct {
                int uThisPosition;
                int uLastPosition;
                int uLastLastPosition;
                int uLastLastLastPosition;
                float uDeltaPosition;
                float uLastDeltaPosition;      //2020521增加
                int positionflt;
                float Lastpositionflt;
                float uSumDelta;
                int speedave_count;
                float speed_rpm_last;
                float speed_rpm;
                float speed_Mrpm;
                float speed_Trpm;
                float rotorAngle;
                float speedback_flt;  //转速滤波值
                int speedclc_countmax;//决定了最大转速测量
                int speedclc_count;
                int speedclc_flag;

                int speed_direction;
                float speedclc_offset;

                //计算函数
                void (*pfnRotorSpeedclc)();

                }TypeRotorSpeedpara;

//=============  =============================
#define RotorSpeedpara_DEFAULTS {0,0,0,0,0.0,0.0,0.0,0.0,0.0,0,0.0,0.0,0.0,0.0,0.0,0.0,0,0,0,0,0.0,\
                              (void (*)(uint32_t))fnRotorSpeedclc\
                              }//80数值表示能测量到的最大转速范围，最高采样频率是2.5ms，80*开关频率是采样频率；（80）中断进一次   (1/20000采样频率)  多少时间算位移。
//============= ==============================
void fnRotorSpeedclc(TypeRotorSpeedpara *p);
float GetElectricalAngle();
float GetRotorAngle(float elec_angle);
float GetRotorSpeed(float angle);
//============ 全局变量与全局函数声明 =============================
extern TypeRotorSpeedpara RotorSpeedclc;
extern float resolver_angle;
#endif
//========================================================================
// No more.
//========================================================================






#endif /* INCLUDE_SYSCTL_ROTORGET_H_ */
