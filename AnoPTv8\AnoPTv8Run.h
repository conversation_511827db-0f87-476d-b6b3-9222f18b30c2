#ifndef __ANOPTV8RUN_H
#define __ANOPTV8RUN_H
#include "AnoPTv8.h"
//接收缓冲结构体定义
typedef struct
{
    uint8_t recvSta;
    uint16_t recvDataLen;
    _un_frame_v8 dataBuf;
} _recvST;

//发送缓冲结构体定义
typedef struct
{
    uint8_t used;
    uint8_t readyToSend;
    _un_frame_v8 dataBuf;
} _sendST;

// 大缓冲区管理结构体定义
typedef struct {
    uint8_t buffer[640];     // 640字节大缓冲区
    uint16_t writePos;       // 当前写入位置
    uint16_t readyToSend;    // 是否准备发送
    uint16_t dataLength;     // 有效数据长度
} _largeBufST;

//发送缓冲区
//extern _sendST AnoPTv8TxBuf[SENDBUFLEN];
//大缓冲区实例
extern _largeBufST AnoPTv8LargeTxBuf;
//当前正在分析的数据帧
extern _st_frame_v8 AnoPTv8CurrentAnlFrame;

void AnoPTv8RecvOneByte(uint8_t dat);
void AnoPTv8RecvBytes(uint8_t *buf, uint16_t len);
// int AnoPTv8GetFreeTxBufIndex(void);  // 已废弃 - 现在使用大缓冲区
void AnoPTv8TxRunThread1ms(void);
void AnoPTv8TxMainLoopSingle(void);  // 主循环单帧处理函数
void AnoPTv8TxMainLoopAll(void);     // 主循环全帧扫描处理函数
void AnoPTv8TxMainLoopBatch(void);   // 主循环批量发送函数（零拷贝）
void AnoPTv8TxLargeBufSend(void);    // 大缓冲区发送函数
void AnoPTv8TxLargeBufFlush(void);   // 强制发送大缓冲区

#endif
