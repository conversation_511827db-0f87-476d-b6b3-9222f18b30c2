
#include "usb_app.h"
#include "usb_conf.h"
#include "wk_system.h"

#include "usbd_int.h"
#include "cdc_class.h"
#include "cdc_desc.h"

#include "HWInterface.h"

usbd_core_type usb_core_dev;

uint8_t usbd_app_buffer_fs1[320];


/**
  * @brief  usb application initialization
  * @param  none
  * @retval none
  */
void wk_usb_app_init(void)
{
  usbd_core_init(&usb_core_dev, USB, &cdc_class_handler, &cdc_desc_handler, 0);

  usbd_connect(&usb_core_dev);
}

/**
  * @brief  usb application task
  * @param  none
  * @retval none
  */
void wk_usb_app_task(void)
{

    uint16_t length = 0;
    /* fs1 device cdc - 直接接收并处理数据 */
    length = usb_vcp_get_rxdata(&usb_core_dev, usbd_app_buffer_fs1);
    if(length > 0)
    {
        // // 直接处理接收到的数据
        AnoPTv8HwRecvBytes(usbd_app_buffer_fs1, length);
        //usb_send_data(usbd_app_buffer_fs1, length);
    }
    
}

/**
  * @brief  usb interrupt handler
  * @param  none
  * @retval none
  */
void wk_usbfs_irq_handler(void)
{
  usbd_irq_handler(&usb_core_dev);
}

/**
  * @brief  usb delay function
  * @param  ms: delay number of milliseconds.
  * @retval none
  */
void usb_delay_ms(uint32_t ms)
{
  wk_delay_ms(ms);
}

/**
  * @brief  usb send data function
  * @param  data: pointer to data buffer
  * @param  len: data length
  * @retval error status: SUCCESS or ERROR
  */
error_status usb_send_data(uint8_t *data, uint16_t len)
{
  return usb_vcp_send_data(&usb_core_dev, data, len);
}
