#ifndef __SENSOR_PROC_H
#define __SENSOR_PROC_H

#include "at32a403a.h"
#include "at32a403a_wk_config.h"
#include "system_status.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======================== 传感器标识枚举 ======================== */
/**
 * @brief 传感器类型枚举
 * @note 枚举值对应ADC1扫描序列中的位置索引
 */
typedef enum {
    SENSOR_M_PT0    = 0,    // 电机温度传感器0 - ADC1_CH0 (PA0)
    SENSOR_M_PT1    = 1,    // 电机温度传感器1 - ADC1_CH1 (PA1)
    SENSOR_B_PT0    = 2,    // 轴承温度传感器0 - ADC1_CH3 (PA3)
    SENSOR_U_DC     = 3,    // 母线电压传感器 - ADC1_CH5 (PA5)
    SENSOR_COUNT    = 4     // 传感器总数
} sensor_id_t;

/* ======================== 传感器状态枚举 ======================== */
/**
 * @brief 传感器健康状态枚举
 */
typedef enum {
    SENSOR_STATUS_OK        = 0,    // 传感器正常
    SENSOR_STATUS_OPEN      = 1,    // 传感器开路
    SENSOR_STATUS_SHORT     = 2,    // 传感器短路
    SENSOR_STATUS_OUT_RANGE = 3,    // 传感器超出范围
    SENSOR_STATUS_INIT      = 4     // 传感器初始化状态
} sensor_status_t;

/* ======================== 转换方法枚举 ======================== */
/**
 * @brief 单位转换方法枚举
 */
typedef enum {
    CONVERT_METHOD_LINEAR   = 0,    // 线性转换 y = ax + b
    CONVERT_METHOD_TABLE    = 1     // 查表法转换（线性插值）
} convert_method_t;

/* ======================== 滤波方法枚举 ======================== */
/**
 * @brief 数字滤波方法枚举
 */
typedef enum {
    FILTER_METHOD_NONE      = 0,    // 无滤波
    FILTER_METHOD_IIR       = 1,    // 一阶低通滤波器(IIR)
    FILTER_METHOD_MOVING_AVG = 2    // 滑动平均滤波器
} filter_method_t;

/* ======================== 数据结构定义 ======================== */
/**
 * @brief 查表法转换数据点结构
 */
typedef struct {
    uint16_t adc_value;     // ADC原始值
    float    physical_value; // 对应的物理值
} lookup_point_t;

/**
 * @brief 查表法转换表结构
 */
typedef struct {
    const lookup_point_t *points;   // 指向数据点数组
    uint8_t point_count;            // 数据点数量
} lookup_table_t;

/**
 * @brief 传感器处理结果结构
 */
typedef struct {
    float           value;          // 最终处理后的物理值
    sensor_status_t status;         // 传感器状态
    uint16_t        raw_adc;        // 原始ADC值
    uint32_t        error_count;    // 错误计数
} sensor_result_t;

/* ======================== 配置参数宏定义 ======================== */
// 母线电压传感器系数
#define U_DC_LINEAR_COEFF_A     0.081372f     // 线性系数a (V/ADC_COUNT)
#define U_DC_LINEAR_COEFF_B     0.0f         // 线性系数b (V)
#define U_DC_MIN_VOLTAGE        0.0f         // 最小电压 (V)
#define U_DC_MAX_VOLTAGE        333.333f     // 最大电压 (V)

// ADC故障检测阈值
#define ADC_OPEN_THRESHOLD      0          // 开路检测阈值（ADC计数）
#define ADC_SHORT_THRESHOLD     4095       // 短路检测阈值（ADC计数）

// 滤波器参数
#define MOVING_AVG_WINDOW_SIZE  (8)         // 滑动平均窗口大小（8位）

/* ======================== 公开API函数声明 ======================== */
/**
 * @brief 初始化传感器处理模块
 * @param adc_buffer: 指向ADC DMA缓冲区的指针
 * @return 0: 成功, -1: 失败
 */
int sensor_proc_init(uint16_t *adc_buffer);

/**
 * @brief 传感器处理任务（需要在主循环中周期性调用）
 * @note 此函数是非阻塞的，执行时间应控制在微秒级别
 */
void sensor_proc_task(void);

/**
 * @brief 获取指定传感器的处理结果
 * @param sensor_id: 传感器ID
 * @param result: 输出结果指针
 * @return 0: 成功, -1: 参数错误
 */
int sensor_get_result(sensor_id_t sensor_id, sensor_result_t *result);

/**
 * @brief 获取指定传感器的物理值
 * @param sensor_id: 传感器ID
 * @param value: 输出物理值指针
 * @return 状态码
 */
sys_status_t sensor_get_value(sensor_id_t sensor_id, float *value);

/**
 * @brief 获取指定传感器的状态
 * @param sensor_id: 传感器ID
 * @return 传感器状态
 */
sensor_status_t sensor_get_status(sensor_id_t sensor_id);

/**
 * @brief 获取指定传感器的原始ADC值
 * @param sensor_id: 传感器ID
 * @return 原始ADC值，错误时返回0
 */
uint16_t sensor_get_raw_adc(sensor_id_t sensor_id);

/**
 * @brief 重置指定传感器的错误计数
 * @param sensor_id: 传感器ID
 */
void sensor_reset_error_count(sensor_id_t sensor_id);

/**
 * @brief 获取模块运行状态信息（用于调试）
 * @param total_errors: 输出总错误计数
 */
void sensor_get_debug_info(uint32_t *total_errors);

/**
 * @brief 设置DMA数据就绪标志（由DMA中断调用）
 * @note 此函数应在DMA1_Channel1_IRQHandler中调用
 */
void sensor_set_dma_ready(void);

/* ======================== 便捷访问宏定义 ======================== */
/**
 * @brief 安全获取传感器值的宏定义
 * @note 这些宏在获取失败时返回默认值，用于向后兼容
 */
#define SENSOR_GET_MOTOR_TEMP()     sensor_get_value_safe(SENSOR_M_PT0, 25.0f)
#define SENSOR_GET_BOARD_TEMP()     sensor_get_value_safe(SENSOR_B_PT0, 25.0f)
#define SENSOR_GET_BUS_VOLTAGE()    sensor_get_value_safe(SENSOR_U_DC, 0.0f)

/**
 * @brief 安全获取传感器值（内部使用）
 * @param sensor_id: 传感器ID
 * @param default_value: 默认值
 * @return 传感器值或默认值
 */
static inline float sensor_get_value_safe(sensor_id_t sensor_id, float default_value)
{
    float value;
    if (sensor_get_value(sensor_id, &value) == SYS_STATUS_OK) {
        return value;
    }
    return default_value;
}

#ifdef __cplusplus
}
#endif

#endif /* __SENSOR_PROC_H */
