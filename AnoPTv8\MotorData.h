#ifndef __MOTOR_DATA_H
#define __MOTOR_DATA_H

#include "sysTypeDef.h"

// 最大传输字节数定义
#define F1_MAX_BYTES 64    // F1帧最大传输字节数
#define F2_MAX_BYTES 64    // F2帧最大传输字节数
#define F3_MAX_BYTES 64    // F3帧最大传输字节数
#define F4_MAX_BYTES 10    // F4帧最大传输字节数
#define F5_MAX_BYTES 128   // F5帧最大传输字节数 
#define F6_MAX_BYTES 40    // F6帧最大传输字节数

// 数据帧类型枚举
typedef enum {
    FRAME_F1 = 0xF1,  // F1帧ID
    FRAME_F2 = 0xF2,  // F2帧ID
    FRAME_F3 = 0xF3,  // F3帧ID
    FRAME_F4 = 0xF4,  // F4帧ID
    FRAME_F5 = 0xF5,  // F5帧ID - ADC数据
    FRAME_F6 = 0xF6   // F6帧ID - ADC原始数据
} FrameType_t;

// F1帧数据结构体
typedef struct {
    float i_phase_a;        // A相电流
    float i_phase_b;        // B相电流
    float i_phase_c;        // C相电流
    float id_ref;           // Id参考值
    float id_actual;        // Id实际值
    float iq_ref;           // Iq参考值
    float iq_actual;        // Iq实际值
    float motor_speed_ref;  // 给定转速
    float motor_speed;      // 实际转速
    float motor_position;   // 电机位置（电角度）
    float svpwm_wave_a;     // A相马勒调制波
    float svpwm_wave_b;     // B相马勒调制波
    float svpwm_wave_c;     // C相马勒调制波
    float ud_actual;        // Ud实际值
    float uq_actual;        // Uq实际值
    float I_dc;             // 母线电流
    uint8_t channel_count;  // 数据通道数量
} MotorDataF1_t;

// F1帧默认值定义
#define MOTOR_DATA_F1_DEFAULTS { \
    0.0f,   /* i_phase_a */     \
    0.0f,   /* i_phase_b */     \
    0.0f,   /* i_phase_c */     \
    0.0f,   /* id_ref */        \
    0.0f,   /* id_actual */     \
    0.0f,   /* iq_ref */        \
    0.0f,   /* iq_actual */     \
    0.0f,   /* motor_speed_ref */\
    0.0f,   /* motor_speed */   \
    0.0f,   /* motor_position */\
    0.0f,   /* svpwm_wave_a */  \
    0.0f,   /* svpwm_wave_b */  \
    0.0f,   /* svpwm_wave_c */  \
    0.0f,   /* ud_actual */     \
    0.0f,   /* uq_actual */     \
    0.0f,   /* I_dc */          \
    16      /* channel_count */ \
}

// F2帧数据结构体 - 传感器数据（10ms频率）
typedef struct {
    float bus_voltage;              // 母线电压 (V)
    float bus_current;              // 母线电流 (A)
    float motor_temp_0;             // 电机温度传感器0 (°C)
    float motor_temp_1;             // 电机温度传感器1 (°C)
    float board_temp;               // 板上温度传感器 (°C)
    float phase_rectified_avg_a;    // A相整流平均值 (A)
    float phase_rectified_avg_b;    // B相整流平均值 (A)
    float phase_rectified_avg_c;    // C相整流平均值 (A)
    float current_imbalance;        // 三相不平衡度 (%)
    uint8_t channel_count;          // 数据通道数量
} MotorDataF2_t;

// F2帧默认值定义
#define MOTOR_DATA_F2_DEFAULTS { \
    0.0f,   /* bus_voltage */              \
    0.0f,   /* bus_current */              \
    25.0f,  /* motor_temp_0 */             \
    25.0f,  /* motor_temp_1 */             \
    25.0f,  /* board_temp */               \
    0.0f,   /* phase_rectified_avg_a */    \
    0.0f,   /* phase_rectified_avg_b */    \
    0.0f,   /* phase_rectified_avg_c */    \
    0.0f,   /* current_imbalance */        \
    10      /* channel_count */            \
}

// F3帧数据结构体 - 温度信息（100ms频率）
typedef struct {
    float temp_mot1;         // 电机温度1
    float temp_mot2;         // 电机温度2
    float temp_cap1;         // 电容温度1
    float temp_cap2;         // 电容温度2
    float temp_oll;          // 油温温度
    float temp_bmf_u;        // U相温度
    float temp_bmf_v;        // V相温度
    float temp_bmf_w;        // W相温度
    float temp_PT100_6;      // 预留PT100温度6
    float temp_PT1000_7;     // 预留PT1000温度7
    uint8_t channel_count;   // 数据通道数量
} MotorDataF3_t;

// F3帧默认值定义
#define MOTOR_DATA_F3_DEFAULTS { \
    0.0f,   /* temp_mot1 */      \
    0.0f,   /* temp_mot2 */      \
    0.0f,   /* temp_cap1 */      \
    0.0f,   /* temp_cap2 */      \
    0.0f,   /* temp_oll */       \
    0.0f,   /* temp_bmf_u */     \
    0.0f,   /* temp_bmf_v */     \
    0.0f,   /* temp_bmf_w */     \
    0.0f,   /* temp_PT100_6 */   \
    0.0f,   /* temp_PT1000_7 */  \
    10      /* channel_count */  \
}

// F4帧数据结构体
typedef struct {
    // 系统状态 (7字节)
    uint8_t power_28v_ok;      // 28V控制电源正常
    uint8_t power_700v_ok;     // 700V高压电源正常      
    uint8_t self_test_ok;      // 自检通过
    uint8_t sys_ready;         // 系统就绪
    uint8_t sys_running;       // 系统运行中
    uint8_t sys_fault;         // 系统故障
    uint8_t sys_stop;          // 系统停机

    // 告警状态 (7字节)
    uint8_t current;           // 过流告警
    uint8_t bus_voltage;       // 母线过压
    uint8_t bus_undervol;      // 母线欠压告警
    uint8_t cap_temp;          // 电容过温告警
    uint8_t motor_temp;        // 电机过温告警
    uint8_t driver_temp;       // 驱动过温告警
    uint8_t v28_undervol;      // 28V欠压告警级别

    // 自定义标志位 (2字节)
    uint8_t custom_flag1;               // 自定义标志1
    uint8_t custom_flag2;               // 自定义标志2
    
    uint8_t channel_count;              // 数据通道数量 (固定16字节数据)
} MotorDataF4_t;

// F4帧默认值定义
#define MOTOR_DATA_F4_DEFAULTS {    \
    0,      /* power_28v_ok */      \
    0,      /* power_700v_ok */     \
    0,      /* self_test_ok */      \
    0,      /* sys_ready */         \
    0,      /* sys_running */       \
    0,      /* sys_fault */         \
    0,      /* sys_stop */          \
    WARN_LEVEL_NONE,  /* current */ \
    WARN_LEVEL_NONE,  /* bus_voltage */ \
    WARN_LEVEL_NONE,  /* bus_undervol */ \
    WARN_LEVEL_NONE,  /* cap_temp */ \
    WARN_LEVEL_NONE,  /* motor_temp */ \
    WARN_LEVEL_NONE,  /* v28_undervol */ \
    0,      /* custom_flag1 */      \
    0,      /* custom_flag2 */      \
    16      /* channel_count */     \
}

// F5帧数据结构体优化
typedef struct {
    float actual_values[ADC_REGULAR_CHANNEL_NUM];    // 所有实际值
    uint8_t channel_count;                           // 通道数量
} MotorDataF5_t;

// F5帧默认值定义
#define MOTOR_DATA_F5_DEFAULTS { \
    {0.0f}, /* actual_values */ \
    ADC_REGULAR_CHANNEL_NUM      /* channel_count */ \
}

// F6帧数据结构体
typedef struct {
    uint16_t actual_values[ADC_REGULAR_CHANNEL_NUM];    // 所有实际值
    uint8_t channel_count;                              // 通道数量
} MotorDataF6_t;

// F6帧默认值定义
#define MOTOR_DATA_F6_DEFAULTS { \
    {0}, /* actual_values */ \
    ADC_REGULAR_CHANNEL_NUM      /* channel_count */ \
}

// 全局数据结构体
typedef struct {
    MotorDataF1_t f1;
    MotorDataF2_t f2;
    MotorDataF3_t f3;
    MotorDataF4_t f4;
    MotorDataF5_t f5;
    MotorDataF6_t f6;
} MotorData_t;

// 全局数据结构体默认值定义
#define MOTOR_DATA_DEFAULTS { \
    MOTOR_DATA_F1_DEFAULTS,  \
    MOTOR_DATA_F2_DEFAULTS,  \
    MOTOR_DATA_F3_DEFAULTS,  \
    MOTOR_DATA_F4_DEFAULTS,  \
    MOTOR_DATA_F5_DEFAULTS,  \
    MOTOR_DATA_F6_DEFAULTS   \
}

// 外部声明
extern MotorData_t gMotorData;

// 函数声明
void MotorDataSendFrame(FrameType_t frame_type);
void MotorDataTest(void);

// 数据更新函数声明
void UpdateF1FocData(void);         // 更新F1帧FOC数据
void UpdateF2SensorData(void);      // 更新F2帧传感器数据（高频）
void UpdateF3TempData(void);        // 更新F3帧温度数据（低频）
void UpdateF5ADCData(void);         // 更新F5帧ADC数据
#endif 
