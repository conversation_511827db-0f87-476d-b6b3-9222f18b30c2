/*
// Version: V1.0
// Date: 	Dec 21, 2012
// Author: <PERSON><PERSON><PERSON><PERSON>
//===========================================================
//
// 文件名:    MathBasic.h
//
// 用途:   	  数学运算函数库
//
// LPF()	低通滤波器，临时使用，精度较低，运算效率不高
// Max()	浮点数3输入取最大值
// Min()	浮点数3输入取最小值
// uMax()	定点数3输入取最大值
// uMin()	定点数3输入取最小值
// uMid()	定点数3输入取中间值
// Mux3s1()	三选一选择器
// 
//=============================================================
*/

#ifndef MATHBASIC_H
#define MATHBASIC_H
#include "at32a403a.h"

// 数学常量定义
// float32格式
// #define PI            3.14159265
#define PI2      	    6.28318531
#define SQRT2By3      0.816496581
#define SQRT3_D2      0.866025404
#define SQRT3         1.73205080
#define SQRT2         1.41421356
#define f1_DSQRT2     0.70710678
#define SQRT3_DSQRT2  (SQRT3/SQRT2)
#define TWO_D_SQRT3   1.15470054
#define FOUR_D_3PI    (PI * 4.0 / 3.0)
#define TWO_D_3PI     (PI * 2.0 / 3.0)
#define VarAveToRMS   1.11072073	// = pi / (2 * sqrt(2))
#define N_PI2		  (-1.0)*(PI2)//
#define f4_D_3PI      (4.0/3.0)*(PI)//
#define f2_D_3PI      (2.0/3.0)*(PI)//
#define D2By3         0.66666667//

// float64格式
#define f2_PI      		 3.1415926535897932384626433832795L
#define f2_PI2      	 6.283185307179586476925286766559L
#define f2_SQRT2By3    0.81649658092772603273242802490196L
#define f2_SQRT3_D2		 0.86602540378443864676372317075294L
#define f2_SQRT3       1.7320508075688772935274463415059L
#define f2_SQRT2       1.4142135623730950488016887242097L
#define f2_TWO_D_SQRT3 1.1547005383792515290182975610039L
#define f2_FOUR_D_3PI  (f2_PI * 4.0L / 3.0L)
#define f2_TWO_D_3PI   (f2_PI * 2.0L / 3.0L)
#define f2_VarAveToRMS 1.1107207345395915617539702475152L// = pi / (2 * sqrt(2))

// 宏函数定义
// #define MAX(x,y)	(((x) >= (y)) ? (x) : (y))		// 求最大值
// #define MIN(x,y)	(((x) <= (y)) ? (x) : (y))		// 求最小值
#define POW2(x)  	((x) * (x))		
#define POW3(x)  	((x) * (x) * (x))		
#define UP_LIMIT(x, limit)		(x = MIN(x, limit))		// 上限幅
#define DOWN_LIMIT(x, limit)	(MAX(x, limit))				// 下限幅
#define DUAL_LIMIT(x, vmax, vmin)		(DOWN_LIMIT(UP_LIMIT(x, vmax), vmin))		// 双向限幅
// 函数声明
// extern float LPFKY(float fLPFVarIn,float fLPFVarInOld,float fLPFVarOutOld,float fLPFCutFre);
// extern float Max(float a,float b,float c);
// extern float Min(float a,float b,float c);
// extern uint16_t uMax(uint16_t a,uint16_t b,uint16_t c);
// extern uint16_t uMin(uint16_t a,uint16_t b,uint16_t c);
// extern uint16_t uMid(uint16_t a,uint16_t b,uint16_t c);
// extern float Mux3s1(uint16_t SelectSig,float fIn0,float fIn1,float fIn2);
// extern int HysLoop(float fVarIn,float fVarCom,float fVarDeta);
// extern float LPFCompensate(float fVarIn,float fVarInOld,float fTCon);
// extern float Limit(float fMinIn,float fMaxIn,float fDateIn);
// extern float LPF(float fLPFVarIn,float fLPFTCon,float fLPFOutOld);

typedef struct {    float   x;              /* Input:Input of the low pass filter (PU) */
                    float   Tc;             /* Parameter:Sampling period (PU) */
                    float   wc;             /* Parameter:Cut off frequency for low pass filter (PU) */
                    float   y_old;          /* Variable:Output of the last cycle (PU) */
                    float   y;              /* Output:Output of the low pass filter (PU) */
                    void  (*calc)();    /* Po_iqer to calculation function */
                 } Filter;


typedef Filter *Filter_handle;

#define Filter_DEFAULTS {0,       \
                          0.0004f,    \
                          50.0f,    \
                          0,    \
                          0,    \
                          (void (*)(long))Filter_calc }
                       


void Filter_calc(Filter_handle);
extern Filter Filter_udch;
extern Filter Filter_udcl;
extern Filter Filter_uuv;
extern Filter Filter_uvw;
#endif

//========================================================================
// No more.
//========================================================================

