#ifndef __PROTECTION_MONITOR_H
#define __PROTECTION_MONITOR_H

#include "at32a403a.h"
#include "system_status.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ======================== 监控参数枚举 ======================== */
/**
 * @brief 保护监控参数ID枚举
 */
typedef enum {
    PROTECT_PARAM_BUS_OVERVOLT      = 0,    // 母线过压
    PROTECT_PARAM_BUS_UNDERVOLT     = 1,    // 母线欠压
    PROTECT_PARAM_BUS_OVERCURRENT   = 2,    // 母线过流
    PROTECT_PARAM_MOTOR_TEMP        = 3,    // 电机温度
    PROTECT_PARAM_DRIVER_TEMP       = 4,    // 驱动模块温度
    PROTECT_PARAM_CURRENT_IMBALANCE = 5,    // 三相电流不平衡度
    PROTECT_PARAM_STALL             = 6,    // 堵转保护（动态延时）
    PROTECT_PARAM_OVERSPEED         = 7,    // 飞车/超速保护
    PROTECT_PARAM_COUNT             = 8     // 监控参数总数
} protect_param_id_t;

/* ======================== 故障码枚举 ======================== */
/**
 * @brief 保护故障码枚举
 */
typedef enum {
    FAULT_CODE_NONE                 = 0x00, // 无故障
    
    // 母线相关故障 (0x10-0x1F)
    FAULT_CODE_BUS_OVERVOLT_WARN    = 0x10, // 母线过压警告
    FAULT_CODE_BUS_OVERVOLT_FAULT   = 0x11, // 母线过压故障
    FAULT_CODE_BUS_UNDERVOLT_WARN   = 0x12, // 母线欠压警告
    FAULT_CODE_BUS_UNDERVOLT_FAULT  = 0x13, // 母线欠压故障
    FAULT_CODE_BUS_OVERCURRENT_WARN = 0x14, // 母线过流警告
    FAULT_CODE_BUS_OVERCURRENT_FAULT= 0x15, // 母线过流故障
    
    // 温度相关故障 (0x20-0x2F)
    FAULT_CODE_MOTOR_TEMP_WARN      = 0x20, // 电机温度警告
    FAULT_CODE_MOTOR_TEMP_FAULT     = 0x21, // 电机温度故障
    FAULT_CODE_DRIVER_TEMP_WARN     = 0x22, // 驱动温度警告
    FAULT_CODE_DRIVER_TEMP_FAULT    = 0x23, // 驱动温度故障
    
    // 电流相关故障 (0x30-0x3F)
    FAULT_CODE_CURRENT_IMBALANCE_WARN = 0x30, // 电流不平衡警告
    FAULT_CODE_CURRENT_IMBALANCE_FAULT= 0x31, // 电流不平衡故障
    
    // 运行相关故障 (0x40-0x4F)
    FAULT_CODE_STALL_WARN           = 0x40, // 堵转警告
    FAULT_CODE_STALL_FAULT          = 0x41, // 堵转故障
    FAULT_CODE_OVERSPEED_WARN       = 0x42, // 超速警告
    FAULT_CODE_OVERSPEED_FAULT      = 0x43  // 超速故障
} fault_code_t;

/* ======================== 保护等级枚举 ======================== */
/**
 * @brief 保护严重性等级枚举
 */
typedef enum {
    PROTECT_LEVEL_WARNING   = 0,    // 警告级别
    PROTECT_LEVEL_FAULT     = 1,    // 故障级别
    PROTECT_LEVEL_COUNT     = 2     // 保护等级总数
} protect_level_t;

/* ======================== 保护状态枚举 ======================== */
/**
 * @brief 保护状态枚举
 */
typedef enum {
    PROTECT_STATUS_NORMAL       = 0,    // 正常状态
    PROTECT_STATUS_MONITORING   = 1,    // 监控中（已越限但未到延时）
    PROTECT_STATUS_TRIGGERED    = 2     // 已触发保护
} protect_status_t;

/* ======================== 故障记录数据结构 ======================== */
/**
 * @brief 故障记录结构
 */
typedef struct {
    uint32_t            trigger_time_ms;    // 触发时间（毫秒时间戳）
    fault_code_t        fault_code;         // 故障/告警代码
    protect_param_id_t  param_id;           // 参数ID
    protect_level_t     level;              // 保护等级
    float               trigger_value;      // 触发时的参数值
    uint8_t             valid;              // 记录有效标志
} fault_record_t;

/* ======================== 故障记录配置 ======================== */
#define FAULT_RECORD_MAX_COUNT  32          // 最大故障记录数量

/* ======================== 堵转检测配置 ======================== */
// 堵转检测阈值
#define STALL_SPEED_THRESHOLD       50.0f   // 堵转转速阈值(RPM)

// 堵转检测电流阈值和对应的持续时间
#define STALL_IQ_THRESHOLD_1        6.0f    // 第一级电流阈值(A)
#define STALL_TIME_THRESHOLD_1      500     // 第一级持续时间(ms)

#define STALL_IQ_THRESHOLD_2        9.0f    // 第二级电流阈值(A)
#define STALL_TIME_THRESHOLD_2      300     // 第二级持续时间(ms)

#define STALL_IQ_THRESHOLD_3        12.0f   // 第三级电流阈值(A)
#define STALL_TIME_THRESHOLD_3      200     // 第三级持续时间(ms)



/* ======================== 函数指针类型定义 ======================== */
/**
 * @brief 参数值获取函数指针类型
 * @return 参数的当前值
 */
typedef float (*param_getter_func_t)(void);

/**
 * @brief 动态延时计算函数指针类型
 * @param current_value: 当前监控参数值
 * @return 动态计算的延时时间(ms)
 */
typedef uint32_t (*dynamic_delay_func_t)(float current_value);

/**
 * @brief 保护事件发布函数指针类型
 * @param fault_code: 故障码
 * @param param_id: 参数ID
 * @param level: 保护等级
 * @param value: 触发时的参数值
 */
typedef void (*protection_event_func_t)(fault_code_t fault_code, 
                                       protect_param_id_t param_id, 
                                       protect_level_t level, 
                                       float value);

/* ======================== 公开API函数声明 ======================== */
/**
 * @brief 初始化保护监控模块
 * @param event_handler: 保护事件处理函数指针
 * @return 0: 成功, -1: 失败
 */
int Protection_Monitor_Init(protection_event_func_t event_handler);

/**
 * @brief 保护监控周期性任务
 * @note 此函数应在主循环中周期性调用（建议1-10ms）
 */
void Protection_Monitor_Tick(void);

/**
 * @brief 获取指定参数的保护状态
 * @param param_id: 参数ID
 * @return 保护状态
 */
protect_status_t Protection_Monitor_GetStatus(protect_param_id_t param_id);

/**
 * @brief 重置指定参数的保护状态
 * @param param_id: 参数ID
 */
void Protection_Monitor_Reset(protect_param_id_t param_id);

/**
 * @brief 重置所有保护状态
 */
void Protection_Monitor_ResetAll(void);

/**
 * @brief 获取指定参数的当前值
 * @param param_id: 参数ID
 * @param value: 输出参数值指针
 * @return 状态码
 */
sys_status_t Protection_Monitor_GetValue(protect_param_id_t param_id, float *value);

/**
 * @brief 获取模块运行统计信息
 * @param total_triggers: 输出总触发次数
 * @param active_protections: 输出当前激活的保护数量
 */
void Protection_Monitor_GetStats(uint32_t *total_triggers, uint8_t *active_protections);

/* ======================== 故障记录API函数声明 ======================== */
/**
 * @brief 获取故障记录数量
 * @return 当前故障记录数量
 */
uint8_t Protection_Monitor_GetFaultRecordCount(void);

/**
 * @brief 获取指定索引的故障记录
 * @param index: 记录索引（0为最新记录）
 * @param record: 输出故障记录指针
 * @return 0: 成功, -1: 索引无效或记录无效
 */
int Protection_Monitor_GetFaultRecord(uint8_t index, fault_record_t *record);

/**
 * @brief 清除所有故障记录
 */
void Protection_Monitor_ClearFaultRecords(void);

/**
 * @brief 获取最新的故障记录
 * @param record: 输出故障记录指针
 * @return 0: 成功, -1: 无有效记录
 */
int Protection_Monitor_GetLatestFaultRecord(fault_record_t *record);

/**
 * @brief 根据故障码查找故障记录
 * @param fault_code: 要查找的故障码
 * @param record: 输出故障记录指针
 * @return 0: 成功, -1: 未找到匹配记录
 */
int Protection_Monitor_FindFaultRecord(fault_code_t fault_code, fault_record_t *record);



/* ======================== 外部接口函数声明（需要外部实现） ======================== */
/**
 * @brief 获取电机转速
 * @return 电机转速(RPM)
 */
float Protection_Monitor_GetMotorSpeed(void);

/**
 * @brief 获取转矩电流Iq
 * @return 转矩电流Iq(A)
 */
float Protection_Monitor_GetTorqueCurrent(void);

#ifdef __cplusplus
}
#endif

#endif /* __PROTECTION_MONITOR_H */
