/**
 * @file Sys_TimerEvent.h
 * @brief 系统定时器事件驱动头文件
 * @details 提供500us基准的定时器事件驱动功能
 */

#ifndef __SYS_TIMER_EVENT_H
#define __SYS_TIMER_EVENT_H

#include "at32a403a.h"                  // Device header


/* 定时标志 */
typedef struct {
    uint8_t flag_500us;    // 500us标志
    uint8_t flag_1ms;      // 1ms标志
    uint8_t flag_1s;       // 1s标志
    uint8_t flag_1min;     // 1min标志
} TimerFlag_t;

/* 定时计数器 */
typedef struct {
    uint16_t cnt_1ms;      // 1ms计数(范围0-1)
    uint16_t cnt_1s;       // 1s计数(范围0-999)
    uint16_t cnt_1min;     // 1min计数(范围0-59)
} TimerCount_t;

// 外部声明
extern TimerFlag_t gTimerFlag;
extern volatile uint32_t sys_runtime_ms;

// 函数声明
void TimerEvent_Init(void);
void TimerEvent_Handler(void);
#define Get_Runtime_Ms() sys_runtime_ms

#endif 