<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>AT32A403ARGT7_154_33</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>6220000::V6.22::ARMCLANG</pCCUsed>
      <uAC6>1</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>-AT32A403ARGT7</Device>
          <Vendor>ArteryTek</Vendor>
          <PackID>ArteryTek.AT32A403A_DFP.2.0.8</PackID>
          <Cpu>IRAM(0x20000000,0x18000) IROM(0x08000000,0x100000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0AT32A403A_1024 -********** -********** -FP0($$Device:-AT32A403ARGT7$Flash\AT32A403A_1024.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:-AT32A403ARGT7$Device\Include\at32a403a.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:-AT32A403ARGT7$SVD\AT32A403Axx_v2.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\objects\</OutputDirectory>
          <OutputName>AT32A403ARGT7_154_33</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>0</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <nBranchProt>0</nBranchProt>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x18000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x18000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>3</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>5</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>AT32A403ARGT7,USE_STDPERIPH_DRIVER,AT_START_A403A_V1</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\libraries\drivers\inc;..\..\libraries\cmsis\cm4\core_support;..\..\libraries\cmsis\cm4\device_support;../../middlewares/usbd_drivers/inc;..\..\User;..\..\User\src_inc;..\..\Motor;..\..\AnoPTv8;..\..\Drive;..\..\System</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>user</GroupName>
          <Files>
            <File>
              <FileName>sysTypeDef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\sysTypeDef.h</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>SysFSM.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\SysFSM.h</FilePath>
            </File>
            <File>
              <FileName>SysFSM.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\SysFSM.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>firmware</GroupName>
          <Files>
            <File>
              <FileName>at32a403a_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_adc.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_crm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_crm.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_debug.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_dma.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_exint.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_exint.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_flash.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_gpio.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_misc.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_pwc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_pwc.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_spi.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_tmr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_tmr.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_usb.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\drivers\src\at32a403a_wdt.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>cmsis</GroupName>
          <Files>
            <File>
              <FileName>startup_at32a403a.s</FileName>
              <FileType>2</FileType>
              <FilePath>.\startup_at32a403a.s</FilePath>
            </File>
            <File>
              <FileName>system_at32a403a.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\libraries\cmsis\cm4\device_support\system_at32a403a.c</FilePath>
            </File>
            <File>
              <FileName>arm_cortexM4lf_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>..\..\middlewares\dsp\arm_cortexM4lf_math.lib</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>middlewares/usbd_driver</GroupName>
          <Files>
            <File>
              <FileName>usbd_core.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../middlewares/usbd_drivers/src/usbd_core.c</FilePath>
            </File>
            <File>
              <FileName>usbd_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../middlewares/usbd_drivers/src/usbd_int.c</FilePath>
            </File>
            <File>
              <FileName>usbd_sdr.c</FileName>
              <FileType>1</FileType>
              <FilePath>../../middlewares/usbd_drivers/src/usbd_sdr.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>inc_src</GroupName>
          <Files>
            <File>
              <FileName>at32a403a_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\at32a403a_conf.h</FilePath>
            </File>
            <File>
              <FileName>at32a403a_int.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\src_inc\at32a403a_int.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_int.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\at32a403a_int.h</FilePath>
            </File>
            <File>
              <FileName>at32a403a_wk_config.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\src_inc\at32a403a_wk_config.c</FilePath>
            </File>
            <File>
              <FileName>at32a403a_wk_config.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\at32a403a_wk_config.h</FilePath>
            </File>
            <File>
              <FileName>cdc_class.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\src_inc\cdc_class.c</FilePath>
            </File>
            <File>
              <FileName>cdc_class.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\cdc_class.h</FilePath>
            </File>
            <File>
              <FileName>cdc_desc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\src_inc\cdc_desc.c</FilePath>
            </File>
            <File>
              <FileName>cdc_desc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\cdc_desc.h</FilePath>
            </File>
            <File>
              <FileName>usb_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\src_inc\usb_app.c</FilePath>
            </File>
            <File>
              <FileName>usb_app.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\usb_app.h</FilePath>
            </File>
            <File>
              <FileName>usb_conf.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\usb_conf.h</FilePath>
            </File>
            <File>
              <FileName>wk_system.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\src_inc\wk_system.c</FilePath>
            </File>
            <File>
              <FileName>wk_system.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\User\src_inc\wk_system.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>motor</GroupName>
          <Files>
            <File>
              <FileName>Algorithm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\Algorithm.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\delay.c</FilePath>
            </File>
            <File>
              <FileName>delay.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\delay.h</FilePath>
            </File>
            <File>
              <FileName>MathBasic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\MathBasic.c</FilePath>
            </File>
            <File>
              <FileName>MathBasic.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\MathBasic.h</FilePath>
            </File>
            <File>
              <FileName>Motor_VectorControl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\Motor_VectorControl.c</FilePath>
            </File>
            <File>
              <FileName>Motor_VectorControl.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\Motor_VectorControl.h</FilePath>
            </File>
            <File>
              <FileName>Sys_Isr_Controller.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\Sys_Isr_Controller.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_AllHeaders.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysCtl_AllHeaders.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_AnalogProcess.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\SysCtl_AnalogProcess.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_AnalogProcess.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysCtl_AnalogProcess.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_ConstDef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysCtl_ConstDef.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_CsvParamDef.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysCtl_CsvParamDef.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_GlobalVar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\SysCtl_GlobalVar.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_GlobalVar.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysCtl_GlobalVar.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_RotorGet.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\SysCtl_RotorGet.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_RotorGet.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysCtl_RotorGet.h</FilePath>
            </File>
            <File>
              <FileName>SysCtl_SysMoore.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Motor\SysCtl_SysMoore.c</FilePath>
            </File>
            <File>
              <FileName>SysCtl_SysMoore.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysCtl_SysMoore.h</FilePath>
            </File>
            <File>
              <FileName>SysVoltBase.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Motor\SysVoltBase.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>anoptv8</GroupName>
          <Files>
            <File>
              <FileName>AnoPTv8.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Cmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8Cmd.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Cmd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8Cmd.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8FrameFactory.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8FrameFactory.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8FrameFactory.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8FrameFactory.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Par.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8Par.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Par.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8Par.h</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Run.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8Run.c</FilePath>
            </File>
            <File>
              <FileName>AnoPTv8Run.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\AnoPTv8Run.h</FilePath>
            </File>
            <File>
              <FileName>HWInterface.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\HWInterface.c</FilePath>
            </File>
            <File>
              <FileName>HWInterface.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\HWInterface.h</FilePath>
            </File>
            <File>
              <FileName>MotorCmd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\MotorCmd.c</FilePath>
            </File>
            <File>
              <FileName>MotorCmd.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\MotorCmd.h</FilePath>
            </File>
            <File>
              <FileName>MotorData.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\MotorData.c</FilePath>
            </File>
            <File>
              <FileName>MotorData.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\MotorData.h</FilePath>
            </File>
            <File>
              <FileName>MotorParams.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\AnoPTv8\MotorParams.c</FilePath>
            </File>
            <File>
              <FileName>MotorParams.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\AnoPTv8\MotorParams.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>drive</GroupName>
          <Files>
            <File>
              <FileName>ad2s1212_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Drive\ad2s1212_spi.c</FilePath>
            </File>
            <File>
              <FileName>ad2s1212_spi.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Drive\ad2s1212_spi.h</FilePath>
            </File>
            <File>
              <FileName>ENC_Speed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Drive\ENC_Speed.c</FilePath>
            </File>
            <File>
              <FileName>ENC_Speed.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Drive\ENC_Speed.h</FilePath>
            </File>
            <File>
              <FileName>adc_pmsm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Drive\adc_pmsm.c</FilePath>
            </File>
            <File>
              <FileName>adc_pmsm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Drive\adc_pmsm.h</FilePath>
            </File>
            <File>
              <FileName>PT1000_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Drive\PT1000_table.c</FilePath>
            </File>
            <File>
              <FileName>PT1000_table.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Drive\PT1000_table.h</FilePath>
            </File>
            <File>
              <FileName>sensor_proc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Drive\sensor_proc.c</FilePath>
            </File>
            <File>
              <FileName>sensor_proc.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Drive\sensor_proc.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>sys</GroupName>
          <Files>
            <File>
              <FileName>Sys_TimerEvent.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\System\Sys_TimerEvent.c</FilePath>
            </File>
            <File>
              <FileName>Sys_TimerEvent.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\System\Sys_TimerEvent.h</FilePath>
            </File>
            <File>
              <FileName>low_speed_alarm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\System\low_speed_alarm.c</FilePath>
            </File>
            <File>
              <FileName>low_speed_alarm.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\System\low_speed_alarm.h</FilePath>
            </File>
            <File>
              <FileName>protection_monitor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\System\protection_monitor.c</FilePath>
            </File>
            <File>
              <FileName>protection_monitor.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\System\protection_monitor.h</FilePath>
            </File>
            <File>
              <FileName>system_status.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\System\system_status.h</FilePath>
            </File>
            <File>
              <FileName>power_stage_test.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\System\power_stage_test.c</FilePath>
            </File>
            <File>
              <FileName>power_stage_test.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\System\power_stage_test.h</FilePath>
            </File>
            <File>
              <FileName>self_test_impl.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\System\self_test_impl.c</FilePath>
            </File>
            <File>
              <FileName>self_test_impl.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\System\self_test_impl.h</FilePath>
            </File>
            <File>
              <FileName>self_test_manager.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\System\self_test_manager.c</FilePath>
            </File>
            <File>
              <FileName>self_test_manager.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\System\self_test_manager.h</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

  <LayerInfo>
    <Layers>
      <Layer>
        <LayName>AT32A403ARGT7_154_33</LayName>
        <LayTarg>0</LayTarg>
        <LayPrjMark>1</LayPrjMark>
      </Layer>
    </Layers>
  </LayerInfo>

</Project>
